# Generated by Django 3.1.7 on 2021-03-04 07:03

from django.db import migrations, models
import django.utils.timezone
import model_utils.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name='FacebookCAPIPixel',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'created',
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name='created',
                    ),
                ),
                (
                    'modified',
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name='modified',
                    ),
                ),
                ('ec_id', models.PositiveIntegerField(db_index=True)),
                (
                    'pixel_name',
                    models.CharField(max_length=120, verbose_name='Pixel Name'),
                ),
                ('pixel_id', models.Char<PERSON>ield(max_length=20, verbose_name='Pixel ID')),
                (
                    'pixel_token',
                    models.CharField(max_length=255, verbose_name='Pixel Token'),
                ),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
