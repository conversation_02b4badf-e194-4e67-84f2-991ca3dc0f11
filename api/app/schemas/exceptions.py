from pydantic import PydanticValueError


class TagtooEventInvalidPropertiesError(PydanticValueError):
    code = 'tagtoo_event_missing_required_properties'
    msg_template = (
        'Invalid properties for event "{event}" => \n'
        'Required: {required_props_string} \n'
        'Optional: {optional_props_string} \n'
        'Get: {input_props_string} \n'
    )

    no_property_string = '(no property)'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.required_props_string = ' or '.join(
            str(sorted(rule.required)) if rule.required else self.no_property_string
            for rule in kwargs['event_prop_rules']
        )
        self.optional_props_string = ' or '.join(
            str(sorted(rule.optional)) if rule.optional else self.no_property_string
            for rule in kwargs['event_prop_rules']
        )
        self.input_props_string = (
            sorted(kwargs['input_props']) or self.no_property_string
        )
