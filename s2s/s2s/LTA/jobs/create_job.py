import os
import datetime
import uuid

from ... import settings


def create_loader_job(
    filename: str,
    ec_id: int,
    version: str,
    mode: str = settings.MODE,
    image: str = settings.LTA_FACEBOOK_DATA_LOADER_IMAGE,
):
    import yaml
    from kubernetes.client import BatchV1Api
    from kubernetes.config import load_incluster_config

    with open(
        os.path.join(os.path.dirname(__file__), 'k8s_job_load_lta_data.yaml')
    ) as f:
        cfg = yaml.safe_load(f)

    # 使用更易讀的時間格式，同時添加隨機 UUID 後綴以避免名稱衝突
    timestamp = datetime.datetime.now().strftime('%Y%m%d-%H%M%S')
    short_uuid = str(uuid.uuid4())[:8]  # 使用 UUID 的前 8 個字符作為後綴
    cfg['metadata']['name'] = f'load-lta-data-{timestamp}-{short_uuid}'
    cfg['metadata']['annotations'] = {'filename': filename, 'ec_id': str(ec_id)}
    cfg['spec']['template']['spec']['containers'][0]['image'] = image
    cfg['spec']['template']['spec']['containers'][0]['args'] = [
        'python',
        'load_lta_data.py',
        f'--filename={filename}',
        f'--ecid={ec_id}',
        f'--version={version}',
    ]

    load_incluster_config()
    batch = BatchV1Api()
    job = batch.create_namespaced_job(namespace='default', body=cfg)
    return job


def create_loader_job_mock(
    filename: str,
    ec_id: int,
    version: str,
    mode: str = settings.MODE,
    image: str = settings.LTA_FACEBOOK_DATA_LOADER_IMAGE,
):
    # For local development and CI, run data loader process directly within
    # the same container
    from ..loader import DataLoader

    loader = DataLoader()
    loader.process(
        file_name=filename,
        ec_id=ec_id,
        version=version,
    )
