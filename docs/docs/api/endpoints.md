## Authentication

The access token is used in the `X-TOKEN` header of the request.

## API V1

### `POST` /event/v1

Publish an event. Make the data to be effective for all available channels.

| Request Body |
|---|
| [Event](/api/objectives#event) |

!!! warning
    In order to adapt the JavaScript **Beacon API** requests. The auth token is resolved by the parameter 
    `token` in the request body not from the request header `X-TOKEN`.

### `GET` /permanent

Exchange an identical permanent ID by combine the fingerprint and the IP address.

| Parameter | Description |
|---|---|
| fp(string) | user ID generated by [FingerPrintJS](https://github.com/fingerprintjs/fingerprintjs)