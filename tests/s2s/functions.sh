#!/bin/bash

error() { echo -e "\e[91m$1\e[m"; exit 1; }
success() { echo -e "\e[92m$1\e[m"; }

function init_config_data() {
docker exec -i config-api python manage.py shell << END
from apps.event_s2s.models import FacebookCAPIPixel
FacebookCAPIPixel.objects.all().delete()
FacebookCAPIPixel.objects.create(
  ec_id=$1,
  pixel_name="tagtootest",
  pixel_id="2169275333130765",
  pixel_token="EAAF2w0Wwk0YBABniYMgVXqRMh291bhK6hScmnfMvZAie1FcqdGoQFhOy0f8KnzWzW9UiFzzO8VjL3j7vNQDC9z2PZCKHXOej6jCjHrLwL7nJrHbOVubzKZAEDRI4sjvhDQTePXzko4uMmFmdaC5MSiGwliEkZCyqtSTInKaE2vFIPihuFNCd"
)
END
}

function update_s2s_db() {
docker exec -i s2s python update_db.py 1
}

function purge_subscription() {
docker exec -i s2s python << END
import sys
from datetime import datetime
from google.cloud import pubsub_v1
subscriber = pubsub_v1.SubscriberClient()
try:
  subscriber.seek(request={'subscription': '$1', 'time': datetime.now()})
  sys.exit(0)
except Exception as exc:
  print(exc)
  sys.exit(-1)
END
}

function publish_test_data() {
docker exec -i s2s python << END
import sys
import json
import time
from google.cloud import pubsub_v1
publisher = pubsub_v1.PublisherClient()
try:
  with open('$1', 'r') as file:
    data = json.load(file)
  # patch event_time field, prevent error "事件時間戳記太久遠"
  for item in data:
    item.update({'event_time': int(time.time())})
  future = publisher.publish('$2', json.dumps(data, indent=2).encode('utf-8'), version='$3', ec_id='$4')
  future.result()
  sys.exit(0)
except Exception as exc:
  print(exc)
  sys.exit(-1)
END
}

function check_subscription_message_count() {
docker exec -i s2s python << END
import sys
from datetime import datetime
from google.cloud import pubsub_v1
subscriber = pubsub_v1.SubscriberClient()
try:
  res = subscriber.pull(request={'subscription': '$1', 'max_messages': 100}, timeout=3)
  count = len(res.received_messages)
  assert count == $2, f'{count} != $2'
  sys.exit(0)
except Exception as exc:
  print(exc)
  sys.exit(-1)
END
}

function publish_lta_test_data {
docker exec -i s2s python << END
import sys
import json
import time
from google.cloud import pubsub_v1
publisher = pubsub_v1.PublisherClient()
try:
  future = publisher.publish(
    '$2',
    b'',
    file_name='$1',
    version='$3',
    ec_id='$4',
  )
  future.result()
  sys.exit(0)
except Exception as exc:
  print(exc)
  sys.exit(-1)
END
}