# https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/compute_network
resource "google_compute_network" "event" {
  description             = "Network for event services."
  name                    = "event-${var.environment}"
  routing_mode            = "GLOBAL"
  auto_create_subnetworks = false
  depends_on              = [google_project_service.gcp_services]
}

resource "google_compute_subnetwork" "asia_east1" {
  name             = "event-asia-east1-${var.environment}"
  ip_cidr_range    = "**********/20"
  region           = "asia-east1"
  network          = google_compute_network.event.id
  stack_type       = "IPV4_IPV6" # Enable dual-stack networking
  ipv6_access_type = "EXTERNAL"
}

# https://www.terraform.io/docs/providers/google/r/compute_firewall.html
resource "google_compute_firewall" "allow_icmp" {
  name    = "${google_compute_network.event.name}-allow-icmp"
  network = google_compute_network.event.name
  allow {
    protocol = "icmp"
  }
  source_ranges = ["0.0.0.0/0"]
  priority      = 65534
}
resource "google_compute_firewall" "allow_http" {
  name    = "${google_compute_network.event.name}-allow-http"
  network = google_compute_network.event.name
  allow {
    protocol = "tcp"
    ports    = ["80", "443"]
  }
  source_ranges = ["0.0.0.0/0"]
  priority      = 65534
}
resource "google_compute_firewall" "allow_ssh" {
  name    = "${google_compute_network.event.name}-allow-ssh"
  network = google_compute_network.event.name
  allow {
    protocol = "tcp"
    ports    = ["22"]
  }
  source_ranges = ["0.0.0.0/0"]
  priority      = 65534
}
