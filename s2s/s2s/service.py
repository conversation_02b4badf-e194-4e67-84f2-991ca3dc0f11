from . import settings
from .enums import Mode

__all__ = [
    'ErrorReportingService',
    'GoogleStorageService',
]


if settings.MODE == Mode.PROD:
    from shared.cloud_storage import GoogleStorageService
    from shared.error_report import ErrorReportingService
elif settings.MODE == Mode.DEV:
    from shared.cloud_storage import GoogleStorageService
    from shared.error_report import MockErrorReportingService as ErrorReportingService
else:
    from shared.cloud_storage import MockGoogleStorageService as GoogleStorageService
    from shared.error_report import MockErrorReportingService as ErrorReportingService

__all__ = [
    'ErrorReportingService',
    'GoogleStorageService',
]


class LTAErrorReportingService(ErrorReportingService):
    project = settings.GCP_PROJECT_ID
    credentials = None  # Use VM service account
    service = 'event-s2s/LTA'

    def report_error(self, error: Exception, **kwargs) -> None:
        expected_error = {
            'LtaExceptions': (
                f'Message version: {kwargs.get("version")}, '
                f'File_name: {kwargs.get("file_name")}, Error: {error}'
            )
        }
        if message := expected_error.get(error.__class__.__name__):
            self.report(message)
        else:
            self.report_exception()
