from app.config import settings

from .event_config import EventConfigService
from .pubsub import GoogleCloudPubSubPublisher

if settings.DEBUG:
    from .mocks import ErrorReportingClientMock as ErrorReportingClient
else:
    from .error_reporting import ErrorReportingClient

error_reporting_client = ErrorReportingClient()
pubsub_client = GoogleCloudPubSubPublisher()
event_config_client = EventConfigService()


__all__ = ['pubsub_client', 'error_reporting_client', 'event_config_client']
