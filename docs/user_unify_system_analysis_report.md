# User Unify 系統技術分析報告

**報告日期：** 2025年9月10日  
**分析範圍：** Tagtoo User Unify 系統完整技術架構  
**分析師：** AI Assistant  
**版本：** v2.0

---

## 📋 執行摘要

User Unify 是 Tagtoo 追蹤系統中負責**使用者身份統一和識別**的核心組件。系統透過分析使用者的多種標識符（email、手機號碼、permanent ID 等），識別並關聯屬於同一使用者的不同身份，建立完整的使用者畫像。目前使用 V2 版本，基於 BigQuery 實現，每日透過 cron job 執行處理流程。

### 關鍵特性
- ✅ **高效能大數據處理**：基於 BigQuery 的分散式運算
- ✅ **智慧身份統一**：使用 Union Find 演算法處理複雜關聯
- ✅ **強健的錯誤恢復**：完整的快照和回滾機制
- ✅ **可擴展架構**：支援大規模使用者資料處理

---

## 🏗️ 系統架構分析

### 1. 整體架構設計

```mermaid
graph TD
    A[Tagtoo Event 表] --> B[每日分組結果生成]
    B --> C[新實體合併]
    C --> D[候選群組預測]
    D --> E[Union Find 演算法]
    E --> F[中間結果表]
    F --> G[解析候選群組合併]
    G --> H[無用群組清理]
    H --> I[user_unify_groups 表]
    I --> J[user_unify_group_permanent 表]
    
    K[快照機制] --> I
    L[回滾機制] --> I
```

### 2. 核心模組架構

#### 2.1 主要 Python 模組

<augment_code_snippet path="user_unify/user_unify/process.py" mode="EXCERPT">
```python
def determine_groups(
    event_date: date,
    client: bigquery.Client,
    dataset: str,
    grouping_result_dataset: str,
    recalc_daily_grouping_result: bool = False,
):
    '''The main process of candidate groups determination

    Steps:
    * Calculate the daily grouping result from event data
    * Merge new entities into existed grouping result table
    * Predict the final group for groups with multiple candidates (Union Find)
    * Write the result into an intermediate table
    * Merge the above result into user_unify_groups
    * Delete the useless rows
    '''
```
</augment_code_snippet>

#### 2.2 設定管理

<augment_code_snippet path="user_unify/user_unify/settings.py" mode="EXCERPT">
```python
GCP_PROJECT = env.str('GCP_PROJECT', 'tagtoo-tracking')
BQ_DATASET = env.str('BQ_DATASET')
BQ_GROUPING_RESULT_DATASET = env.str('BQ_GROUPING_RESULT_DATASET')
BQ_SNAPSHOT_DATASET = env.str('BQ_SNAPSHOT_DATASET')
BQ_TABLE_USER_UNIFY_GROUPS = env.str('BQ_TABLE_USER_UNIFY_GROUPS')
```
</augment_code_snippet>

### 3. 資料流程和處理邏輯

#### 3.1 主要處理步驟

1. **每日分組結果生成**
   - 從 `tagtoo_event` 表提取當日使用者資料
   - 根據相同特徵將使用者聚集成群組
   - 產生 Daily Group Result

2. **新實體合併**
   - 將 Daily Group Result 合併到持續維護的 Group Result
   - 處理新群組和既有群組的更新邏輯

3. **候選群組預測**
   - 分析每個 permanent ID 可能隸屬的群組
   - 識別一對多的複雜關聯情況

4. **Union Find 演算法處理**
   - 解決一個 permanent 隸屬多個群組的衝突
   - 確定最終的群組歸屬

5. **結果合併和清理**
   - 更新最終的群組結果
   - 清理無用的重複群組

#### 3.2 Union Find 演算法實作

<augment_code_snippet path="user_unify/user_unify/utils.py" mode="EXCERPT">
```python
def resolve_multiple_candidates(data: List[dict]) -> dict:
    root = {}

    def find(node):
        root.setdefault(node, node)
        if root[node] != node:
            root[node] = find(root[node])
        return root[node]

    def union(x, y):
        parent_x = find(x)
        parent_y = find(y)
        if parent_x < parent_y:
            root[parent_y] = parent_x
        elif parent_x > parent_y:
            root[parent_x] = parent_y
```
</augment_code_snippet>

---

## 🗄️ 資料庫架構分析

### 1. 核心資料表結構

#### 1.1 user_unify_groups 表

| 欄位名 | 類型 | 描述 |
|--------|------|------|
| group_id | STRING | 群組唯一標識符 |
| group_time | TIMESTAMP | 群組創建時間 |
| permanents | ARRAY<STRING> | 屬於該群組的所有 permanent ID |
| emails | ARRAY<STRING> | 該群組的所有電子郵件地址 |
| phones | ARRAY<STRING> | 該群組的所有電話號碼 |
| usernames | ARRAY<STRING> | 該群組的所有使用者名稱 |
| gids | ARRAY<STRING> | 該群組的所有 GID |

#### 1.2 資料表關聯

```mermaid
erDiagram
    tagtoo_event ||--o{ daily_grouping_result : generates
    daily_grouping_result ||--o{ user_unify_groups : merges_into
    user_unify_groups ||--o{ user_unify_group_permanent : creates
    user_unify_groups ||--o{ snapshot_tables : backs_up
```

### 2. BigQuery SQL 查詢邏輯

#### 2.1 新實體合併查詢

<augment_code_snippet path="user_unify/user_unify/bigquery/sql/merge_new_entities.sql" mode="EXCERPT">
```sql
MERGE `$groups_table` T
USING (
    WITH contact_res AS (
        SELECT * FROM `$grouping_result_table`
    ),
    DISTINST_res AS (
        SELECT
            group_id,
            group_time,
            ARRAY(SELECT DISTINCT p FROM contact_res.permanents p) permanents,
            ARRAY(SELECT DISTINCT em FROM contact_res.emails em) emails,
            ARRAY(SELECT DISTINCT ph FROM contact_res.phones ph) phones,
            ARRAY(SELECT DISTINCT un FROM contact_res.usernames un) usernames,
            ARRAY(SELECT DISTINCT gid FROM contact_res.gids gid) gids
        FROM contact_res
    )
```
</augment_code_snippet>

#### 2.2 解析候選群組合併

<augment_code_snippet path="user_unify/user_unify/bigquery/sql/merge_resolved_candidates.sql" mode="EXCERPT">
```sql
MERGE `$groups_table` T
USING (
    WITH Attr_contact AS (
        SELECT predict_result, T.* 
        FROM `$intermediate_table` I
        JOIN `$groups_table` T ON I.group_id = T.group_id
    ),
    Distinct_res AS (
        SELECT
            predict_result group_id,
            ARRAY(SELECT DISTINCT p FROM Attr_contact.permanents p) permanents,
            ARRAY(SELECT DISTINCT em FROM Attr_contact.emails em) emails
        FROM Attr_contact
    )
```
</augment_code_snippet>

---

## 🔧 技術實作細節

### 1. 使用的技術棧

#### 1.1 核心技術
- **Python 3.8**：主要程式語言
- **Google BigQuery**：大數據處理和儲存
- **Docker**：容器化部署
- **Google Cloud Platform**：雲端基礎設施

#### 1.2 第三方套件
- `google-cloud-bigquery`：BigQuery 客戶端
- `environs`：環境變數管理
- `pytz`：時區處理

#### 1.3 Docker 配置

<augment_code_snippet path="user_unify/Dockerfile" mode="EXCERPT">
```dockerfile
FROM --platform=linux/amd64 python:3.8-slim

ENV PYTHONFAULTHANDLER=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONHASHSEED=random \
    PIP_NO_CACHE_DIR=off \
    PIP_DISABLE_PIP_VERSION_CHECK=on

WORKDIR /app
COPY user_unify/requirements.txt .
RUN pip install --upgrade pip && \
    pip install -r requirements.txt
```
</augment_code_snippet>

### 2. 錯誤處理和日誌記錄

#### 2.1 錯誤報告服務

<augment_code_snippet path="user_unify/user_unify/service.py" mode="EXCERPT">
```python
class UserUnifyErrorReportingService(ErrorReportingService):
    project = settings.GCP_PROJECT
    credentials = None  # Use VM service account
    service = 'event-user-unify'

error_reporting_service = UserUnifyErrorReportingService('v1')
```
</augment_code_snippet>

#### 2.2 快照和回滾機制

<augment_code_snippet path="user_unify/user_unify/process.py" mode="EXCERPT">
```python
def rollback_user_unify_groups_table(
    event_date: date,
    client: bigquery.Client,
    dataset: str,
    snapshot_dataset: str,
):
    rollback_filepath = os.path.join(SQL_PATH, 'rollback_user_unify_groups.sql')
    snapshot_table = (
        f'{snapshot_dataset}.user_unify_groups_{event_date.strftime("%Y_%m_%d")}'
    )
```
</augment_code_snippet>

### 3. 效能最佳化策略

#### 3.1 BigQuery 查詢最佳化
- **分區表設計**：按日期分區提升查詢效能
- **陣列欄位去重**：使用 `ARRAY(SELECT DISTINCT)` 減少資料重複
- **批次處理**：每日批次處理而非即時處理

#### 3.2 記憶體最佳化
- **分段處理**：大型資料集分段載入和處理
- **中間表機制**：使用臨時表避免記憶體溢出

---

## 💼 業務邏輯理解

### 1. User Unify 核心功能

#### 1.1 使用者身份統一
- **多重標識符關聯**：email、phone、username、permanent ID、GID
- **跨裝置識別**：透過共同標識符連結不同裝置的使用者行為
- **歷史資料整合**：持續累積和更新使用者身份資訊

#### 1.2 群組管理機制
- **動態群組更新**：每日增量更新群組資訊
- **衝突解決**：使用 Union Find 演算法處理複雜的群組關聯
- **資料一致性**：確保群組資料的完整性和準確性

### 2. 與追蹤系統的關聯

#### 2.1 資料來源
- **主要來源**：`tagtoo_event` 表中的使用者行為資料
- **標識符提取**：從事件資料中提取各種使用者標識符
- **即時更新**：每日處理新的使用者資料

#### 2.2 下游應用
- **廣告歸因**：提供準確的使用者身份用於廣告效果分析
- **個人化推薦**：支援跨裝置的個人化內容推薦
- **使用者分析**：提供完整的使用者行為分析基礎

---

## 🔄 系統工作流程

### 1. 每日處理流程

<augment_code_snippet path="user_unify/update_grouping_result.py" mode="EXCERPT">
```python
for date_ in date_range:
    process.generate_group_result_snapshot(
        event_date=date_,
        client=client,
        dataset=dataset,
        snapshot_dataset=snapshot_dataset,
    )
    try:
        result = process.determine_groups(
            event_date=date_,
            client=client,
            dataset=dataset,
            grouping_result_dataset=grouping_result_dataset,
            recalc_daily_grouping_result=args.recalc_daily_grouping_result,
        )
    except Exception as e:
        process.rollback_user_unify_groups_table(
            event_date=date_,
            client=client,
            dataset=dataset,
            snapshot_dataset=snapshot_dataset,
        )
```
</augment_code_snippet>

### 2. 命令列介面

#### 2.1 主要參數
- `-s, --start-date`：處理開始日期
- `-e, --end-date`：處理結束日期  
- `-r, --recalc-daily-grouping-result`：強制重新計算每日分組結果

#### 2.2 執行範例
```bash
python update_grouping_result.py -s 2025-09-01 -e 2025-09-10 -r
```

---

## 📊 系統監控和維護

### 1. 日誌記錄機制

```python
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s %(levelname)s] %(message)s',
)
logger = logging.getLogger(__file__)
```

### 2. 錯誤處理策略

#### 2.1 自動回滾
- 處理失敗時自動回滾到快照狀態
- 保證資料一致性和系統穩定性

#### 2.2 錯誤報告
- 整合 Google Cloud Error Reporting
- 自動收集和分析錯誤資訊

### 3. 效能監控指標

| 指標類型 | 監控項目 | 正常範圍 |
|---------|---------|---------|
| 處理時間 | 每日處理完成時間 | < 4 小時 |
| 資料量 | 每日處理記錄數 | 依業務量而定 |
| 錯誤率 | 處理失敗率 | < 1% |
| 資源使用 | BigQuery 查詢成本 | 監控預算 |

---

## 🎯 系統優勢與限制

### 優勢

1. **高可擴展性**
   - 基於 BigQuery 的雲端架構
   - 支援 PB 級資料處理

2. **高可靠性**
   - 完整的快照和回滾機制
   - 自動錯誤恢復

3. **高準確性**
   - Union Find 演算法確保群組關聯正確性
   - 多重標識符提升識別準確度

### 限制

1. **處理延遲**
   - 每日批次處理，非即時更新
   - 新使用者識別有 24 小時延遲

2. **成本考量**
   - BigQuery 查詢成本隨資料量增長
   - 需要持續監控和最佳化

3. **複雜性**
   - 系統邏輯複雜，維護成本較高
   - 需要專業技術人員維護

---

## 🔮 未來發展建議

### 短期改進（1-3 個月）

1. **效能最佳化**
   - 最佳化 BigQuery 查詢效能
   - 減少不必要的資料掃描

2. **監控增強**
   - 加入更詳細的效能監控
   - 建立自動化告警機制

### 中期發展（3-6 個月）

1. **即時處理能力**
   - 研究串流處理架構
   - 減少使用者識別延遲

2. **機器學習整合**
   - 使用 ML 模型提升識別準確度
   - 自動化參數調整

### 長期願景（6-12 個月）

1. **跨平台整合**
   - 整合更多資料來源
   - 建立統一的使用者身份平台

2. **隱私保護增強**
   - 符合 GDPR/CCPA 等法規要求
   - 實作資料匿名化機制

---

**報告完成日期：** 2025年9月10日  
**下次複查建議：** 2025年12月10日  
**負責團隊：** User Unify 開發團隊  
**批准狀態：** 待審核
