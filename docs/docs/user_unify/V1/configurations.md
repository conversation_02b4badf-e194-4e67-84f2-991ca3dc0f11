Use **environment variables** to config the application.

|Name|Description|
|---|---|
|TEST_MODE|Control the app behavior in test/local environment.|
|LOG_LEVEL|Log level of application, e.g. `DEBUG`, `INFO`|
|GCP_PROJECT_ID|The Project ID of GCP.|
|USER_UNIFY_STREAMING_SUBSCRIPTION_V1|Pub/Sub subscription name for user unify streaming.|
|USER_UNIFY_REWRITE_SUBSCRIPTION_V1|Pub/Sub subscription name for user unify rewrite.|
|BIGTABLE_USER_UNIFY_INSTANCE_ID|BigTable instance for user unify.|
|BIGTABLE_USER_UNIFY_ENTITY_TABLE_ID|Entity table name in user unify database.|
|BIGTABLE_USER_UNIFY_PERMANENT_TABLE_ID|Permanent table name in user unify database.|
|BIGTABLE_USER_UNIFY_EMAIL_TABLE_ID|Email table name in user unify database.|
|BIGTABLE_USER_UNIFY_MOBILE_TABLE_ID|Mobile table name in user unify database.|
|BIGTABLE_USER_UNIFY_USERNAME_TABLE_ID|Username table name in user unify database.|
|BIGTABLE_USER_UNIFY_FBP_TABLE_ID|Fbp table name in user unify database.|
|BIGTABLE_USER_UNIFY_GID_TABLE_ID|Gid table name in user unify database.|
|USER_UNIFY_HASH_GID|Hash the input Gid for a shorten value.|
|USER_UNIFY_HASH_FBP|Hash the input Fbp for a shorten value.|