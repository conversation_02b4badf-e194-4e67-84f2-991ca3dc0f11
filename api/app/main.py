import logging

from fastapi import Depends, FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.services import error_reporting_client

from .config import init_logging, settings
from .routers import auth, healthcheck, track

# from starlette.exceptions import HTTPException as StarletteHTTPException

logger = logging.getLogger(__file__)

# Initializing logging
init_logging()


async def error_reporting_to_gcp():
    try:
        yield
    except Exception as e:
        error_reporting_client.report(e)


# Initializing Fastapi server
# - Disable docs URL here. The docs URLs are created at /app/routers/auth.py for
#   issueing a basic authentication when accessing docs URL.
app = FastAPI(
    dependencies=[Depends(error_reporting_to_gcp)],
    docs_url=None,
    redoc_url=None,
    openapi_url=None,
    debug=settings.DEBUG,
)


# Routers
app.include_router(track.router)
app.include_router(healthcheck.router)
app.include_router(auth.router)

# Middlewares
app.add_middleware(
    CORSMiddleware,
    allow_origins=['*'],
    allow_methods=['GET', 'POST', 'OPTIONS'],
    allow_headers=[settings.API_TOKEN_HEADER],
)
