"""
BigQuery 雙寫入架構指標收集器

此模組負責收集和報告雙寫入架構的關鍵效能指標，
包括吞吐量、延遲、錯誤率和資源使用情況。
"""

import time
import logging
from typing import Dict, Any, Optional
from google.cloud import monitoring_v3
from google.cloud.monitoring_v3 import TimeSeries, Point
import threading
from collections import defaultdict, deque
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class DualWriteMetricsCollector:
    """雙寫入架構指標收集器"""
    
    def __init__(self, project_id: str, enable_cloud_monitoring: bool = True):
        """
        初始化指標收集器
        
        Args:
            project_id: GCP 專案 ID
            enable_cloud_monitoring: 是否啟用 Cloud Monitoring
        """
        self.project_id = project_id
        self.enable_cloud_monitoring = enable_cloud_monitoring
        self.project_name = f"projects/{project_id}"
        
        # 初始化 Cloud Monitoring 客戶端
        if self.enable_cloud_monitoring:
            try:
                self.client = monitoring_v3.MetricServiceClient()
                logger.info("Cloud Monitoring 客戶端初始化成功")
            except Exception as e:
                logger.error(f"Cloud Monitoring 客戶端初始化失敗: {e}")
                self.enable_cloud_monitoring = False
        
        # 本地指標緩存
        self._metrics_cache = defaultdict(deque)
        self._lock = threading.Lock()
        
        # 指標統計
        self._stats = {
            'dual_write_count': 0,
            'dual_write_success': 0,
            'dual_write_errors': 0,
            'transformation_success': 0,
            'transformation_errors': 0,
            'latency_samples': deque(maxlen=1000),
            'raw_json_sizes': deque(maxlen=1000)
        }
    
    def record_dual_write_latency(self, latency_ms: float, success: bool = True):
        """
        記錄雙寫入延遲
        
        Args:
            latency_ms: 延遲時間（毫秒）
            success: 是否成功
        """
        with self._lock:
            self._stats['dual_write_count'] += 1
            if success:
                self._stats['dual_write_success'] += 1
            else:
                self._stats['dual_write_errors'] += 1
            
            self._stats['latency_samples'].append({
                'timestamp': time.time(),
                'latency_ms': latency_ms,
                'success': success
            })
        
        # 發送到 Cloud Monitoring
        if self.enable_cloud_monitoring:
            self._send_latency_metric(latency_ms, success)
    
    def record_transformation_result(self, success: bool, error_type: Optional[str] = None):
        """
        記錄資料轉換結果
        
        Args:
            success: 轉換是否成功
            error_type: 錯誤類型（如果失敗）
        """
        with self._lock:
            if success:
                self._stats['transformation_success'] += 1
            else:
                self._stats['transformation_errors'] += 1
        
        # 發送到 Cloud Monitoring
        if self.enable_cloud_monitoring:
            self._send_transformation_metric(success, error_type)
    
    def record_raw_json_size(self, size_bytes: int):
        """
        記錄 raw_json 大小
        
        Args:
            size_bytes: JSON 大小（位元組）
        """
        with self._lock:
            self._stats['raw_json_sizes'].append({
                'timestamp': time.time(),
                'size_bytes': size_bytes
            })
        
        # 發送到 Cloud Monitoring
        if self.enable_cloud_monitoring:
            self._send_size_metric(size_bytes)
    
    def get_current_stats(self) -> Dict[str, Any]:
        """
        獲取當前統計資料
        
        Returns:
            包含各項指標的字典
        """
        with self._lock:
            # 計算最近 5 分鐘的統計
            now = time.time()
            five_min_ago = now - 300  # 5 分鐘
            
            recent_latencies = [
                sample for sample in self._stats['latency_samples']
                if sample['timestamp'] > five_min_ago
            ]
            
            recent_sizes = [
                sample for sample in self._stats['raw_json_sizes']
                if sample['timestamp'] > five_min_ago
            ]
            
            # 計算統計值
            stats = {
                'total_dual_writes': self._stats['dual_write_count'],
                'total_successes': self._stats['dual_write_success'],
                'total_errors': self._stats['dual_write_errors'],
                'error_rate': (
                    self._stats['dual_write_errors'] / max(self._stats['dual_write_count'], 1)
                ) * 100,
                'transformation_success_rate': (
                    self._stats['transformation_success'] / 
                    max(self._stats['transformation_success'] + self._stats['transformation_errors'], 1)
                ) * 100,
                'recent_throughput': len(recent_latencies) / 5,  # msg/min -> msg/s
            }
            
            # 延遲統計
            if recent_latencies:
                latencies = [s['latency_ms'] for s in recent_latencies]
                latencies.sort()
                stats.update({
                    'latency_p50': latencies[len(latencies) // 2],
                    'latency_p95': latencies[int(len(latencies) * 0.95)],
                    'latency_avg': sum(latencies) / len(latencies),
                    'latency_max': max(latencies)
                })
            
            # 大小統計
            if recent_sizes:
                sizes = [s['size_bytes'] for s in recent_sizes]
                sizes.sort()
                stats.update({
                    'raw_json_size_p50': sizes[len(sizes) // 2],
                    'raw_json_size_p95': sizes[int(len(sizes) * 0.95)],
                    'raw_json_size_avg': sum(sizes) / len(sizes),
                    'raw_json_size_max': max(sizes)
                })
            
            return stats
    
    def _send_latency_metric(self, latency_ms: float, success: bool):
        """發送延遲指標到 Cloud Monitoring"""
        try:
            series = TimeSeries()
            series.metric.type = "custom.googleapis.com/bq_writer/dual_write_latency"
            series.metric.labels["success"] = str(success).lower()
            series.resource.type = "k8s_container"
            
            point = Point()
            point.value.double_value = latency_ms
            point.interval.end_time.seconds = int(time.time())
            series.points = [point]
            
            self.client.create_time_series(
                name=self.project_name,
                time_series=[series]
            )
        except Exception as e:
            logger.error(f"發送延遲指標失敗: {e}")
    
    def _send_transformation_metric(self, success: bool, error_type: Optional[str]):
        """發送轉換結果指標到 Cloud Monitoring"""
        try:
            series = TimeSeries()
            series.metric.type = "custom.googleapis.com/bq_writer/transformation_result"
            series.metric.labels["success"] = str(success).lower()
            if error_type:
                series.metric.labels["error_type"] = error_type
            series.resource.type = "k8s_container"
            
            point = Point()
            point.value.int64_value = 1
            point.interval.end_time.seconds = int(time.time())
            series.points = [point]
            
            self.client.create_time_series(
                name=self.project_name,
                time_series=[series]
            )
        except Exception as e:
            logger.error(f"發送轉換指標失敗: {e}")
    
    def _send_size_metric(self, size_bytes: int):
        """發送大小指標到 Cloud Monitoring"""
        try:
            series = TimeSeries()
            series.metric.type = "custom.googleapis.com/bq_writer/raw_json_size"
            series.resource.type = "k8s_container"
            
            point = Point()
            point.value.int64_value = size_bytes
            point.interval.end_time.seconds = int(time.time())
            series.points = [point]
            
            self.client.create_time_series(
                name=self.project_name,
                time_series=[series]
            )
        except Exception as e:
            logger.error(f"發送大小指標失敗: {e}")
    
    def log_performance_summary(self):
        """記錄效能摘要到日誌"""
        stats = self.get_current_stats()
        
        logger.info(
            f"雙寫入效能摘要: "
            f"吞吐量={stats.get('recent_throughput', 0):.2f} msg/s, "
            f"錯誤率={stats.get('error_rate', 0):.2f}%, "
            f"P95延遲={stats.get('latency_p95', 0):.2f}ms, "
            f"轉換成功率={stats.get('transformation_success_rate', 0):.2f}%"
        )


# 全域指標收集器實例
_metrics_collector: Optional[DualWriteMetricsCollector] = None


def get_metrics_collector() -> Optional[DualWriteMetricsCollector]:
    """獲取全域指標收集器實例"""
    return _metrics_collector


def initialize_metrics_collector(project_id: str, enable_cloud_monitoring: bool = True):
    """初始化全域指標收集器"""
    global _metrics_collector
    _metrics_collector = DualWriteMetricsCollector(project_id, enable_cloud_monitoring)
    logger.info("指標收集器初始化完成")


def record_dual_write_latency(latency_ms: float, success: bool = True):
    """記錄雙寫入延遲（便利函數）"""
    if _metrics_collector:
        _metrics_collector.record_dual_write_latency(latency_ms, success)


def record_transformation_result(success: bool, error_type: Optional[str] = None):
    """記錄轉換結果（便利函數）"""
    if _metrics_collector:
        _metrics_collector.record_transformation_result(success, error_type)


def record_raw_json_size(size_bytes: int):
    """記錄 raw_json 大小（便利函數）"""
    if _metrics_collector:
        _metrics_collector.record_raw_json_size(size_bytes)
