Use **environment variables** to config the application.

|Name|Description|
|---|---|
|TEST_MODE|Control the app behavior in test/local environment.|
|GCP_PROJECT_ID|The Project ID of GCP.|
|BIGQUERY_TAGTOO_EVENT_TABLE_ID|BigQuery table ID to stores Tagtoo channel events.|
|IP2LOCATION_DB_HOST|Host URI of IP2Location MySQL server.|
|**INTEGRATED_WRITE_ENABLED**|**Enable dual-write to integrated_event table (v2.1 tracking_id strategy). Default: false**|
|**INTEGRATED_WRITE_SAMPLE_RATE**|**Sample rate for integrated_event writes (0.0-1.0). Default: 0.0**|

## Resource Configuration Changes (v2.1 Dual-Write Architecture)

The dual-write architecture requires additional resources to handle the increased processing load:

### BQ Writer Subscriber Resources (+35%)
- **CPU**: 100m → 135m
- **Memory**: 100Mi → 135Mi
- **Ephemeral Storage**: 100Mi → 135Mi

### IP2Location Container Resources (+35%)
- **CPU**: 100m → 135m
- **Memory**: 400Mi → 540Mi
- **Ephemeral Storage**: 3Gi → 4Gi

These adjustments ensure optimal performance for the tracking_id core strategy while maintaining cost efficiency.
