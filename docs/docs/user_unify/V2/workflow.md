<script src="https://unpkg.com/mermaid@8.13.3/dist/mermaid.min.js"></script>

## Daily process work flow
```mermaid
flowchart TD
    A([Daily cron job triggered]) --> B[Check process date range]
    B --> C[Create Daily Grouping Reslut Snapshot]
    C --> D[Determine groups]
    D --> E{Execute} -- Success --> F[Update Grouping Reslut]
    E -- Error --> G[RollBack] --> Z([End])
    F --> H{Execute} -- Success --> I{Have othert days?}
    H -- Error --> G
    I -- Yes --> C
    I -- No --> Z
```

- 以上流程圖大致為 [update_grouping_result](./commands.md#update_grouping_result) 執行流程

## Determine groups & Update Grouping Reslut detail Flowchart
![architecture](./../../assets/img/UU_v2/UU_V2.jpg)

1. 執行 [merge_new_entities.sql](./BigQuery_SQL/UU_SQL.md#merge_new_entitiessql)，該 SQL 會使用 [get_grouping_result_by_date.sql](./BigQuery_SQL/table_functions.md#get_grouping_result_by_datesql)，從 Tagtoo event 撈出當天的使用者資訊後，將有相同特徵的使用者聚集成一個群組，產出 Daily Group Result，再將該結果 [Merge](https://cloud.google.com/bigquery/docs/reference/standard-sql/dml-syntax#merge_statement) 進持續維護的 Group Result，Merge 的規則如會依據 Daily Group Result 產出的 `group id` 是否有出現在 Group Result 來處理
    1. `group id` 有出現在 Group Result：將兩筆資料合併後更新
    2. `group id` 沒有出現在 Group Result：意即該群組是全新的群組，直接新增
2. 執行 [predict_candidate.sql](./BigQuery_SQL/UU_SQL.md#predict_candidatesql)，該 SQL 會使用 [get_entity_by_date.sql](./BigQuery_SQL/table_functions.md#get_entity_by_datesql)，從 Tagtoo event 撈出當天的使用者資訊，作為 Daily Entity Data，接著跟上一動被更新的 Group Result 做運算，找出每個 permanent 可以隸屬於哪些 `group id` 群組
    - 使用 Daily Entity Data 來運算，而不是用 Daily Group Result 來做運算是因為，Daily Group Result 跟 Group Result 運算時，各個特徵原本是 Array，比對時，就需要做展開，這樣會導致資料量大增，而使用原始資料的 Daily Entity Data 就能以較少的資料量，並有足夠的資訊做運算
3. 將 [predict_candidate.sql](./BigQuery_SQL/UU_SQL.md#predict_candidatesql) 計算出來的結果，改成使用 [Python Script](https://github.com/Tagtoo/ad-track/blob/master/user_unify/user_unify/utils.py) 執行 [Union Find](./algorithm.md#union-find)
    - 這部分主要是處理一個 permanent 隸屬多個 `group id` 群組的情況，在這種情況下，使用 SQL 語法處理會有些困難，因此選擇使用 Python 來處理，相關的案理可以參考 [ISSUE 240](https://github.com/Tagtoo/ad-track/issues/240)
4. 將 [Python Script](https://github.com/Tagtoo/ad-track/blob/master/user_unify/user_unify/utils.py) 計算後的結果，存入暫存 Table，接著先執行 [merge_resolved_candidates.sql](./BigQuery_SQL/UU_SQL.md#merge_resolved_candidatessql) 後，再執行 [delete_useless_group.sql](./BigQuery_SQL/UU_SQL.md#delete_useless_groupsql)，完成更新 Group Result


!!! notes
    - 關於流程相關的更祥資訊，可以參照 [ISSUE 221](https://github.com/Tagtoo/ad-track/issues/221) 以及 [ISSUE 240](https://github.com/Tagtoo/ad-track/issues/240) ，在這兩個 Issue 中，會有比較詳細的介紹此 Flowchart 這樣設計的前因後果
    - Download and modify [detail Flowchart](./../../assets/draw.io/UU_V2.xml) at [Draw.io](https://draw.io).