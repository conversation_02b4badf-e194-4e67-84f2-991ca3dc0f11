"""
智慧重試處理器

實施分類錯誤處理和智慧重試策略，避免無效重試，最佳化資源使用。
支援指數退避、線性退避和無重試策略。

版本: v2.1 (tracking_id 核心策略)
日期: 2025-08-18
"""

import asyncio
import logging
import time
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Callable
from enum import Enum

logger = logging.getLogger(__name__)


class ErrorType(Enum):
    """錯誤類型枚舉"""
    QUOTA_EXCEEDED = "quota_exceeded"
    NETWORK_ERROR = "network_error"
    SCHEMA_ERROR = "schema_error"
    PERMISSION_ERROR = "permission_error"
    UNKNOWN_ERROR = "unknown_error"


class RetryStrategy(ABC):
    """重試策略抽象基類"""

    @abstractmethod
    async def execute(self, operation: Callable, data: Dict[str, Any], max_retries: int) -> bool:
        """執行重試策略"""
        pass


class ExponentialBackoffStrategy(RetryStrategy):
    """指數退避重試策略"""

    def __init__(self, base_delay: float = 1.0, max_delay: float = 300.0, multiplier: float = 2.0):
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.multiplier = multiplier

    async def execute(self, operation: Callable, data: Dict[str, Any], max_retries: int) -> bool:
        """執行指數退避重試"""
        for attempt in range(max_retries):
            try:
                result = await operation(data)
                if result:
                    logger.debug(f"指數退避重試成功，嘗試次數: {attempt + 1}")
                    return True
            except Exception as e:
                logger.warning(f"指數退避重試失敗 (嘗試 {attempt + 1}/{max_retries}): {e}")

                if attempt < max_retries - 1:
                    delay = min(self.base_delay * (self.multiplier ** attempt), self.max_delay)
                    logger.debug(f"等待 {delay:.2f} 秒後重試")
                    await asyncio.sleep(delay)

        logger.error(f"指數退避重試最終失敗，已嘗試 {max_retries} 次")
        return False


class LinearBackoffStrategy(RetryStrategy):
    """線性退避重試策略"""

    def __init__(self, delay: float = 5.0):
        self.delay = delay

    async def execute(self, operation: Callable, data: Dict[str, Any], max_retries: int) -> bool:
        """執行線性退避重試"""
        for attempt in range(max_retries):
            try:
                result = await operation(data)
                if result:
                    logger.debug(f"線性退避重試成功，嘗試次數: {attempt + 1}")
                    return True
            except Exception as e:
                logger.warning(f"線性退避重試失敗 (嘗試 {attempt + 1}/{max_retries}): {e}")

                if attempt < max_retries - 1:
                    logger.debug(f"等待 {self.delay} 秒後重試")
                    await asyncio.sleep(self.delay)

        logger.error(f"線性退避重試最終失敗，已嘗試 {max_retries} 次")
        return False


class NoRetryStrategy(RetryStrategy):
    """無重試策略"""

    async def execute(self, operation: Callable, data: Dict[str, Any], max_retries: int) -> bool:
        """不執行重試，直接失敗"""
        logger.info("使用無重試策略，直接標記為失敗")
        return False


class IntelligentRetryHandler:
    """智慧重試處理器"""

    def __init__(self, max_immediate_retries: Optional[int] = None, max_delayed_retries: Optional[int] = None):
        """初始化重試處理器

        :param max_immediate_retries: 立即重試的最大次數（可選，預設取自環境變數或 3）
        :param max_delayed_retries: 延遲重試的最大次數（可選，預設取自環境變數或 10）
        """
        import os
        self.max_immediate_retries = (
            max_immediate_retries
            if max_immediate_retries is not None
            else int(os.environ.get("MAX_IMMEDIATE_RETRIES", 3))
        )
        self.max_delayed_retries = (
            max_delayed_retries
            if max_delayed_retries is not None
            else int(os.environ.get("MAX_DELAYED_RETRIES", 10))
        )

        # 不同錯誤類型的重試策略
        self.retry_strategies = {
            ErrorType.QUOTA_EXCEEDED: ExponentialBackoffStrategy(
                base_delay=60, max_delay=3600, multiplier=2.0
            ),
            ErrorType.NETWORK_ERROR: LinearBackoffStrategy(delay=5.0),
            ErrorType.SCHEMA_ERROR: NoRetryStrategy(),
            ErrorType.PERMISSION_ERROR: NoRetryStrategy(),
            ErrorType.UNKNOWN_ERROR: ExponentialBackoffStrategy(
                base_delay=2.0, max_delay=60.0, multiplier=1.5
            )
        }

        # 統計資訊
        self.stats = {
            "total_retries": 0,
            "successful_retries": 0,
            "failed_retries": 0,
            "error_type_counts": {error_type.value: 0 for error_type in ErrorType}
        }

        logger.info("IntelligentRetryHandler 初始化完成")

    async def write_with_retry(self, operation: Callable, data: Dict[str, Any],
                              message_id: str) -> bool:
        """
        帶重試的寫入邏輯

        Args:
            operation: 寫入操作函數
            data: 要寫入的資料
            message_id: 訊息 ID

        Returns:
            是否成功
        """
        self.stats["total_retries"] += 1

        # 立即重試 (3次)
        for attempt in range(self.max_immediate_retries):
            try:
                success = await operation(data)
                if success:
                    self.stats["successful_retries"] += 1
                    logger.debug(f"立即重試成功: {message_id}, 嘗試次數: {attempt + 1}")
                    return True

                # 短暫等待後重試
                if attempt < self.max_immediate_retries - 1:
                    await asyncio.sleep(0.1 * (attempt + 1))

            except Exception as e:
                error_type = self._classify_error(e)
                self.stats["error_type_counts"][error_type.value] += 1

                logger.warning(f"立即重試失敗 (嘗試 {attempt + 1}/{self.max_immediate_retries}): {e}")

                if error_type in [ErrorType.SCHEMA_ERROR, ErrorType.PERMISSION_ERROR]:
                    # 不可重試的錯誤
                    logger.error(f"不可重試的錯誤 {message_id}: {e}")
                    await self._send_to_dead_letter(data, message_id, str(e))
                    self.stats["failed_retries"] += 1
                    return False

                if attempt == self.max_immediate_retries - 1:
                    # 立即重試失敗，使用智慧重試策略
                    return await self._execute_smart_retry(operation, data, message_id, error_type)

        self.stats["failed_retries"] += 1
        return False

    async def _execute_smart_retry(self, operation: Callable, data: Dict[str, Any],
                                  message_id: str, error_type: ErrorType) -> bool:
        """
        執行智慧重試策略

        Args:
            operation: 寫入操作
            data: 資料
            message_id: 訊息 ID
            error_type: 錯誤類型

        Returns:
            是否成功
        """
        strategy = self.retry_strategies.get(error_type, self.retry_strategies[ErrorType.UNKNOWN_ERROR])

        logger.info(f"使用 {type(strategy).__name__} 處理 {error_type.value} 錯誤: {message_id}")

        try:
            success = await strategy.execute(operation, data, self.max_delayed_retries)
            if success:
                self.stats["successful_retries"] += 1
                logger.info(f"智慧重試成功: {message_id}")
            else:
                self.stats["failed_retries"] += 1
                logger.error(f"智慧重試最終失敗: {message_id}")
                await self._send_to_dead_letter(data, message_id, f"智慧重試失敗: {error_type.value}")

            return success

        except Exception as e:
            logger.error(f"智慧重試異常: {e}")
            self.stats["failed_retries"] += 1
            await self._send_to_dead_letter(data, message_id, f"重試異常: {str(e)}")
            return False

    def _classify_error(self, error: Exception) -> ErrorType:
        """
        分類錯誤類型

        Args:
            error: 異常物件

        Returns:
            錯誤類型
        """
        error_msg = str(error).lower()

        if any(keyword in error_msg for keyword in ['quota', 'rate limit', 'exceeded']):
            return ErrorType.QUOTA_EXCEEDED
        elif any(keyword in error_msg for keyword in ['network', 'timeout', 'connection']):
            return ErrorType.NETWORK_ERROR
        elif any(keyword in error_msg for keyword in ['schema', 'invalid', 'format', 'type']):
            return ErrorType.SCHEMA_ERROR
        elif any(keyword in error_msg for keyword in ['permission', 'forbidden', 'unauthorized']):
            return ErrorType.PERMISSION_ERROR
        else:
            return ErrorType.UNKNOWN_ERROR

    async def _send_to_dead_letter(self, data: Dict[str, Any], message_id: str, reason: str):
        """
        發送到死信佇列

        Args:
            data: 資料
            message_id: 訊息 ID
            reason: 失敗原因
        """
        try:
            dead_letter_record = {
                'message_id': message_id,
                'data': data,
                'failure_reason': reason,
                'failure_time': time.time(),
                'retry_count': self.max_immediate_retries + self.max_delayed_retries
            }

            # 這裡可以實作實際的死信佇列邏輯
            # 例如寫入特殊的 BigQuery 表格或發送到 Pub/Sub 死信主題
            logger.error(f"發送到死信佇列: {message_id}, 原因: {reason}")

        except Exception as e:
            logger.error(f"發送死信佇列失敗: {e}")

    def get_stats(self) -> Dict[str, Any]:
        """
        獲取統計資訊

        Returns:
            統計資訊
        """
        total = self.stats["total_retries"]
        success_rate = (self.stats["successful_retries"] / max(total, 1)) * 100

        return {
            **self.stats,
            "success_rate": success_rate,
            "failure_rate": 100 - success_rate
        }

    def reset_stats(self):
        """重置統計資訊"""
        self.stats = {
            "total_retries": 0,
            "successful_retries": 0,
            "failed_retries": 0,
            "error_type_counts": {error_type.value: 0 for error_type in ErrorType}
        }
        logger.info("重試統計資訊已重置")
