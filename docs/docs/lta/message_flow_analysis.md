# LTA-PROD Pub/Sub Topic 訊息處理流程完整分析

## 概述

`lta-prod` 是 Google Cloud Pub/Sub 的一個主題 (topic)，負責處理 LTA (Lookalike Targeting Audience) 數據處理請求。本文檔詳細分析了從訊息發布到最終處理完成的完整流程。

## 架構概覽

```mermaid
graph TD
    A[上游系統] --> B[lta-prod Topic]
    B --> C[lta-v1-prod Subscription]
    C --> D[s2s-lta-subscriber Pod]
    D --> E[LTAToFacebookCapi Class]
    E --> F[create_loader_job Function]
    F --> G[load-lta-data Kubernetes Job]
    G --> H[DataLoader.process]
    H --> I[GCS AVRO 檔案讀取]
    I --> J[像素數據組合]
    J --> K[LTAToFacebookCApiPublisher]
    K --> L[lta-facebook-capi-batch-prod Topic]
    L --> M[Facebook Conversion API]
```

## 詳細流程分析

### 1. Topic 和 Subscription 設定

#### Topic 配置
- **Topic 名稱**: `lta-prod` (在生產環境中的完整名稱)
- **Terraform 定義**: `deploy/pubsub.tf:23-29`
- **實際建立的 topic**: `lta-${var.environment}` (environment = prod)

#### Subscription 配置
- **Subscription 名稱**: `lta-v1-prod`
- **Terraform 定義**: `deploy/pubsub.tf:123-138`
- **過濾器**: 無版本過濾器（接收所有訊息）
- **消費者**: `event-s2s-lta-subscriber`

### 2. 訊息消費服務

#### s2s-lta-subscriber Deployment
- **檔案位置**: `deploy/s2s.tf` 中的 deployment 設定
- **執行命令**: `python subscribe.py lta --version=v1 --max-messages=1`
- **節點選擇**: `application=event` 和 `name=s2s-lta-subscriber`

#### 消費者程式
- **檔案**: `s2s/s2s/LTA/subscribers.py`
- **類別**: `LTAToFacebookCapi`
- **繼承**: `pubsub.BaseSubscriberClient`

### 3. 訊息處理流程

#### 3.1 訊息解析
訊息屬性包含：
- `ec_id`: 客戶 ID (整數)
- `file_name`: AVRO 檔案路徑 (字串)
- `version`: API 版本 (字串)

#### 3.2 Job 建立流程
```mermaid
sequenceDiagram
    participant Sub as s2s-lta-subscriber
    participant K8s as Kubernetes API
    participant Job as load-lta-data Job
    participant GCS as Google Cloud Storage
    participant FB as Facebook Conversion API
    
    Sub->>Sub: 解析 Pub/Sub 訊息
    Sub->>K8s: 建立 Kubernetes Job
    K8s->>Job: 啟動 Pod
    Job->>GCS: 讀取 AVRO 檔案
    GCS-->>Job: 返回用戶數據
    Job->>Job: 處理和組合像素數據
    Job->>FB: 發送數據到 Facebook API
    FB-->>Job: 返回處理結果
    Job->>Job: 記錄處理結果
```

### 4. Kubernetes Job 詳細處理

#### 4.1 Job 配置
- **模板檔案**: `s2s/s2s/LTA/jobs/k8s_job_load_lta_data.yaml`
- **命名格式**: `load-lta-data-{YYYYMMDD}-{HHMMSS}-{UUID}`
- **容器映像**: 從 `settings.LTA_FACEBOOK_DATA_LOADER_IMAGE` 取得
- **執行命令**: 
  ```bash
  python load_lta_data.py --filename={filename} --ecid={ec_id} --version={version}
  ```

#### 4.2 資源配置
- **CPU 請求**: 600m
- **CPU 限制**: 1 核心
- **記憶體請求**: 0.5Gi
- **記憶體限制**: 1Gi
- **重試次數**: 5 次
- **完成後保留時間**: 24 小時 (86400 秒)

#### 4.3 調度優化
- **Pod 反親和性**: 避免同類型 Job 在同一節點
- **拓撲分佈約束**: 確保 Job 均勻分佈
- **優先級類別**: `lta-job-priority`

### 5. 數據處理引擎

#### 5.1 DataLoader 類別
- **檔案**: `s2s/s2s/LTA/loader.py`
- **主要功能**: 讀取 AVRO 檔案，轉換數據，發送到 Facebook API

#### 5.2 處理步驟
1. **檔案讀取**: 使用 `LTAGoogleStorage` 從 GCS 讀取 AVRO 檔案
2. **數據轉換**: 將用戶數據轉換為 Facebook Conversion API 格式
3. **批次處理**: 
   - 預設批次大小: 10,000 筆記錄
   - 分塊大小: 1,000 筆記錄
   - 訊息大小限制: 8MB
4. **發送數據**: 透過 `LTAToFacebookCApiPublisher` 發送到下游 topic

### 6. 輸出和下游處理

#### 6.1 輸出 Topic
- **Topic 名稱**: `lta-facebook-capi-batch-prod`
- **發送者**: `LTAToFacebookCApiPublisher`
- **設定檔案**: `s2s/s2s/LTA/publishers.py`

#### 6.2 最終目的地
- **Facebook Conversion API**: 最終的數據目的地
- **批次處理**: 後續由其他服務處理發送到 Facebook

### 7. 錯誤處理和監控

#### 7.1 重試機制
- **預設重試週期**: 30 秒
- **最大重試次數**: 10 次
- **失敗佇列**: 使用 `deque` 管理失敗的訊息

#### 7.2 日誌級別
- **DEBUG**: 詳細處理信息
- **INFO**: 一般處理狀態
- **WARNING**: 重試和警告
- **ERROR**: 處理錯誤
- **CRITICAL**: 嚴重錯誤，需要立即關注

#### 7.3 CRITICAL 錯誤觸發條件
1. 無法從 GCS 讀取檔案
2. 處理像素數據時發生錯誤
3. 沒有成功發布任何訊息

### 8. 環境變數和設定

#### 8.1 關鍵環境變數
- `LTA_FACEBOOK_SUBSCRIPTION_V1`: 訂閱名稱 (生產環境: `lta`)
- `LTA_FACEBOOK_TOPIC`: 輸出 topic 名稱 (生產環境: `lta-fb`)
- `GCP_PROJECT_ID`: Google Cloud 專案 ID
- `MODE`: 執行模式 (prod/dev)

#### 8.2 設定檔案
- **環境變數**: `s2s/.env`
- **設定載入**: `s2s/s2s/settings.py`
- **Terraform**: `deploy/s2s.tf`

## 完整訊息處理流程時序圖

```mermaid
sequenceDiagram
    participant US as 上游系統
    participant LT as lta-prod Topic
    participant LS as lta-v1-prod Subscription
    participant Sub as s2s-lta-subscriber
    participant K8s as Kubernetes
    participant Job as load-lta-data Job
    participant GCS as Google Cloud Storage
    participant Pub as LTAToFacebookCApiPublisher
    participant FBT as lta-facebook-capi-batch Topic
    participant FB as Facebook Conversion API
    
    US->>LT: 發布訊息 (ec_id, file_name, version)
    LT->>LS: 路由訊息到訂閱
    LS->>Sub: 拉取訊息
    Sub->>Sub: 解析訊息屬性
    Sub->>K8s: 建立 load-lta-data Job
    Sub->>LS: ACK 訊息
    
    K8s->>Job: 啟動 Job Pod
    Job->>GCS: 讀取 AVRO 檔案
    GCS-->>Job: 返回用戶數據
    
    loop 批次處理
        Job->>Job: 轉換數據為像素格式
        Job->>Pub: 發送批次數據
        Pub->>FBT: 發布到 Facebook topic
        FBT-->>FB: 傳送到 Facebook API
    end
    
    Job->>Job: 記錄處理結果
    Job->>K8s: Job 完成
```

## 關鍵程式碼位置總結

| 組件 | 檔案位置 | 說明 |
|------|----------|------|
| Topic 定義 | `deploy/pubsub.tf:23-29` | lta topic 的 Terraform 設定 |
| Subscription 定義 | `deploy/pubsub.tf:123-138` | lta subscription 的 Terraform 設定 |
| 訊息消費者 | `s2s/s2s/LTA/subscribers.py` | LTAToFacebookCapi 類別 |
| Job 建立邏輯 | `s2s/s2s/LTA/jobs/create_job.py` | create_loader_job 函式 |
| Job 模板 | `s2s/s2s/LTA/jobs/k8s_job_load_lta_data.yaml` | Kubernetes Job 配置 |
| 數據處理器 | `s2s/s2s/LTA/loader.py` | DataLoader 類別 |
| Job 執行腳本 | `s2s/load_lta_data.py` | Job 的主要執行點 |
| 訊息發布者 | `s2s/s2s/LTA/publishers.py` | LTAToFacebookCApiPublisher 類別 |

## 效能考量

1. **批次處理**: 使用 10,000 筆記錄的批次大小來平衡處理效率和記憶體使用
2. **分塊處理**: 1,000 筆記錄的分塊避免訊息過大
3. **並發處理**: 透過 Kubernetes Job 實現水平擴展
4. **資源優化**: Pod 反親和性和拓撲分佈確保負載均衡
5. **重試機制**: 指數退避策略處理暫時性錯誤

這個完整的分析涵蓋了從 `lta-prod` topic 接收訊息到最終處理完成的所有步驟和組件。