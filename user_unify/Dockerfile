# Stage 1
FROM --platform=linux/amd64 python:3.8 as build-deps

COPY shared /shared

RUN python /shared/setup_error_report.py bdist_wheel

# Stage 2
FROM --platform=linux/amd64 python:3.8-slim

LABEL author="<PERSON> (<EMAIL>)"

# python envs
ENV PYTHONFAULTHANDLER=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONHASHSEED=random \
    PIP_NO_CACHE_DIR=off \
    PIP_DISABLE_PIP_VERSION_CHECK=on \
    PIP_DEFAULT_TIMEOUT=100

USER root

COPY --from=build-deps /dist /dist

WORKDIR /app

COPY user_unify/requirements.txt .

RUN pip install --upgrade pip && \
    pip install /dist/* && \
    pip install -r requirements.txt

COPY user_unify/. .
