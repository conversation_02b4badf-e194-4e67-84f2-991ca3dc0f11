#!/usr/bin/env bash

set -o errexit
set -o pipefail

cmd="$@"

function rabbitmq_ready(){
python << END
import sys
from s2s import amqp
try:
  with amqp.channel_scope() as channel:
    sys.exit(0)
except Exception as exc:
  sys.exit(-1)
END
}

function redis_ready() {
python << END
import sys
from redis import Redis
from s2s import settings
try:
  Redis(settings.REDIS_HOST).ping()
  sys.exit(0)
except Exception as exc:
  sys.exit(-1)
END
}

function pubsub_emulator_ready() {
  curl "$PUBSUB_EMULATOR_HOST" || exit 1
}

until rabbitmq_ready; do
  >&2 echo "RabbitMQ is unavailable - sleeping"
  sleep 1
done

>&2 echo "RabbitMQ is up - continuing..."

until redis_ready; do
  >&2 echo "Redis is unavailable - sleeping"
  sleep 1
done

>&2 echo "Redis is up - continuing..."

if [[ -n "$PUBSUB_EMULATOR_HOST" ]]; then

  echo "Run app against the Pub/Sub emulator"

  until pubsub_emulator_ready; do
    >&2 echo "Pub/Sub emulator is unavailable - sleeping"
    sleep 1
  done

  >&2 echo "Pub/Sub emulator is up - continuing..."
fi

exec $cmd