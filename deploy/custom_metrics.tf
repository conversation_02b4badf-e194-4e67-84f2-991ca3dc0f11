# 創建 custom-metrics namespace
resource "kubernetes_namespace" "custom_metrics" {
  metadata {
    name = "custom-metrics"
  }
}

# 創建 ServiceAccount
resource "kubernetes_service_account" "custom_metrics_stackdriver_adapter" {
  metadata {
    name      = "custom-metrics-stackdriver-adapter"
    namespace = kubernetes_namespace.custom_metrics.metadata[0].name
  }
}

# 創建 ClusterRoleBinding
resource "kubernetes_cluster_role_binding" "custom_metrics_stackdriver_adapter" {
  metadata {
    name = "custom-metrics-stackdriver-adapter"
  }
  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "ClusterRole"
    name      = "custom-metrics-server-resources"
  }
  subject {
    kind      = "ServiceAccount"
    name      = kubernetes_service_account.custom_metrics_stackdriver_adapter.metadata[0].name
    namespace = kubernetes_namespace.custom_metrics.metadata[0].name
  }
}

# 創建 Deployment
resource "kubernetes_deployment" "custom_metrics_stackdriver_adapter" {
  metadata {
    name      = "custom-metrics-stackdriver-adapter"
    namespace = kubernetes_namespace.custom_metrics.metadata[0].name
    labels = {
      run     = "custom-metrics-stackdriver-adapter"
      k8s-app = "custom-metrics-stackdriver-adapter"
    }
  }
  spec {
    replicas = 1
    selector {
      match_labels = {
        run     = "custom-metrics-stackdriver-adapter"
        k8s-app = "custom-metrics-stackdriver-adapter"
      }
    }
    template {
      metadata {
        labels = {
          run                      = "custom-metrics-stackdriver-adapter"
          k8s-app                  = "custom-metrics-stackdriver-adapter"
          "kubernetes.io/cluster-service" = "true"
        }
      }
      spec {
        service_account_name = kubernetes_service_account.custom_metrics_stackdriver_adapter.metadata[0].name
        container {
          image = "gcr.io/gke-release/custom-metrics-stackdriver-adapter:v0.12.2-gke.0"
          name  = "custom-metrics-stackdriver-adapter"
          command = [
            "/adapter",
            "--use-new-resource-model=true"
          ]
          resources {
            limits = {
              cpu    = "250m"
              memory = "200Mi"
            }
            requests = {
              cpu    = "250m"
              memory = "200Mi"
            }
          }
        }
      }
    }
  }
}

# 創建 Service
resource "kubernetes_service" "custom_metrics_stackdriver_adapter" {
  metadata {
    name      = "custom-metrics-stackdriver-adapter"
    namespace = kubernetes_namespace.custom_metrics.metadata[0].name
    labels = {
      run                      = "custom-metrics-stackdriver-adapter"
      k8s-app                  = "custom-metrics-stackdriver-adapter"
      "kubernetes.io/cluster-service" = "true"
      "kubernetes.io/name"            = "Adapter"
    }
  }
  spec {
    port {
      port        = 443
      protocol    = "TCP"
      target_port = 443
    }
    selector = {
      run     = "custom-metrics-stackdriver-adapter"
      k8s-app = "custom-metrics-stackdriver-adapter"
    }
  }
}

# 創建 APIService v1beta1.custom.metrics.k8s.io
resource "kubernetes_api_service" "v1beta1_custom_metrics" {
  metadata {
    name = "v1beta1.custom.metrics.k8s.io"
  }
  spec {
    service {
      name      = kubernetes_service.custom_metrics_stackdriver_adapter.metadata[0].name
      namespace = kubernetes_namespace.custom_metrics.metadata[0].name
    }
    group                  = "custom.metrics.k8s.io"
    version                = "v1beta1"
    insecure_skip_tls_verify = true
    group_priority_minimum = 100
    version_priority       = 100
  }
}

# 創建 APIService v1beta2.custom.metrics.k8s.io
resource "kubernetes_api_service" "v1beta2_custom_metrics" {
  metadata {
    name = "v1beta2.custom.metrics.k8s.io"
  }
  spec {
    service {
      name      = kubernetes_service.custom_metrics_stackdriver_adapter.metadata[0].name
      namespace = kubernetes_namespace.custom_metrics.metadata[0].name
    }
    group                  = "custom.metrics.k8s.io"
    version                = "v1beta2"
    insecure_skip_tls_verify = true
    group_priority_minimum = 100
    version_priority       = 200
  }
}

# 創建 APIService v1beta1.external.metrics.k8s.io
resource "kubernetes_api_service" "v1beta1_external_metrics" {
  metadata {
    name = "v1beta1.external.metrics.k8s.io"
  }
  spec {
    service {
      name      = kubernetes_service.custom_metrics_stackdriver_adapter.metadata[0].name
      namespace = kubernetes_namespace.custom_metrics.metadata[0].name
    }
    group                  = "external.metrics.k8s.io"
    version                = "v1beta1"
    insecure_skip_tls_verify = true
    group_priority_minimum = 100
    version_priority       = 100
  }
}
