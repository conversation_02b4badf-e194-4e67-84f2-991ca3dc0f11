resource "google_bigquery_dataset" "event" {
  dataset_id                  = "event_${var.environment}"
  friendly_name               = "event"
  description                 = "User Event dataset"
  location                    = "asia-east1"
  default_table_expiration_ms = null
  labels = {
    environment = var.environment
  }
}

resource "google_bigquery_dataset" "event_grouping_result" {
  dataset_id                  = "event_grouping_result_${var.environment}"
  friendly_name               = "event_grouping_result"
  description                 = "Grouping result snapshots (by date)"
  location                    = "US"
  default_table_expiration_ms = null
  labels = {
    environment = var.environment
  }
}

resource "google_bigquery_dataset" "backup" {
  dataset_id                  = "backup_${var.environment}"
  friendly_name               = "backup"
  description                 = "Backup dataset"
  location                    = "US"
  default_table_expiration_ms = null
  labels = {
    environment = var.environment
  }
}
resource "google_bigquery_dataset" "snapshot" {
  dataset_id                  = "event_snapshot_${var.environment}"
  friendly_name               = "event_snapshot"
  description                 = "Dataset for snapshots"
  location                    = "US"
  default_table_expiration_ms = null
  labels = {
    environment = var.environment
  }
}

resource "google_bigquery_table" "tagtoo_event" {
  dataset_id               = google_bigquery_dataset.event.dataset_id
  table_id                 = "tagtoo_event"
  require_partition_filter = false
  time_partitioning {
    type          = "DAY"
    field         = "event_time"
    expiration_ms = null
  }
  clustering      = ["ec_id"]
  expiration_time = null
  schema          = file("bigquery/schemas/tagtoo-event.json")
  labels = {
    environment = var.environment
  }
  deletion_protection = true
}

resource "google_bigquery_table" "user_unify_groups" {
  dataset_id      = google_bigquery_dataset.event.dataset_id
  table_id        = "user_unify_groups"
  expiration_time = null

  labels = {
    environment = var.environment
    application = "event"
    component   = "user-unify"
  }
  clustering          = ["group_id"]
  schema              = file("bigquery/schemas/user-unify-groups.json")
  deletion_protection = true
}

resource "google_bigquery_table" "user_unify_group_permanent" {
  dataset_id      = google_bigquery_dataset.event.dataset_id
  table_id        = "user_unify_group_permanent"
  expiration_time = null
  labels = {
    environment = var.environment
    application = "event"
    component   = "user-unify"
  }
  schema              = file("bigquery/schemas/user-unify-group-permanent.json")
  deletion_protection = false
}

resource "google_bigquery_table" "user_unify__intermediate_predict" {
  dataset_id      = google_bigquery_dataset.event.dataset_id
  table_id        = "_intermediate_predict"
  expiration_time = null
  labels = {
    environment = var.environment
    application = "event"
    component   = "user-unify"
  }
  schema              = file("bigquery/schemas/_intermediate_predict.json")
  deletion_protection = false
}

resource "google_bigquery_table" "tagtoo_entity" {
  dataset_id  = google_bigquery_dataset.event.dataset_id
  table_id    = "tagtoo_entity"
  description = "Tagtoo historical entity data (computed from tagtoo_event)"
  labels = {
    application = "event"
  }
  clustering = ["ec_id"]
  materialized_view {
    query               = file("bigquery/queries/tagtoo_entity.sql")
    enable_refresh      = true
    refresh_interval_ms = 86400000 # 1 day
  }
  deletion_protection = false
}

resource "google_bigquery_table" "tagtoo_event_product" {
  dataset_id  = google_bigquery_dataset.event.dataset_id
  table_id    = "tagtoo_event_product"
  description = "Tagtoo historical product data (computed from tagtoo_event)"
  labels = {
    application = "event"
  }
  clustering = ["id", "name"]
  materialized_view {
    query               = file("bigquery/queries/tagtoo_event_product.sql")
    enable_refresh      = true
    refresh_interval_ms = 86400000 # 1 day
  }
  deletion_protection = false
}

// Table Functions
resource "google_bigquery_routine" "table_function_get_entity_by_date" {
  dataset_id      = google_bigquery_dataset.event.dataset_id
  routine_id      = "get_entity_by_date"
  routine_type    = "TABLE_VALUED_FUNCTION"
  language        = "SQL"
  definition_body = file("bigquery/table_functions/get_entity_by_date.sql")
  arguments {
    name          = "start_date"
    argument_kind = "FIXED_TYPE"
    data_type     = jsonencode({ "typeKind" : "STRING" })
  }
  arguments {
    name          = "end_date"
    argument_kind = "FIXED_TYPE"
    data_type     = jsonencode({ "typeKind" : "STRING" })
  }
}

resource "google_bigquery_routine" "table_function_get_entity_neighbors_by_date" {
  dataset_id      = google_bigquery_dataset.event.dataset_id
  routine_id      = "get_entity_neighbors_by_date"
  routine_type    = "TABLE_VALUED_FUNCTION"
  language        = "SQL"
  definition_body = file("bigquery/table_functions/get_entity_neighbors_by_date.sql")
  arguments {
    name          = "start_date"
    argument_kind = "FIXED_TYPE"
    data_type     = jsonencode({ "typeKind" : "STRING" })
  }
  arguments {
    name          = "end_date"
    argument_kind = "FIXED_TYPE"
    data_type     = jsonencode({ "typeKind" : "STRING" })
  }
}

resource "google_bigquery_routine" "table_function_get_grouping_result_by_date" {
  dataset_id      = google_bigquery_dataset.event.dataset_id
  routine_id      = "get_grouping_result_by_date"
  routine_type    = "TABLE_VALUED_FUNCTION"
  language        = "SQL"
  definition_body = file("bigquery/table_functions/get_grouping_result_by_date.sql")
  arguments {
    name          = "start_date"
    argument_kind = "FIXED_TYPE"
    data_type     = jsonencode({ "typeKind" : "STRING" })
  }
  arguments {
    name          = "end_date"
    argument_kind = "FIXED_TYPE"
    data_type     = jsonencode({ "typeKind" : "STRING" })
  }
}
