import abc
import logging
from functools import partial
from collections import deque
from google.cloud import pubsub_v1
from . import parser

__all__ = ['BaseSubscriberClient', 'BasePublisherClient', 'parser']

logger = logging.getLogger(__file__)


class BaseSubscriberClient(metaclass=abc.ABCMeta):
    @property
    @abc.abstractmethod
    def project_id(self):
        pass

    @property
    @abc.abstractmethod
    def subscription_name(self):
        pass

    service_account_path = None
    error_report_service = None

    def __init__(self):
        if self.service_account_path:
            self._client = pubsub_v1.SubscriberClient.from_service_account_file(
                self.service_account_path
            )
        else:
            self._client = pubsub_v1.SubscriberClient()
        self.subscription_path = self._client.subscription_path(
            self.project_id, self.subscription_name
        )

    @abc.abstractmethod
    def callback_function(self, message):
        """The function should either ack() or nack() the message."""
        pass

    def callback(self, message):
        try:
            self.callback_function(message)
        except Exception as e:
            if self.error_report_service:
                self.error_report_service.report_exception()
            else:
                logger.exception(e)
            message.nack()
            return

    def __call__(self, max_messages=None, timeout=None):
        subscribe = partial(
            self._client.subscribe, self.subscription_path, callback=self.callback
        )

        if max_messages:
            # Limit the subscriber to only have n outstanding messages at a time.
            flow_control = pubsub_v1.types.FlowControl(max_messages=max_messages)
            subscribe = partial(subscribe, flow_control=flow_control)

        streaming_pull_future = subscribe()
        print(f'Listening for messages on {self.subscription_path} ...')

        # Wrap subscriber in a 'with' block to automatically call close() when done.
        with self._client:
            try:
                # When `timeout` is not set, result() will block indefinitely,
                # unless an exception is encountered first.
                streaming_pull_future.result(timeout=timeout)
            except Exception:  # noqa
                streaming_pull_future.cancel()
                # Block until the shutdown is complete.
                streaming_pull_future.result()


class BasePublisherClient(metaclass=abc.ABCMeta):
    """Context manageable publisher client.
    Example:

        >>> with PublisherClient('[PROJECT]', '[TOPIC]') as publisher:
        >>>     for i in range(10):
        >>>         publisher.add_message(f"Message {i}")
    """

    @property
    @abc.abstractmethod
    def project_id(self):
        pass

    @property
    @abc.abstractmethod
    def topic_name(self):
        pass

    service_account_path = None

    def __init__(self, max_messages=1000):
        if self.service_account_path:
            self._client = pubsub_v1.PublisherClient.from_service_account_file(
                self.service_account_path
            )
        else:
            self._client = pubsub_v1.PublisherClient()
        self.topic_path = self._client.topic_path(self.project_id, self.topic_name)
        self.tasks = []
        self.future_q = deque(maxlen=max_messages)

    def _produce_message(self):
        for task in self.tasks:
            future = task()
            self.future_q.appendleft(future)
            if len(self.future_q) == self.future_q.maxlen:
                print('Future queue full, yielding control ...')
                yield

    def _consume_message(self):
        while True:
            # Blocking until all messages in queue are published
            while len(self.future_q) > 0:
                future = self.future_q.pop()
                future.result()
            print('Future queue empty, yielding control ...')
            yield

    def add_message(self, data, **kwargs):
        for k, v in kwargs.items():
            assert isinstance(v, bytes) or isinstance(
                v, str
            ), "Message metadata attribute should be text strings or byte strings"
        # Data must be a bytestring
        data = data.encode("utf-8")
        task = partial(self._client.publish, self.topic_path, data=data, **kwargs)
        self.tasks.append(task)

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        producer = self._produce_message()
        consumer = self._consume_message()
        while True:
            try:
                next(producer)
            except StopIteration:
                break
            finally:
                next(consumer)
        if exc_type:
            logger.exception(exc_type)
        return False
