"""
BigQuery Writer 雙寫入架構效能基準測試

驗證雙寫入架構的效能表現，包括吞吐量、延遲、資源使用情況。
基於 v2.1 tracking_id 核心策略的技術規格。

版本: v2.1 (tracking_id 核心策略)
日期: 2025-08-18
"""

import time
import psutil
import json
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Any
from unittest.mock import MagicMock
import logging

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'bq_writer'))

# 模擬延遲常數
MOCK_BQ_BASE_WRITE_LATENCY_SEC = 0.01   # 基礎寫入延遲 10ms
MOCK_BQ_PER_ROW_LATENCY_SEC = 0.005     # 每行額外延遲 5ms

# Mock the modules for testing
class MockOptimizedIntegratedEventTransformer:
    def _generate_tracking_id(self, data: Dict[str, Any], message_id: str) -> str:
        return f"test_tracking_id_{int(time.time())}"

    def transform(self, data: Dict[str, Any], message_id: str) -> Dict[str, Any]:
        return {
            "permanent": data.get("permanent"),
            "ec_id": data.get("ec_id"),
            "partner_source": "test",
            "event_time": data.get("event_time"),
            "create_time": datetime.utcnow().isoformat() + "Z",
            "raw_json": {
                "tracking": {
                    "id": self._generate_tracking_id(data, message_id),
                    "message_id": message_id
                },
                "event": data.get("event", {}),
                "user": data.get("user", {}),
                "location": data.get("location", {})
            }
        }

class MockMemoryOptimizedDualWriter:
    def __init__(self, client):
        self.client = client

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PerformanceBenchmark:
    """效能基準測試類別"""

    def __init__(self):
        """Initialize test environment"""
        self.transformer = MockOptimizedIntegratedEventTransformer()
        self.mock_bq_client = self._create_mock_bq_client()
        self.dual_writer = MockMemoryOptimizedDualWriter(self.mock_bq_client)

        # 測試配置
        self.test_configs = {
            'light_load': {'messages_per_second': 10, 'duration_seconds': 60},
            'normal_load': {'messages_per_second': 50, 'duration_seconds': 120},
            'heavy_load': {'messages_per_second': 100, 'duration_seconds': 180},
            'peak_load': {'messages_per_second': 200, 'duration_seconds': 300}
        }

        # 效能指標收集
        self.metrics = {
            'throughput': [],
            'latency': [],
            'cpu_usage': [],
            'memory_usage': [],
            'error_rate': [],
            'tracking_id_generation_time': [],
            'raw_json_size': []
        }

    def _create_mock_bq_client(self):
        """建立模擬的 BigQuery 客戶端"""
        mock_client = MagicMock()

        # 模擬成功的寫入操作
        def mock_insert_rows_json(table_id, rows):
            # 模擬寫入延遲 (10-50ms)
            time.sleep(MOCK_BQ_BASE_WRITE_LATENCY_SEC + (len(rows) * MOCK_BQ_PER_ROW_LATENCY_SEC))
            return []  # 無錯誤

        mock_client.insert_rows_json = mock_insert_rows_json
        return mock_client

    def generate_test_data(self, count: int) -> List[Dict[str, Any]]:
        """生成測試資料"""
        test_data = []

        for i in range(count):
            data = {
                "permanent": f"test_permanent_{i}",
                "ec_id": 12345 + (i % 100),
                "event_time": (datetime.utcnow() - timedelta(seconds=i)).isoformat() + "Z",
                "link": f"https://example.com/product/{i}",
                "session_id": f"session_{i}_{int(time.time())}",
                "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "referrer": f"https://google.com/search?q=test_{i}",
                "ip_address": f"192.168.1.{(i % 254) + 1}",
                "language": "zh-TW",
                "event": {
                    "name": ["purchase", "view_item", "add_to_cart"][i % 3],
                    "value": 99.99 + (i % 100),
                    "currency": "USD",
                    "custom_data": {
                        "order_id": f"ORDER_{i}"
                    },
                    "items": [
                        {"id": f"item_{i}", "name": f"Product {i}", "price": 49.99}
                    ]
                },
                "user": {
                    "em": f"test{i}@example.com",
                    "ph": f"+123456789{i % 10}"
                },
                "location": {
                    "country_code": "TW",
                    "city": "Taipei"
                }
            }
            test_data.append(data)

        return test_data

    def measure_single_write_performance(self, test_data: List[Dict]) -> Dict[str, float]:
        """測量單一寫入模式的效能"""
        logger.info("測試單一寫入模式效能...")

        start_time = time.time()
        cpu_before = psutil.cpu_percent()
        memory_before = psutil.virtual_memory().percent

        # 執行單一寫入
        for data in test_data:
            table_id = "test_project.test_dataset.tagtoo_event"
            self.mock_bq_client.insert_rows_json(table_id, [data])

        end_time = time.time()
        cpu_after = psutil.cpu_percent()
        memory_after = psutil.virtual_memory().percent

        duration = end_time - start_time
        throughput = len(test_data) / duration

        return {
            'duration': duration,
            'throughput': throughput,
            'cpu_usage': cpu_after - cpu_before,
            'memory_usage': memory_after - memory_before,
            'total_messages': len(test_data)
        }

    def measure_dual_write_performance(self, test_data: List[Dict]) -> Dict[str, float]:
        """測量雙寫入模式的效能"""
        logger.info("測試雙寫入模式效能...")

        start_time = time.time()
        cpu_before = psutil.cpu_percent()
        memory_before = psutil.virtual_memory().percent

        # 收集詳細指標
        latencies = []
        tracking_id_times = []
        raw_json_sizes = []

        # 執行雙寫入
        for data in test_data:
            message_id = f"test_message_{int(time.time() * 1000000)}"

            # 測量 tracking_id 生成時間
            tracking_start = time.time()
            tracking_id = self.transformer._generate_tracking_id(data, message_id)
            tracking_end = time.time()
            tracking_id_times.append((tracking_end - tracking_start) * 1000)  # ms

            # 測量轉換和寫入延遲
            transform_start = time.time()
            integrated_data = self.transformer.transform(data, message_id)

            # 測量 raw_json 大小
            raw_json_size = len(json.dumps(integrated_data["raw_json"]))
            raw_json_sizes.append(raw_json_size)

            # 模擬雙寫入
            # 1. 主要寫入 (tagtoo_event)
            table_id = "test_project.test_dataset.tagtoo_event"
            self.mock_bq_client.insert_rows_json(table_id, [data])

            # 2. 次要寫入 (integrated_event)
            integrated_table_id = "test_project.test_dataset.integrated_event"
            self.mock_bq_client.insert_rows_json(integrated_table_id, [integrated_data])

            transform_end = time.time()
            latencies.append((transform_end - transform_start) * 1000)  # ms

        end_time = time.time()
        cpu_after = psutil.cpu_percent()
        memory_after = psutil.virtual_memory().percent

        duration = end_time - start_time
        throughput = len(test_data) / duration

        return {
            'duration': duration,
            'throughput': throughput,
            'cpu_usage': cpu_after - cpu_before,
            'memory_usage': memory_after - memory_before,
            'total_messages': len(test_data),
            'avg_latency': statistics.mean(latencies),
            'p95_latency': statistics.quantiles(latencies, n=20)[18] if len(latencies) > 20 else max(latencies),
            'avg_tracking_id_time': statistics.mean(tracking_id_times),
            'avg_raw_json_size': statistics.mean(raw_json_sizes),
            'max_raw_json_size': max(raw_json_sizes) if raw_json_sizes else 0
        }

    def run_benchmark_suite(self) -> Dict[str, Any]:
        """執行完整的效能基準測試套件"""
        logger.info("開始執行效能基準測試套件...")

        results = {}

        for test_name, config in self.test_configs.items():
            logger.info(f"執行 {test_name} 測試...")

            # 生成測試資料
            message_count = config['messages_per_second'] * (config['duration_seconds'] // 60)
            test_data = self.generate_test_data(message_count)

            # 執行單一寫入測試
            single_write_results = self.measure_single_write_performance(test_data)

            # 執行雙寫入測試
            dual_write_results = self.measure_dual_write_performance(test_data)

            # 計算效能比較
            throughput_impact = ((dual_write_results['throughput'] - single_write_results['throughput'])
                               / single_write_results['throughput'] * 100)

            results[test_name] = {
                'config': config,
                'single_write': single_write_results,
                'dual_write': dual_write_results,
                'performance_impact': {
                    'throughput_change_percent': throughput_impact,
                    'cpu_overhead_percent': dual_write_results['cpu_usage'] - single_write_results['cpu_usage'],
                    'memory_overhead_percent': dual_write_results['memory_usage'] - single_write_results['memory_usage']
                }
            }

            logger.info(f"{test_name} 完成 - 吞吐量影響: {throughput_impact:.2f}%")

        return results

    def generate_performance_report(self, results: Dict[str, Any]) -> str:
        """生成效能測試報告"""
        report = []
        report.append("# BigQuery Writer 雙寫入架構效能基準測試報告")
        report.append(f"測試時間: {datetime.now().isoformat()}")
        report.append(f"測試版本: v2.1 (tracking_id 核心策略)")
        report.append("")

        # 測試環境資訊
        report.append("## 測試環境")
        report.append(f"- CPU: {psutil.cpu_count()} cores")
        report.append(f"- Memory: {psutil.virtual_memory().total // (1024**3)} GB")
        report.append(f"- Python: {sys.version}")
        report.append("")

        # 測試結果摘要
        report.append("## 測試結果摘要")
        report.append("")

        for test_name, result in results.items():
            config = result['config']
            single = result['single_write']
            dual = result['dual_write']
            impact = result['performance_impact']

            report.append(f"### {test_name.replace('_', ' ').title()}")
            report.append(f"**配置**: {config['messages_per_second']} msg/s × {config['duration_seconds']}s")
            report.append("")

            report.append("| 指標 | 單一寫入 | 雙寫入 | 影響 |")
            report.append("|------|---------|--------|------|")
            report.append(f"| 吞吐量 (msg/s) | {single['throughput']:.2f} | {dual['throughput']:.2f} | {impact['throughput_change_percent']:+.2f}% |")
            report.append(f"| CPU 使用率 | {single['cpu_usage']:.2f}% | {dual['cpu_usage']:.2f}% | {impact['cpu_overhead_percent']:+.2f}% |")
            report.append(f"| 記憶體使用率 | {single['memory_usage']:.2f}% | {dual['memory_usage']:.2f}% | {impact['memory_overhead_percent']:+.2f}% |")

            if 'avg_latency' in dual:
                report.append(f"| 平均延遲 (ms) | - | {dual['avg_latency']:.2f} | - |")
                report.append(f"| P95 延遲 (ms) | - | {dual['p95_latency']:.2f} | - |")
                report.append(f"| tracking_id 生成時間 (ms) | - | {dual['avg_tracking_id_time']:.4f} | - |")
                report.append(f"| 平均 raw_json 大小 (bytes) | - | {dual['avg_raw_json_size']:.0f} | - |")
                report.append(f"| 最大 raw_json 大小 (bytes) | - | {dual['max_raw_json_size']} | - |")

            report.append("")

        return "\n".join(report)


def main():
    """主要測試執行函數"""
    benchmark = PerformanceBenchmark()

    # 執行基準測試
    results = benchmark.run_benchmark_suite()

    # 生成報告
    report = benchmark.generate_performance_report(results)

    # 儲存報告
    with open('performance_benchmark_report.md', 'w', encoding='utf-8') as f:
        f.write(report)

    print("效能基準測試完成！")
    print("報告已儲存至: performance_benchmark_report.md")

    return results


if __name__ == "__main__":
    main()
