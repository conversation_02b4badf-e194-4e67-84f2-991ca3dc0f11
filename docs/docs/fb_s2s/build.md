# 構建 Docker 映像

本文檔說明如何構建 S2S 服務的 Docker 映像，包括本地構建和使用 Google Cloud Build 構建。

## 本地構建

使用 Makefile 中定義的命令在本地構建 Docker 映像：

```bash
make build-event-s2s TAG_NAME=your-tag-name
```

這個命令會使用 `docker buildx build` 來構建映像，並將映像標籤設置為 `your-tag-name`。

!!! warning "架構注意事項"
    為了避免架構不匹配問題（特別是在 Apple Silicon Mac 上開發時），所有構建命令都明確指定了 `--platform=linux/amd64` 參數。

## 使用 Google Cloud Build 構建

為了解決在本地構建 Docker 映像時可能遇到的架構不匹配問題，我們添加了使用 Google Cloud Build 構建映像的功能。

### 使用方法

使用以下命令來使用 Google Cloud Build 構建映像：

```bash
make build-event-s2s-cloud TAG_NAME=your-tag-name
```

這個命令會使用 Google Cloud Build 來構建映像，並將映像標籤設置為 `your-tag-name`。

### 優點

使用 Google Cloud Build 構建映像有以下優點：

1. **架構一致性**：Google Cloud Build 會在 Google Cloud 的基礎設施上構建映像，確保映像的架構與生產環境一致。
2. **跨平台兼容性**：無論您使用的是什麼平台（Mac、Windows、Linux），構建出的映像都會是一致的。
3. **資源效率**：構建過程在 Google Cloud 上進行，不會消耗本地機器的資源。

### 配置文件

Google Cloud Build 的配置文件位於 `s2s/cloudbuild.yaml`，其中定義了構建步驟和映像標籤：

```yaml
steps:
- name: 'gcr.io/cloud-builders/docker'
  args: ['build', '--platform=linux/amd64', '-f', 's2s/Dockerfile', '-t', 'asia.gcr.io/tagtoo-tracking/event-s2s:$_TAG_NAME', '.']
images:
- 'asia.gcr.io/tagtoo-tracking/event-s2s:$_TAG_NAME'
```

### 注意事項

- 確保您已經安裝並配置了 `gcloud` 命令行工具。
- 確保您有足夠的權限來使用 Google Cloud Build 服務。
- 構建過程可能需要幾分鐘的時間，請耐心等待。

## 常見問題

### exec format error

如果您在運行容器時遇到 "exec format error"，這通常是因為 Docker 映像的架構與運行環境不匹配。確保在構建時使用 `--platform=linux/amd64` 參數，或者使用 Google Cloud Build 來構建映像。

### 構建失敗

如果構建過程失敗，請檢查以下幾點：

1. 確保您有足夠的權限訪問所有必要的資源。
2. 檢查網絡連接，確保可以訪問所有依賴的服務。
3. 檢查 Dockerfile 中的指令是否正確。
4. 如果使用 Google Cloud Build，檢查 Cloud Build 日誌以獲取更詳細的錯誤信息。
