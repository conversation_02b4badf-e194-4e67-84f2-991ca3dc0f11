import argparse
import json
import logging

import apache_beam as beam
import apache_beam.transforms.window as window
from apache_beam.options.pipeline_options import PipelineOptions
from apache_beam.io.gcp.pubsub import PubsubMessage


class UserOptions(PipelineOptions):
    @classmethod
    def _add_argparse_args(cls, parser):  # noqa
        parser.add_argument(
            "--input-subscription",
            help="The Cloud Pub/Sub subscription to read from.\n"
            '"projects/<PROJECT_NAME>/subscriptions/<SUBSCRIPTION_NAME>".',
        )
        parser.add_argument(
            "--output-topic",
            help="The Cloud Pub/Sub topic to write to.\n"
            '"projects/<PROJECT_NAME>/topics/<TOPIC_NAME>".',
        )
        parser.add_argument(
            "--window-size",
            type=int,
            default=1,
            help="Output file's window size in number of seconds.",
        )


def compose_group_key(message):
    return f"{message.attributes['version']}#{message.attributes['ec_id']}"


def decompose_group_key(key):
    split = key.split('#')
    return split[0], split[1]


def compress_messages(tuples):
    """Concat grouped data for multiple messages, then transform into PubSubMessage"""
    key, messages = tuples
    version, ec_id = decompose_group_key(key)
    concatenated = [json.loads(message) for message in messages]
    return PubsubMessage(
        data=json.dumps(concatenated).encode("utf-8"),
        attributes={'ec_id': ec_id, 'version': version},
    )


def run(pipeline_args: list):
    # `save_main_session` is set to true because some DoFn's rely on
    # globally imported modules.
    pipeline_options = PipelineOptions(
        pipeline_args, streaming=True, save_main_session=True
    )
    user_options = pipeline_options.view_as(UserOptions)

    with beam.Pipeline(options=pipeline_options) as pipeline:
        messages = (
            pipeline
            | "Read PubSub Messages"
            >> beam.io.ReadFromPubSub(
                subscription=user_options.input_subscription, with_attributes=True
            )
            | 'Parse Message'
            >> beam.Map(lambda msg: (compose_group_key(msg), msg.data))
            # Note that all the elements in one window must fit into memory
            | "Windowing"
            >> beam.WindowInto(window.FixedWindows(user_options.window_size))
            | "Grouping" >> beam.GroupByKey()
            | "Compress Messages" >> beam.Map(compress_messages)
        )
        # Publish the results into PubSub
        _ = messages | 'Write to PubSub' >> beam.io.WriteToPubSub(
            user_options.output_topic, with_attributes=True
        )


if __name__ == "__main__":  # noqa
    logging.getLogger().setLevel(logging.INFO)

    parser = argparse.ArgumentParser()
    known_args, pipeline_args = parser.parse_known_args()

    run(pipeline_args)
