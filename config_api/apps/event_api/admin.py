from django.contrib import admin
from .models import Scope, Client, AccessToken


class ScopeAdmin(admin.ModelAdmin):
    list_display = ['name']


class ClientAdmin(admin.ModelAdmin):
    list_display = ['name', 'client_id', 'is_internal', 'channel_type', 'created']
    search_fields = ['name']
    ordering = ['-created']
    readonly_fields = ['client_id', 'created', 'modified']


class AccessTokenAdmin(admin.ModelAdmin):
    list_display = [
        'client',
        'ec_id',
        'token',
        'display_scopes',
        'is_revoked',
    ]
    search_fields = ['name']
    ordering = ['-created']
    readonly_fields = ['token', 'revoked', 'created', 'modified']

    def display_scopes(self, obj):  # noqa
        return ','.join([s.name for s in obj.scopes.all()])


admin.site.register(Scope, ScopeAdmin)
admin.site.register(Client, ClientAdmin)
admin.site.register(AccessToken, AccessTokenAdmin)
