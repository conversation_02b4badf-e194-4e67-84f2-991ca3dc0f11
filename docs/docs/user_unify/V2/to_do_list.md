## Test
- 由於 User Unify 在實作上，大量使用 BigQuery SQL，因此在做測試時，建議另外多開一個 data set 放小量的測資，供測試使用
- 若有開測試用的 data set，可能要考慮是否要加在 [user_unify.tf](https://github.com/Tagtoo/ad-track/blob/master/deploy/user_unify.tf) 方便將來管理，但若有找到可以 mock BigQuery 的方法，就不需要另開測試用的 data set
- 由於 SQL 蠻不直觀的，因此建議可以再寫一些 Python Script 來搭配驗證 SQL 的結果是否真的有符合預期

## Optimization
- [每日 Grouping result 的 group 所含 GID 數量討論](https://github.com/Tagtoo/ad-track/issues/249)

## Future Problems
- 新增其他廣告渠道的追蹤 ID
- 將來資量過大時，可能會出現 [Issue 244](https://github.com/Tagtoo/ad-track/issues/244) 或 [Issue 247](https://github.com/Tagtoo/ad-track/issues/247) 遇到的問題
- permanent 可能需要退場機制，不應該無上限的一直新增