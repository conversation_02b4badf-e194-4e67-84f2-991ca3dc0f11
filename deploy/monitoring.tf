# 死信隊列警報策略
resource "google_monitoring_alert_policy" "tagtoo_event_to_bigquery_deadletter_alert" {
  display_name = "Tagtoo event to bigquery deadletter queue has messages"
  combiner     = "OR"  # 添加必要的 combiner 參數

  documentation {
    content = <<-EOT
      Messages in the deadletter queue indicate that some messages have failed processing after multiple attempts.

      1. Check the deadletter queue to understand what messages are failing.
      2. Fix the underlying issue in the subscriber code.
      3. Consider reprocessing the messages from the deadletter queue once the issue is fixed.
    EOT
    mime_type = "text/markdown"
  }

  conditions {
    display_name = "Unacked messages in deadletter queue"

    condition_threshold {
      filter = "metric.type=\"pubsub.googleapis.com/subscription/num_undelivered_messages\" resource.type=\"pubsub_subscription\" resource.labels.subscription_id=\"tagtoo-event-to-bigquery-deadletter-sub-${terraform.workspace}\""
      comparison = "COMPARISON_GT"
      threshold_value = 0
      duration = "300s"

      trigger {
        count = 1
      }

      aggregations {
        alignment_period = "60s"
        per_series_aligner = "ALIGN_MEAN"
      }
    }
  }

  # 移除不支援的 severity 參數
  # severity = "WARNING"

  notification_channels = [
    "projects/tagtoo-tracking/notificationChannels/8364392097963667925",  # <EMAIL>
    "projects/tagtoo-tracking/notificationChannels/9326133803526328119",  # <EMAIL>
    "projects/tagtoo-tracking/notificationChannels/4418949314967596891",  # <EMAIL>
    "projects/tagtoo-tracking/notificationChannels/6960126470672007525",  # <EMAIL>
    "projects/tagtoo-tracking/notificationChannels/2491074233266009643"   # <EMAIL>
  ]
}
