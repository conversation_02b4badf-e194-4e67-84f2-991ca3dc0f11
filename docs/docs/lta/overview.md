# LTA (Label Target Audience) 數據處理系統

LTA 數據處理系統是 Tagtoo 追蹤系統的一個重要組件，負責處理大量用戶數據並將其發送到 Facebook Conversion API。本文檔提供了系統架構、數據流程和最佳實踐的詳細說明。

## 系統架構概述

LTA 數據處理系統是一個事件驅動的架構，由以下關鍵組件組成：

- **Google Cloud Storage (GCS)**: 存儲 AVRO 格式的用戶數據文件
- **Pub/Sub**: 消息隊列，用於觸發數據處理
- **Kubernetes**: 容器編排平台，運行數據處理 Job
- **Facebook Conversion API**: 數據的最終目的地

## 數據流程

1. **數據源**:
   - AVRO 文件存儲在 GCS 中，路徑格式為 `gs://tagtoo-ml-workflow/topic10/Audience_labels/YYYYMMDD/XXXXXX.avro`
   - 文件包含用戶數據（如電子郵件、手機號碼、性別、出生日期等）和 Facebook 相關信息

2. **觸發機制**:
   - 上游系統將消息發送到 `lta-prod` Pub/Sub 主題
   - 消息包含 `ec_id`（客戶 ID）、`file_name`（AVRO 文件路徑）和 `version`（API 版本）參數

3. **消息處理**:
   - `s2s-lta-subscriber` Kubernetes 部署監聽 `lta-v1-prod` 訂閱
   - 當收到消息時，創建 `load-lta-data` Kubernetes Job

4. **數據處理**:
   - Job 從 GCS 讀取 AVRO 文件
   - 解析用戶數據並轉換為 Facebook Conversion API 格式
   - 分批發送到 Facebook API

5. **結果報告**:
   - 成功處理後，記錄日誌並清理資源

## 關鍵組件

### s2s-lta-subscriber 部署

- **功能**: 監聽 Pub/Sub 訂閱並創建處理 Job
- **配置**: 在 `deploy/s2s.tf` 中定義
- **命令**: `python subscribe.py lta --version=v1 --max-messages=1`
- **節點選擇器**: `application=event` 和 `name=s2s-lta-subscriber`

### load-lta-data Kubernetes Job

- **模板**: `s2s/s2s/LTA/jobs/k8s_job_load_lta_data.yaml`
- **命名**: 使用人類可讀格式，如 `load-lta-data-YYYYMMDD-HHMMSS-UUID`
- **命令**: `python load_lta_data.py --filename={filename} --ecid={ec_id} --version={version}`
- **資源配置**:
  - CPU 請求: 1000m (1 核)
  - CPU 限制: 1.5 核
  - 內存請求: 1.5Gi
  - 內存限制: 3Gi

### 調度優化

為了確保 LTA 數據處理 Job 的高效執行，我們實施了以下調度優化：

#### 1. Pod 反親和性 (Pod Anti-Affinity)

```yaml
affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchExpressions:
          - key: job-type
            operator: In
            values:
            - load-lta-data
        topologyKey: "kubernetes.io/hostname"
```

這確保了同類型的 Job 盡可能分散到不同節點，減少資源競爭。

#### 2. 拓撲分佈約束 (Topology Spread Constraints)

```yaml
topologySpreadConstraints:
- maxSkew: 1
  topologyKey: kubernetes.io/hostname
  whenUnsatisfiable: ScheduleAnyway
  labelSelector:
    matchLabels:
      job-type: load-lta-data
```

這確保了 Job 均勻分佈在各節點，避免某些節點過載。

#### 3. 優先級設置 (Priority Class)

```yaml
priorityClassName: lta-job-priority
```

對應的 PriorityClass 定義：

```yaml
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: lta-job-priority
value: 1000
globalDefault: false
description: "Priority class for LTA data processing jobs"
```

這確保了 LTA Job 有適當的優先級，在資源緊張時能獲得更好的調度。

## 性能考量

### 資源使用

- **CPU 使用**: 處理大型 AVRO 文件時，CPU 是主要瓶頸
- **內存使用**: 批處理大量記錄時需要足夠內存
- **網絡使用**: 與 Facebook API 通信時需要考慮網絡延遲和帶寬

### 批處理策略

- 默認批次大小為 10,000 條消息
- 批次過大可能導致處理時間過長或失敗
- 批次過小可能增加 API 調用次數和網絡開銷

### 節點資源競爭

- 多個 Job 在同一節點上運行可能導致資源競爭
- 資源競爭是執行時間差異的主要原因（如 59 秒 vs 401 秒）
- 通過調度優化可以顯著減少執行時間差異

## 最佳實踐

1. **資源配置**:
   - 根據實際負載調整 CPU 和內存請求
   - 確保 CPU 限制足夠處理峰值負載

2. **調度策略**:
   - 使用 Pod 反親和性和拓撲分佈約束確保負載均衡
   - 設置適當的優先級確保關鍵任務優先執行

3. **監控與告警**:
   - 監控 Job 執行時間和成功率
   - 設置告警以及時發現和解決問題

4. **錯誤處理**:
   - 實施重試機制處理暫時性錯誤
   - 使用死信隊列處理無法處理的消息

## 故障排除

### 常見問題

1. **執行時間差異大**:
   - 檢查節點資源使用率
   - 確認調度策略是否正確應用

2. **處理失敗**:
   - 檢查 AVRO 文件格式和大小
   - 檢查 Facebook API 響應和錯誤消息

3. **資源不足**:
   - 考慮增加節點數量或調整資源請求
   - 檢查節點自動擴展配置

## 未來改進

1. **動態批處理**:
   - 根據系統負載動態調整批次大小
   - 在高負載時使用較小批次，低負載時使用較大批次

2. **更精細的監控**:
   - 添加更詳細的指標，如批次處理時間、API 響應時間等
   - 實施更精細的告警策略

3. **資源自動調整**:
   - 根據歷史數據自動調整資源請求和限制
   - 實施更智能的調度策略

## 參考資料

- [Facebook Conversion API 文檔](https://developers.facebook.com/docs/marketing-api/conversions-api/)
- [Kubernetes 調度文檔](https://kubernetes.io/docs/concepts/scheduling-eviction/kube-scheduler/)
- [Pub/Sub 文檔](https://cloud.google.com/pubsub/docs)
