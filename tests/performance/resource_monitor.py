# -*- coding: utf-8 -*-
"""
資源監控工具

監控雙寫入架構的系統資源使用情況，包括 CPU、記憶體、磁碟 I/O。
支援即時監控和歷史資料收集。

版本: v2.1 (tracking_id 核心策略)
日期: 2025-08-18
"""

import psutil
import time
import json
import threading
from datetime import datetime
from typing import Dict, List, Any
import logging

logger = logging.getLogger(__name__)


class ResourceMonitor:
    """系統資源監控器"""

    def __init__(self, sample_interval: float = 1.0):
        """
        初始化資源監控器

        Args:
            sample_interval: 採樣間隔（秒）
        """
        self.sample_interval = sample_interval
        self.monitoring = False
        self.monitor_thread = None
        self.data = []

    def start_monitoring(self):
        """開始監控"""
        if self.monitoring:
            return

        self.monitoring = True
        self.data = []
        self.monitor_thread = threading.Thread(target=self._monitor_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        logger.info("資源監控已開始")

    def stop_monitoring(self):
        """停止監控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
        logger.info("資源監控已停止")

    def _monitor_loop(self):
        """監控循環"""
        while self.monitoring:
            try:
                # 收集系統資源資訊
                cpu_percent = psutil.cpu_percent(interval=None)
                memory = psutil.virtual_memory()
                disk = psutil.disk_usage('/')

                # 收集網路資訊
                network = psutil.net_io_counters()

                # 收集程序資訊
                current_process = psutil.Process()
                process_info = {
                    'cpu_percent': current_process.cpu_percent(),
                    'memory_mb': current_process.memory_info().rss / 1024 / 1024,
                    'num_threads': current_process.num_threads(),
                    'num_fds': current_process.num_fds() if hasattr(current_process, 'num_fds') else 0
                }

                sample = {
                    'timestamp': datetime.now().isoformat(),
                    'cpu': {
                        'percent': cpu_percent,
                        'count': psutil.cpu_count(),
                        'freq': psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
                    },
                    'memory': {
                        'total_gb': memory.total / 1024 / 1024 / 1024,
                        'available_gb': memory.available / 1024 / 1024 / 1024,
                        'percent': memory.percent,
                        'used_gb': memory.used / 1024 / 1024 / 1024
                    },
                    'disk': {
                        'total_gb': disk.total / 1024 / 1024 / 1024,
                        'used_gb': disk.used / 1024 / 1024 / 1024,
                        'free_gb': disk.free / 1024 / 1024 / 1024,
                        'percent': (disk.used / disk.total) * 100
                    },
                    'network': {
                        'bytes_sent': network.bytes_sent,
                        'bytes_recv': network.bytes_recv,
                        'packets_sent': network.packets_sent,
                        'packets_recv': network.packets_recv
                    },
                    'process': process_info
                }

                self.data.append(sample)

            except Exception as e:
                logger.error(f"監控資料收集錯誤: {e}")

            time.sleep(self.sample_interval)

    def get_summary_stats(self) -> Dict[str, Any]:
        """獲取摘要統計資訊"""
        if not self.data:
            return {}

        # 計算各項指標的統計資訊
        cpu_values = [sample['cpu']['percent'] for sample in self.data]
        memory_values = [sample['memory']['percent'] for sample in self.data]
        process_cpu_values = [sample['process']['cpu_percent'] for sample in self.data]
        process_memory_values = [sample['process']['memory_mb'] for sample in self.data]

        def calculate_stats(values):
            if not values:
                return {}
            return {
                'min': min(values),
                'max': max(values),
                'avg': sum(values) / len(values),
                'count': len(values)
            }

        return {
            'monitoring_duration_seconds': len(self.data) * self.sample_interval,
            'sample_count': len(self.data),
            'system_cpu': calculate_stats(cpu_values),
            'system_memory': calculate_stats(memory_values),
            'process_cpu': calculate_stats(process_cpu_values),
            'process_memory_mb': calculate_stats(process_memory_values),
            'peak_memory_usage_mb': max(process_memory_values) if process_memory_values else 0,
            'avg_thread_count': sum(sample['process']['num_threads'] for sample in self.data) / len(self.data)
        }

    def export_data(self, filename: str):
        """匯出監控資料到檔案"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump({
                'metadata': {
                    'sample_interval': self.sample_interval,
                    'total_samples': len(self.data),
                    'start_time': self.data[0]['timestamp'] if self.data else None,
                    'end_time': self.data[-1]['timestamp'] if self.data else None
                },
                'data': self.data,
                'summary': self.get_summary_stats()
            }, f, indent=2, ensure_ascii=False)

        logger.info(f"監控資料已匯出至: {filename}")


class PerformanceProfiler:
    """效能分析器"""

    def __init__(self):
        self.profiles = {}

    def start_profile(self, name: str):
        """開始效能分析"""
        self.profiles[name] = {
            'start_time': time.time(),
            'start_cpu': psutil.cpu_percent(),
            'start_memory': psutil.virtual_memory().percent
        }

    def end_profile(self, name: str) -> Dict[str, float]:
        """結束效能分析並返回結果"""
        if name not in self.profiles:
            raise ValueError(f"Profile '{name}' not found")

        profile = self.profiles[name]
        end_time = time.time()
        end_cpu = psutil.cpu_percent()
        end_memory = psutil.virtual_memory().percent

        result = {
            'duration_seconds': end_time - profile['start_time'],
            'cpu_change_percent': end_cpu - profile['start_cpu'],
            'memory_change_percent': end_memory - profile['start_memory']
        }

        del self.profiles[name]
        return result


class GKEResourceCalculator:
    """GKE 資源計算器"""

    @staticmethod
    def calculate_pod_capacity(machine_type: str, pod_resources: Dict[str, str]) -> Dict[str, Any]:
        """
        計算 GKE 節點的 Pod 容量

        Args:
            machine_type: 機器類型 (e.g., 'n4-highcpu-4')
            pod_resources: Pod 資源需求 {'cpu': '810m', 'memory': '1215Mi'}

        Returns:
            容量分析結果
        """
        # GKE 機器類型規格
        machine_specs = {
            'n4-highcpu-2': {'cpu': 2000, 'memory': 2048},  # 2 vCPU, 2GB
            'n4-highcpu-4': {'cpu': 4000, 'memory': 4096},  # 4 vCPU, 4GB
            'n4-highcpu-8': {'cpu': 8000, 'memory': 8192},  # 8 vCPU, 8GB
        }

        # GKE 系統預留資源
        system_reserved = {
            'n4-highcpu-2': {'cpu': 300, 'memory': 648},
            'n4-highcpu-4': {'cpu': 400, 'memory': 1000},
            'n4-highcpu-8': {'cpu': 500, 'memory': 1500},
        }

        if machine_type not in machine_specs:
            raise ValueError(f"Unsupported machine type: {machine_type}")

        # 計算可用資源
        total_resources = machine_specs[machine_type]
        reserved_resources = system_reserved[machine_type]
        available_resources = {
            'cpu': total_resources['cpu'] - reserved_resources['cpu'],
            'memory': total_resources['memory'] - reserved_resources['memory']
        }

        # 解析 Pod 資源需求
        def parse_resource(value: str) -> int:
            if value.endswith('m'):
                return int(value[:-1])  # CPU millicores
            elif value.endswith('Mi'):
                return int(value[:-2])  # Memory MiB
            elif value.endswith('Gi'):
                return int(value[:-2]) * 1024  # Memory GiB to MiB
            else:
                return int(value)

        pod_cpu = parse_resource(pod_resources['cpu'])
        pod_memory = parse_resource(pod_resources['memory'])

        # 計算可部署的 Pod 數量
        pods_by_cpu = available_resources['cpu'] // pod_cpu
        pods_by_memory = available_resources['memory'] // pod_memory
        max_pods = min(pods_by_cpu, pods_by_memory)

        # 計算資源使用率
        if max_pods > 0:
            cpu_utilization = (pod_cpu * max_pods) / available_resources['cpu'] * 100
            memory_utilization = (pod_memory * max_pods) / available_resources['memory'] * 100
        else:
            cpu_utilization = memory_utilization = 0

        return {
            'machine_type': machine_type,
            'total_resources': total_resources,
            'available_resources': available_resources,
            'pod_resources': {'cpu': pod_cpu, 'memory': pod_memory},
            'max_pods_per_node': max_pods,
            'limiting_factor': 'cpu' if pods_by_cpu < pods_by_memory else 'memory',
            'resource_utilization': {
                'cpu_percent': cpu_utilization,
                'memory_percent': memory_utilization
            }
        }


def main():
    """測試資源監控功能"""
    monitor = ResourceMonitor(sample_interval=0.5)

    print("開始資源監控測試...")
    monitor.start_monitoring()

    # 模擬一些工作負載
    time.sleep(5)

    monitor.stop_monitoring()

    # 獲取統計資訊
    stats = monitor.get_summary_stats()
    print("監控統計資訊:")
    print(json.dumps(stats, indent=2, ensure_ascii=False))

    # 匯出資料
    monitor.export_data('resource_monitor_test.json')

    # 測試 GKE 資源計算
    calculator = GKEResourceCalculator()
    capacity = calculator.calculate_pod_capacity(
        'n4-highcpu-4',
        {'cpu': '810m', 'memory': '1215Mi'}
    )
    print("\nGKE 資源容量分析:")
    print(json.dumps(capacity, indent=2, ensure_ascii=False))


if __name__ == "__main__":
    main()
