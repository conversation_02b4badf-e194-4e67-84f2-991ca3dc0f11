import logging
from operator import itemgetter

from shared import pubsub

from .. import service, settings

DEFAULT_RETRY_PERIOD = 30
DEFAULT_MAX_RETRY_TIMES = 10
MESSAGE_LIMIT = 8000000

logger = logging.getLogger('s2s')


class LTAToFacebookCapi(pubsub.parser.MessageParserMixin, pubsub.BaseSubscriberClient):
    project_id = settings.GCP_PROJECT_ID
    subscription_name = settings.LTA_FACEBOOK_SUBSCRIPTION_V1
    message_attribute_parsers = {
        'ec_id': pubsub.parser.parse_int,
        'file_name': pubsub.parser.parse_string,
        'version': pubsub.parser.parse_string,
    }
    service_account_path = None  # Use VM service account
    error_report_service = service.LTAErrorReportingService(version='v1')

    def callback_function(self, message):
        from .jobs import create_loader_job

        result = self.parse(message)
        ec_id, file_name, version = itemgetter('ec_id', 'file_name', 'version')(result)

        create_loader_job(
            filename=file_name,
            ec_id=ec_id,
            version=version,
        )
        message.ack()
