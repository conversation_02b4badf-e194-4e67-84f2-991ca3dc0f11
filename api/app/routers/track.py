import hashlib
from typing import Optional

from fastapi import (
    APIRouter,
    BackgroundTasks,
    Body,
    Depends,
    HTTPException,
    Header,
    Query,
    Request,
    status,
)
from fastapi.responses import JSONResponse

import aioredis

from ..config import settings
from ..enums import ChannelType, EventDataScope
from ..exceptions import FacebookUserClientIPNotGlobalException
from ..schemas.validators import EventDataValidator
from ..services import error_reporting_client, pubsub_client
from ..utils import get_db, get_event_config, get_ip_from_request, is_global_ip
from .generic import EventAPICommonRoute

router = APIRouter(route_class=EventAPICommonRoute)


async def get_meta_data(
    request: Request,
    ip: str = Depends(get_ip_from_request),
    user_agent: Optional[str] = Header(None),
):
    return {
        'user_agent': user_agent,
        'ip': ip,
        'system_time': request.state.system_time,
    }


async def get_event_data(event_data: dict = Body(...)):
    return event_data


async def get_event_config_from_body_token(
    event_data: dict = Depends(get_event_data),
    db: aioredis.Redis = Depends(get_db),
):
    token = event_data.get('token')
    return await get_event_config(token, db)


def verify_scope(
    event_config: dict = Depends(get_event_config_from_body_token),
    event_data: dict = Depends(get_event_data),
):
    token = event_data['token']
    unauthorized_scopes = []
    for scope in EventDataScope:
        if scope.data_key in event_data and scope.name not in event_config['scopes']:
            unauthorized_scopes.append(scope.name)
    if unauthorized_scopes:
        unauth_scopes_string = ','.join(unauthorized_scopes)
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f'Token ({token}) has no authority on scopes: {unauth_scopes_string}',
        )
    return unauthorized_scopes


@router.post(
    '/event/v1',
    tags=['event'],
)
async def event(
    *,
    meta_data: dict = Depends(get_meta_data),
    event_config: dict = Depends(get_event_config_from_body_token),
    event_data: dict = Depends(get_event_data),
    request: Request,
    bg_tasks: BackgroundTasks,
):
    verify_scope(event_config, event_data)
    validator = EventDataValidator(request, event_data, event_config)
    event_data = validator.validate()

    # Validate fields by scopes
    if not validator.has_top_level_error():
        if validator.is_scope_valid(EventDataScope.FACEBOOK_CAPI):
            bg_tasks.add_task(
                process_facebook_event_data,
                meta_data,
                event_data,
                event_config,
            )

        if validator.is_scope_valid(EventDataScope.TAGTOO_EVENT):
            bg_tasks.add_task(
                process_tagtoo_event_data,
                meta_data,
                event_data,
                event_config,
            )

    if validator.errors():
        channel_type = event_config['client']['channel_type']
        message = validator.construct_error_message()
        bg_tasks.add_task(error_reporting_client.report, message)
        if channel_type == ChannelType.FRONTEND:
            return JSONResponse(status_code=status.HTTP_200_OK, content={})
        else:
            return JSONResponse(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, content=message
            )

    return JSONResponse(status_code=status.HTTP_200_OK, content={})


def process_facebook_event_data(
    meta_data: dict,
    event_data: dict,
    event_config: dict,
):
    facebook_data = event_data['facebook']
    if event_config['client']['channel_type'] == ChannelType.FRONTEND:
        facebook_data['event_time'] = event_data.get('event_time') or int(
            meta_data['system_time']
        )
        ip = meta_data['ip']
        if not settings.DEBUG and not is_global_ip(ip):
            error_reporting_client.report(FacebookUserClientIPNotGlobalException(ip))
            ip = ''
        facebook_data['user_data']['client_ip_address'] = ip
        facebook_data['user_data']['client_user_agent'] = meta_data['user_agent']
    else:  # From BE and `event_time` is assigned in payload
        # XXX: The `event_time` format through S2S has not yet been specified.
        facebook_data['event_time'] = event_data['event_time'] // 1000

    if not facebook_data['user_data']:
        del facebook_data['user_data']

    pubsub_client.publish(
        topic_name=settings.PUBSUB_EVENT_FACEBOOK_CAPI_TOPIC_NAME,
        message=facebook_data,
        attrs={'version': 'v1', 'ec_id': str(event_config['ec_id'])},
    )


def process_tagtoo_event_data(
    meta_data: dict,
    event_data: dict,
    event_config: dict,
):
    tagtoo_event_data = event_data['tagtoo']
    channel_type = event_config['client']['channel_type']
    system_time = int(meta_data['system_time'])

    tagtoo_event_data['event_time'] = event_data.get('event_time') or system_time
    if channel_type == ChannelType.FRONTEND:
        tagtoo_event_data['user_agent'] = meta_data['user_agent']
        tagtoo_event_data['ip_address'] = meta_data['ip']

    pubsub_client.publish(
        topic_name=settings.PUBSUB_EVENT_TAGTOO_EVENT_TOPIC_NAME,
        message=tagtoo_event_data,
        attrs={'version': 'v1', 'ec_id': str(event_config['ec_id'])},
    )


@router.get(
    '/permanent',
    tags=['event'],
)
async def get_permanent(
    fp: str = Query(
        ...,
        title='Fingerprint',
        description='A random ID generated with FingerPrintJS',
        regex='[0-9a-fA-F]{32}',
    ),
    meta_data: dict = Depends(get_meta_data),
):
    string = fp + meta_data['ip']
    permanent = hashlib.blake2b(string.encode("utf-8"), digest_size=16).hexdigest()
    return {
        'permanent': permanent,
    }
