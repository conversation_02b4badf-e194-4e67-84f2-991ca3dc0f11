# Visit the official image source code at https://github.com/ip2location/docker-ip2location-mysql
# The origin image download and initialize the database at container start.
# In order to prevent the re-download (which might exceed the limit), pre-download the data at the build time.

# Stage 1
FROM ip2location/mysql as official
## Fixed variables
ENV MYSQL_PASSWORD passwd

## Replaceable variables
ARG API_TOKEN
ARG PRODUCT_CODE

COPY download.sh .

RUN apt-get install -y --no-install-recommends gnupg curl mariadb-server

RUN apt-key adv --keyserver keyserver.ubuntu.com --recv-keys 648ACFD622F3D138 0E98404D386FA1D9 DCC9EFBF77E11517 AA8E81B4331F7F50  112695A0E562B32A

RUN apt-get install -y file

COPY ./download.sh /usr/local/bin/download.sh
RUN chmod +x /usr/local/bin/download.sh

RUN /usr/local/bin/download.sh && mysqldump --socket=/var/run/mysqld/mysqld.sock --databases ip2location_database > /data.sql

# Stage 2
FROM mariadb:latest

# Install MySQLTuner
# RUN echo "deb http://deb.debian.org/debian buster main" > /etc/apt/sources.list
#RUN apt-get clean
#RUN rm -rf /var/lib/apt/lists/*
RUN apt-get update
RUN apt-get install -y wget

RUN wget http://mysqltuner.pl/ -O mysqltuner.pl && \
    wget https://raw.githubusercontent.com/major/MySQLTuner-perl/master/basic_passwords.txt -O basic_passwords.txt && \
    wget https://raw.githubusercontent.com/major/MySQLTuner-perl/master/vulnerabilities.csv -O vulnerabilities.csv

ENV MYSQL_ALLOW_EMPTY_PASSWORD yes

COPY --from=official /etc/mysql/mariadb.conf.d/999-custom.cnf /etc/mysql/conf.d/custom.cnf

COPY --from=official /data.sql docker-entrypoint-initdb.d/data.sql