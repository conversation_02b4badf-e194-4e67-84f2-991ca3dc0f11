- 存放路徑為 `ad-track/user_unify/user_unify/bigquery/sql/`
- 主要使用 SQL 來處理 [Flowchart](./../workflow.md#determine-groups-update-grouping-reslut-detail-flow-chart) 中的各種行為
- 會使用到不少次 [Merge 語法](https://cloud.google.com/bigquery/docs/reference/standard-sql/dml-syntax#merge_statement)

### merge_new_entities.sql
```sql
MERGE `$groups_table` T USING (
    -- 抓出指定日期的 Grouping result 作為 next_day_result
    WITH next_day_result AS (
        SELECT * FROM `$grouping_result_table`
    ),
    -- 將 next_day_result 跟 $groups_table 一樣 group id 的欄位合併
    -- 由於 ARRAY_CONCAT() 要是裡面有 NULL，就會回傳 NULL
    -- 所以要搭配 IFNULL 來處理這個狀況
    contact_res AS (
        SELECT
            cnds.group_id,
            IFNULL(temp.group_time, cnds.group_time) group_time,
            IFNULL(
                (temp.permanents || cnds.permanents),
                cnds.permanents
            ) permanents,
            IFNULL((temp.emails || cnds.emails), cnds.emails) emails,
            IFNULL((temp.phones || cnds.phones), cnds.phones) phones,
            IFNULL(
                (temp.usernames || cnds.usernames),
                cnds.usernames
            ) usernames,
            -- IFNULL((temp.fbps || cnds.fbps), cnds.fbps) fbps,
            IFNULL((temp.gids || cnds.gids), cnds.gids) gids,
        FROM
            next_day_result cnds
            LEFT JOIN `$groups_table` temp ON temp.group_id = cnds.group_id
    ),
    -- 由於 contact_res 結果中的各個屬性值中，可能會有重複的值
    -- 怕將來不段重複累積，可能會過長，因此要做一次 DISTINCT，保留唯一值
    DISTINST_res AS (
        SELECT
            group_id,
            group_time,
            ARRAY(SELECT DISTINCT p FROM contact_res.permanents p) permanents,
            ARRAY(SELECT DISTINCT em FROM contact_res.emails em) emails,
            ARRAY(SELECT DISTINCT ph FROM contact_res.phones ph) phones,
            ARRAY(SELECT DISTINCT un FROM contact_res.usernames un) usernames,
            -- ARRAY(SELECT DISTINCT fbp FROM contact_res.fbps fbp) fbps,
            ARRAY(SELECT DISTINCT gid FROM contact_res.gids gid) gids,
        FROM
            contact_res
    )
    -- 最終使用 DISTINST_res 的結果來進行 MERGE
    SELECT * FROM DISTINST_res
) Q ON T.group_id = Q.group_id
WHEN NOT MATCHED THEN
    INSERT(group_id, group_time, permanents, emails, phones, usernames, -- fbps,
            gids)
    VALUES(Q.group_id, Q.group_time, Q.permanents, Q.emails, Q.phones, Q.usernames, -- Q.fbps,
            Q.gids
        )
WHEN MATCHED THEN
    UPDATE SET
        permanents = Q.permanents,
        emails = Q.emails,
        phones = Q.phones,
        usernames = Q.usernames,
        -- fbps = Q.fbps,
        gids = Q.gids
```

- [Flowchart](./../workflow.md#determine-groups-update-grouping-reslut-detail-flow-chart) 中的 Step 1，負責將 [`get_grouping_result_by_date()`](./table_functions.md#get_grouping_result_by_datesql) 產出的結果，加入每天維護的 `$groups_table` 中
- `$groups_table`: 每日維護的 grouping reslut，通常是 `tagtoo-tracking.event_prod.user_unify_groups`
- `$grouping_result_table`: 一般來說，就是想要 merge 進 `$groups_table` 的每日 grouping result，EX：`tagtoo-tracking.event_grouping_result_prod.result-2022-10-01`
- [ARRAY_CONCAT](https://cloud.google.com/bigquery/docs/reference/standard-sql/array_functions#array_concat) 要是有遇到 `NULL`，會回傳 `NULL`，所以要特別處理

### predict_candidate.sql
```sql
WITH 
    -- email 屬性展開
    flatten_by_email AS (
        SELECT group_id, group_time, email
        FROM `$groups_table`, `$groups_table`.emails AS email
    ),
    -- phone 屬性展開
    flatten_by_phone AS (
        SELECT group_id, group_time, phone
        FROM `$groups_table`, `$groups_table`.phones AS phone
    ),
    -- user name 屬性展開
    flatten_by_username AS (
        SELECT group_id, group_time, username
        FROM `$groups_table`, `$groups_table`.usernames AS username
    ),
    -- fbp 屬性展開
    -- flatten_by_fbp AS (
    --   SELECT group_id, group_time, fbp
    --   FROM `$groups_table`, `$groups_table`.fbps AS fbp
    -- ),
    -- gid 屬性展開
    flatten_by_gid AS (
        SELECT group_id, group_time, gid
        FROM `$groups_table`, `$groups_table`.gids AS gid
    ),
    -- permanent 屬性展開，因為有可能同一個 permanent，但卻有其他的不同屬性
    flatten_by_permanent AS (
        SELECT group_id, group_time, permanent
        FROM `$groups_table`, `$groups_table`.permanents AS permanent
    ),
    -- 抓取每日的 entity data，為了之後的預測使用
    entity_data AS (SELECT * FROM `$get_entity_function`('$start_date', '$end_date')),
    -- t_fbp AS (
    --   SELECT permanent, entity_time, group_id,
    --   FROM entity_data LEFT OUTER JOIN flatten_by_fbp ffbp ON ffbp.fbp IS NOT NULL
    --     AND ffbp.fbp = entity_data.fbp
    -- ),
    -- 以下的 t_xxx，目標就是為了找出 entity_data 中的 permanent
    -- 他們跟 `$groups_table` 的哪些群組有一樣的 xxx 屬性
    t_gid AS (
        SELECT permanent, entity_time, group_id,
        FROM
            entity_data
            LEFT OUTER JOIN flatten_by_gid fgid 
            ON fgid.gid IS NOT NULL AND fgid.gid = entity_data.gid
    ),
    t_em AS (
        SELECT permanent, entity_time, group_id,
        FROM
            entity_data
            LEFT OUTER JOIN flatten_by_email fem
            ON fem.email IS NOT NULL AND fem.email = entity_data.em
    ),
    t_ph AS (
        SELECT permanent, entity_time, group_id,
        FROM
            entity_data
            LEFT OUTER JOIN flatten_by_phone fph 
            ON fph.phone IS NOT NULL AND fph.phone = entity_data.ph
    ),
    t_un AS (
        SELECT permanent, entity_time, group_id,
        FROM
            entity_data
            LEFT OUTER JOIN flatten_by_username f_un 
            ON f_un.username IS NOT NULL AND f_un.username = entity_data.un
    ),
    t_permanent AS (
        SELECT entity_data.permanent, entity_time, group_id,
        FROM
            entity_data
            LEFT OUTER JOIN flatten_by_permanent f_pmt 
            ON f_pmt.permanent IS NOT NULL AND f_pmt.permanent = entity_data.permanent
    ),
    -- 將各個屬性比對得到的 group id 結合成一張表，為了之後的聚合使用
    res AS (
        -- SELECT * FROM t_fbp UNION ALL
        SELECT * FROM t_gid UNION ALL
        SELECT * FROM t_ph UNION ALL
        SELECT * FROM t_em UNION ALL
        SELECT * FROM t_un UNION ALL
        SELECT * FROM t_permanent
    )
-- 聚合，找出每個 permanent 可以讓多少個群組合併
-- 會做 IGNORE NULLS 主要是為了保險
-- 理論上在現在的流程中，entity_data 中的 permanent 至少一定會對到一個 group id
-- 基於當前的流程，只隸屬一個 group id 的情況是不需要特別處理的
SELECT
  permanent,
  ARRAY_AGG(DISTINCT group_id IGNORE NULLS) group_ids
FROM
  res
GROUP BY
  1
HAVING
  ARRAY_LENGTH(group_ids) > 1;
```

- [Flowchart](./../workflow.md#determine-groups-update-grouping-reslut-detail-flow-chart) 中的 Step 2，使用每日的 `entity_data` 去跟 `$groups_table` 的各個屬性去做比對，抓出候選的 `group_id`，最後只提取候選 `group_id` 超過一個以上的 permanent
- 每個屬性需要獨自取出分別跟 `entity_data` 做比較，主要是怕要是多個屬性同時比對，可能會出現不預期的邏輯錯誤，一個一個單獨比較，比較可以將問題簡化，而最終再將所有的 `t_xxx` 的結果都 `UNION` 就能將所有屬性抓到的候選 `group_id` 放到同一張暫存表
- `$get_entity_function`: 通常是 [`get_entity_by_date()`](./table_functions.md#get_entity_by_datesql)
- `$groups_table`: 每日維護的 grouping reslut，通常是 `tagtoo-tracking.event_prod.user_unify_groups`
- 流程相關的部分，可以參考 [Flowchart](./../workflow.md#determine-groups-update-grouping-reslut-detail-flow-chart)，而這個 SQL 為何只需要抓取隸屬一個 `group_id` 的 permanent 的相關細項解釋，可以參考 [ISSUE 221 comment](https://github.com/Tagtoo/ad-track/issues/221#issuecomment-1305267593)，在該 comment 中，有對這塊有比較詳細的解釋

### merge_resolved_candidates.sql
```sql
-- Merge result of candidate > 1
MERGE `$groups_table` T USING (
    -- 取出 Union Find 的結果，當前裡面有的欄位有 group_id、predict_result
    -- group_id: 對應 $groups_table 的 group id
    -- predict_result: Union Find 計算出來後，該 group id 最終的歸屬結果
    -- 將此結果跟 $groups_table 做 left join，只將這些要合併的 groups 取出
    WITH Group_attrs AS (
        SELECT
            *
        FROM
            `$intermediate_table` PRE
            LEFT OUTER JOIN `$groups_table` GR ON PRE.group_id = GR.group_id
    ),
    -- 將一樣 predict_result 的做聚合
    Attr_contact AS (
        SELECT
            predict_result,
            ARRAY_CONCAT_AGG(permanents) permanents,
            ARRAY_CONCAT_AGG(emails) emails,
            ARRAY_CONCAT_AGG(phones) phones,
            ARRAY_CONCAT_AGG(usernames) usernames,
            -- ARRAY_CONCAT_AGG(fbps) fbps,
            ARRAY_CONCAT_AGG(gids) gids
        FROM
            Group_attrs
        GROUP BY
            1
    ),
    -- 對 Attr_contact 後得到的各個屬性，做 DISTINCT
    Distinct_res AS (
        SELECT
            predict_result group_id,
            ARRAY(SELECT DISTINCT p FROM Attr_contact.permanents p) permanents,
            ARRAY(SELECT DISTINCT em FROM Attr_contact.emails em) emails,
            ARRAY(SELECT DISTINCT ph FROM Attr_contact.phones ph) phones,
            ARRAY(SELECT DISTINCT un FROM Attr_contact.usernames un) usernames,
            -- ARRAY(SELECT DISTINCT fbp FROM Attr_contact.fbps fbp) fbps,
            ARRAY(SELECT DISTINCT gid FROM Attr_contact.gids gid) gids,
        FROM
            Attr_contact
    )
    -- 使用 Distinct_res 作為合併的依據
    SELECT * FROM Distinct_res
) Q ON T.group_id = Q.group_id
-- 只針對 predict_result 做更新的處理
WHEN MATCHED THEN
UPDATE
SET
    permanents = Q.permanents,
    emails = Q.emails,
    phones = Q.phones,
    usernames = Q.usernames,
    -- fbps = Q.fbps,
    gids = Q.gids;
```

- [Flowchart](./../workflow.md#determine-groups-update-grouping-reslut-detail-flow-chart) 中的 Step 4 Merge，在這步驟中，將 Merge 跟 Delet 分開執行，是為了將來若要做改寫會比較好處理，此 SQL 主要是在更新 Union Find 計算出的最終結果群組資料
- `$groups_table`: 每日維護的 grouping reslut，通常是 `tagtoo-tracking.event_prod.user_unify_groups`
- `$intermediate_table`: Union Find 計算結果暫存 Table，通常是 `tagtoo-tracking.event_prod._intermediate_predict`

### delete_useless_group.sql
```sql
-- Delete useless rows after the merging process
-- 將不是 predict_result 的 group id 資料都刪除
DELETE `$groups_table`
WHERE
    group_id IN (SELECT group_id FROM `$intermediate_table` WHERE group_id != predict_result);
```

- [Flowchart](./../workflow.md#determine-groups-update-grouping-reslut-detail-flow-chart) 中的 Step 4 Delete，流程上，會先進行 Merge 再執行此步驟，因為當 Merge 之後，這些資料就不再必要，為了避免影響下次的運算，都要刪除
- `$groups_table`: 每日維護的 grouping reslut，通常是 `tagtoo-tracking.event_prod.user_unify_groups`
- `$intermediate_table`: Union Find 計算結果暫存 Table，通常是 `tagtoo-tracking.event_prod._intermediate_predict`