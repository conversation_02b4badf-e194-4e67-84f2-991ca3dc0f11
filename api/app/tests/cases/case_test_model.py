from pydantic import ValidationError

import pytest

from app.schemas.enums import PaymentMethodEnum


class TagtooEventModelOKCases(object):
    def case_simple(self):
        return {
            'name': 'add_to_cart',
            'value': 333.0,
            'currency': 'TWD',
            'items': [],
        }

    def case_with_optional_prop(self):
        return {
            'name': 'add_payment_info',
            'currency': 'TWD',  # Optional property
            'value': 33,  # Optional property
            'custom_data': {
                'payment_method': PaymentMethodEnum.CREDIT_CARD,
            },
        }

    def case_multiple_condition_props(self):
        return {
            'name': 'focus',
            'custom_data': {
                # Should only contain either 'status' or 'focus_minutes' when event='focus'
                'status': True,
            },
        }


@pytest.mark.xfail(raises=ValidationError)
class TagtooEventModelFailedCases(object):
    def case_event_level_missing_prop(self):
        return {
            'name': 'add_to_cart',
            'value': 333.0,
            'currency': 'TWD',
            # Missing 'items'
        }

    def case_event_level_redundant_prop(self):
        return {
            'name': 'refund',
            'value': 333.0,
            'currency': 'TWD',
            'items': {},  # 'items' is redundant
            'custom_data': {
                'order_id': '123456',
            },
        }

    def case_custom_data_missing_prop(self):
        return {
            'name': 'refund',
            'value': 333.0,
            'currency': 'TWD',
            # 'custom_data.order_id is missing
        }

    def case_custom_data_redundant_prop(self):
        return {
            'name': 'add_to_cart',
            'value': 333.0,
            'currency': 'TWD',
            'items': {},
            'custom_data': {
                'i_am_redundant': 'value_dont_care',  # Redundant custom_data key
            },
        }

    def case_event_level_and_custom_data_mix(self):
        return {
            # 'currency' and 'value' are missing
            'name': 'refund',
            'redundant_event_level': '',  # Redundant event level key
            'custom_data': {
                'i_am_redundant': 'value_dont_care',  # Redundant custom_data key
            },
        }

    def case_multiple_condition_props(self):
        return {
            'name': 'focus',
            'custom_data': {
                # Should only contain either 'status' or 'focus_minutes' when event='focus'
                'status': True,
                'focus_minutes': 30,
            },
        }

    def case_no_propert_allowed(self):
        return {
            'name': 'share',
            'value': 333,  # Redundant property. Event 'share' did not allow any properties
        }

    def case_contain_properties_neither_required_nor_optional(self):
        return {
            'name': 'add_payment_info',
            'currency': 'TWD',  # Optional property
            'value': 33,  # Optional property
            'custom_data': {
                'payment_method': PaymentMethodEnum.CREDIT_CARD,
                # 'search_string' is neither required nor optional property for event 'add_payment_info'
                'search_string': 'abcdefg',
            },
        }
