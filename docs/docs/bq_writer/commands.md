## Pub/Sub Subscriber

Run a Pub/Sub subscriber by given name. 

```
python subscribe.py [-h] 
                    [--version {v1}] 
                    [--max-messages MAX_MESSAGES]
                    [--timeout TIMEOUT] {tagtoo_event}
```

**Instances**

|Subscriber Name|Type|Description|
|---|---|---|
|tagtoo_event|asynchronous pull|Transform and streaming insert Tagtoo events into the BigQuery table.|
