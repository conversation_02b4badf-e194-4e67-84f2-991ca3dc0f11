FROM debian:bookworm-slim

# 設定版本參數
ENV TERRAFORM_VERSION=1.11.4 \
    CLOUD_SDK_VERSION=435.0.1 \
    KUBECTL_VERSION=1.28.8

# 安裝系統套件
RUN apt-get update && apt-get install -y --no-install-recommends \
    ca-certificates \
    curl \
    git \
    openssh-client \
    python3 \
    python3-crcmod \
    unzip \
    wget \
    && rm -rf /var/lib/apt/lists/*

# 安裝 Terraform
RUN wget -q https://releases.hashicorp.com/terraform/${TERRAFORM_VERSION}/terraform_${TERRAFORM_VERSION}_linux_amd64.zip \
    && unzip terraform_${TERRAFORM_VERSION}_linux_amd64.zip -d /usr/local/bin \
    && rm terraform_${TERRAFORM_VERSION}_linux_amd64.zip

# 安裝 gcloud SDK
RUN curl -O https://dl.google.com/dl/cloudsdk/channels/rapid/downloads/google-cloud-sdk-${CLOUD_SDK_VERSION}-linux-x86_64.tar.gz \
    && tar xzf google-cloud-sdk-${CLOUD_SDK_VERSION}-linux-x86_64.tar.gz \
    && rm google-cloud-sdk-${CLOUD_SDK_VERSION}-linux-x86_64.tar.gz \
    && ln -s /lib /lib64

# 安裝 kubectl
RUN curl -LO "https://dl.k8s.io/release/v${KUBECTL_VERSION}/bin/linux/amd64/kubectl" \
    && install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl

ENV PATH /google-cloud-sdk/bin:$PATH

# 驗證安裝
RUN terraform -version \
    && gcloud -v \
    && kubectl version --client

# Terraform and xterrafile/yamllint to handle terraform modules

WORKDIR /src