<script src="https://unpkg.com/mermaid@8.13.3/dist/mermaid.min.js"></script>

## Docker Compose

Event API microservices is depends on Event Config database. Please compose these services locally in a specific order.

```mermaid
flowchart TB
  subgraph docker bridge network
    subgraph network:config
        config-api --- config-db[(config-db)]
    end
    subgraph network:api
        api -- publish --> api-pubsub
        api -- query --> api-db[(api-db)]
        api-db -- subscribe --> api-worker
    end
    config-db -. sync .-> api-db
  end
  config-api o-. 10001:8000 .-o Host
  api o-. 10002:8000 .-o Host
```

1. Start the event config services
   ```shell
   docker-compose -f docker-compose.config.yml up
   ```
2. Start the api services
   ```shell
   docker-compose -f docker-compose.api.yml up
   ```
3. Turn down the services
   ```shell
   docker-compose -f docker-compose.api.yml down
   docker-compose -f docker-compose.config.yml down
   ```
   
!!! info
    The Pub/Sub local service is an emulator built from `messagebird/gcloud-pubsub-emulator`.

## Test

Use shell scripts to test across services. Start the services and run these commands at `tests/api` folder:

```shell
./test_all.sh
```
