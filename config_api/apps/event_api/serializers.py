from rest_framework import serializers
from .models import AccessToken, Client


class ClientReadSerializer(serializers.ModelSerializer):
    class Meta:
        model = Client
        fields = ('client_id', 'name', 'channel_type', 'is_internal')


class AccessTokenReadSerializer(serializers.ModelSerializer):
    client = ClientReadSerializer()
    scopes = serializers.StringRelatedField(many=True)

    class Meta:
        model = AccessToken
        fields = ('ec_id', 'token', 'client', 'scopes')
