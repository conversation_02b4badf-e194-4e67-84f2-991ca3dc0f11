## IP2LOCATION MYSQL DATABASE

In order to resolve ip address to location information, we use IP2LOCATION database service.

For develop, use lite database from https://lite.ip2location.com. For production, use paid database from https://ip2location.com.

### Login Information

* lite.ip2location.com: `<EMAIL>` / `wBuQ\_PYJg?8vXk?`
* ip2location.com: `<EMAIL>` / `wBuQ\_PYJg?8vXk?`

### Customized build

The official docker image ip2location/mysql download the database then initialize when **run** the container. It is a 
long-running process and might fail if exceed the download quota. Therefore, we split the build into two stage:

* Stage1: 1) Download 2) Initialize 3) Dump using the ip2location official image
* Stage2: Load the sql file dumped by Stage1, using mariadb image

### Update the database

IP2Location database update their database **monthly**, after the update, we should rebuild this image with `--no-cache`
argument, then perform a rolling update to the applications which using this image. 

### Build Command

This command will generate a new production-ready image which prevent caching and guarantee the data is re-downloaded 
from the source.

```
$ make build-ip2location
```