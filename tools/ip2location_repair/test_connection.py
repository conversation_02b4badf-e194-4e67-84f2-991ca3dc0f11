#!/usr/bin/env python
import os
import sys
import time
import logging
import pymysql  # 使用 pymysql 代替 mariadb，更容易安裝
from google.cloud import bigquery
from datetime import datetime
from dataclasses import dataclass

# 設定 logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_connection.log'),
        logging.StreamHandler()
    ]
)

@dataclass
class Location:
    country_code: str
    region_name: str
    city_name: str
    latitude: float
    longitude: float
    zip_code: str

def test_ip2location():
    """測試 IP2Location 連接和查詢"""
    logging.info("開始測試 IP2Location 連接...")

    # 從環境變數獲取配置
    mysql_host = os.environ.get('MYSQL_HOST', 'ip2location')
    mysql_port = int(os.environ.get('MYSQL_PORT', 3306))
    mysql_user = os.environ.get('MYSQL_USER', 'root')
    mysql_password = os.environ.get('MYSQL_PASSWORD', '')
    mysql_database = os.environ.get('MYSQL_DATABASE', 'ip2location_database')

    logging.info(f"使用 MariaDB 配置: 主機={mysql_host}, 端口={mysql_port}, 用戶={mysql_user}, 資料庫={mysql_database}")

    # 最多重試 3 次
    for attempt in range(3):
        try:
            logging.info(f"連接嘗試 {attempt+1}/3...")
            conn = pymysql.connect(
                user=mysql_user,
                host=mysql_host,
                port=mysql_port,
                password=mysql_password,
                database=mysql_database,
                connect_timeout=10
            )
            logging.info("✅ 成功連接到 MariaDB")

            # 測試查詢
            cursor = conn.cursor()
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            logging.info(f"資料庫中的表: {[table[0] for table in tables]}")

            return True
        except Exception as e:
            logging.error(f"連接失敗: {e}")
            if attempt < 2:
                wait_time = 5 * (attempt + 1)
                logging.info(f"等待 {wait_time} 秒後重試...")
                time.sleep(wait_time)
            else:
                logging.error("達到最大重試次數，測試失敗")
                return False

def test_bigquery():
    """測試 BigQuery 連接和查詢"""
    logging.info("開始測試 BigQuery 連接...")

    if 'GOOGLE_APPLICATION_CREDENTIALS' not in os.environ:
        logging.error("❌ GOOGLE_APPLICATION_CREDENTIALS 環境變數未設定")
        return False

    try:
        # 建立 BigQuery 客戶端
        client = bigquery.Client(location='asia-east1')
        logging.info("✅ 成功連接到 BigQuery")

        # 查詢少量的缺失位置的 IP
        query = """
        SELECT DISTINCT ip_address
        FROM `tagtoo-tracking.event_prod.tagtoo_event`
        WHERE event_time BETWEEN '2025-02-17' AND '2025-03-27'
        AND location IS NULL
        AND ip_address IS NOT NULL
        LIMIT 5
        """

        query_job = client.query(query)
        results = list(query_job)

        if results:
            logging.info(f"✅ 從 BigQuery 查詢到 {len(results)} 個 IP 樣本:")
            for row in results:
                logging.info(f"IP: {row.ip_address}")
        else:
            logging.warning("⚠️ 沒有找到任何缺少位置的 IP")

        # 測試創建暫存資料集
        try:
            schema_query = "CREATE SCHEMA IF NOT EXISTS `tagtoo-tracking.temp`"
            job = client.query(schema_query)
            job.result()
            logging.info("✅ 成功建立或確認暫存資料集存在")
        except Exception as e:
            logging.error(f"❌ 建立暫存資料集失敗: {e}")
            return False

        return True
    except Exception as e:
        logging.error(f"❌ BigQuery 測試失敗: {e}")
        return False

def combined_test():
    """執行完整的連接測試和少量 IP 的修復測試"""
    logging.info("=== 開始執行環境測試 ===")

    # 測試 BigQuery 連接
    if not test_bigquery():
        logging.error("❌ BigQuery 連接測試失敗，無法繼續")
        return False

    # 測試 IP2Location 連接
    if not test_ip2location():
        logging.error("❌ IP2Location 連接測試失敗，無法繼續")
        return False

    logging.info("✅ 所有連接測試成功")

    # 測試完整流程（使用少量資料）
    try:
        logging.info("開始測試少量 IP 的定位流程...")

        # 獲取一些測試用 IP
        client = bigquery.Client(location='asia-east1')
        query = """
        SELECT DISTINCT ip_address
        FROM `tagtoo-tracking.event_prod.tagtoo_event`
        WHERE event_time BETWEEN '2025-02-17' AND '2025-03-27'
        AND location IS NULL
        AND ip_address IS NOT NULL
        LIMIT 10
        """

        query_job = client.query(query)
        test_ips = [row.ip_address for row in query_job]

        if not test_ips:
            logging.warning("⚠️ 無法取得測試 IP")
            return False

        # 從環境變數獲取配置
        mysql_host = os.environ.get('MYSQL_HOST', 'localhost')
        mysql_port = int(os.environ.get('MYSQL_PORT', 3306))
        mysql_user = os.environ.get('MYSQL_USER', 'root')
        mysql_password = os.environ.get('MYSQL_PASSWORD', '')
        mysql_database = os.environ.get('MYSQL_DATABASE', 'ip2location_database')

        # 連接 IP2Location 並查詢位置
        conn = pymysql.connect(
            user=mysql_user,
            host=mysql_host,
            port=mysql_port,
            password=mysql_password,
            database=mysql_database,
            connect_timeout=15  # 增加連接超時
        )
        cursor = conn.cursor()

        # 找到正確的表名
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        table_name = 'ip2location_database'  # 默認值

        for table in tables:
            if 'ip2location' in table[0].lower():
                table_name = table[0]
                break

        logging.info(f"使用表: {table_name}")

        # 檢查表結構
        cursor.execute(f"DESCRIBE `{table_name}`")
        columns = cursor.fetchall()
        column_names = [col[0] for col in columns]
        logging.info(f"表 {table_name} 的結構: {column_names}")

        success_count = 0
        for ip in test_ips:
            try:
                cursor.execute(
                    f"""
                    SELECT
                        `country_code`,
                        `region_name`,
                        `city_name`,
                        `latitude`,
                        `longitude`,
                        `zip_code`
                    FROM `{table_name}`
                    WHERE INET_ATON(%s) <= ip_to
                    LIMIT 1
                    """,
                    (ip,)
                )
                result = cursor.fetchone()
                if result and result[0] != '-':
                    success_count += 1
                    logging.info(f"✅ 成功取得 IP {ip} 的位置資訊: {result[0]}, {result[2]}")
                else:
                    logging.warning(f"⚠️ 找不到 IP {ip} 的有效位置資訊")
            except Exception as e:
                logging.error(f"❌ 查詢 IP {ip} 時出錯: {e}")

        conn.close()

        logging.info(f"測試結果: 成功查詢 {success_count}/{len(test_ips)} 個 IP 的位置資訊")
        if success_count > 0:
            logging.info("✅ 流程測試成功，可以開始完整的修復程序")
            return True
        else:
            logging.error("❌ 流程測試失敗，請檢查 IP2Location 資料庫設定")
            return False

    except Exception as e:
        logging.error(f"❌ 流程測試失敗: {e}")
        return False

if __name__ == "__main__":
    start_time = datetime.now()
    logging.info(f"測試開始時間: {start_time}")

    result = combined_test()

    end_time = datetime.now()
    duration = end_time - start_time
    logging.info(f"測試完成時間: {end_time}，耗時: {duration}")

    if result:
        logging.info("🎉 總結: 所有測試通過，環境準備就緒")
        sys.exit(0)
    else:
        logging.error("❌ 總結: 測試失敗，請解決上述問題後再繼續")
        sys.exit(1)
