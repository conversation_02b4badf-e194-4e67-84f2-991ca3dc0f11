# LTA 智能 Hash 處理測試

## 功能概述

LTA (Label Target Audience) Loader 的智能電話號碼 hash 處理功能，能夠自動檢測已經 hash 的電話號碼，避免重複 hash 處理，同時維持與舊資料的完全相容性。

## 核心改進

### 1. 智能 Hash 檢測
- **自動識別**: 檢測 64 字符的十六進位字串作為 SHA256 hash
- **效能提升**: 混合資料情況下可提升 43.4% 處理效能
- **向後相容**: 100% 相容現有資料處理邏輯

### 2. 新增方法

```python
def _is_already_hashed(self, value: str) -> bool:
    """檢測是否已經是 SHA256 hash"""
    
def _safe_encode_phone(self, phone: str) -> str:
    """智能電話號碼編碼"""
```

## 測試架構

### 測試檔案結構

```
tests/s2s/LTA/
├── __init__.py
├── test_loader.py              # 主測試檔案（使用真實 DataLoader）
├── test_loader_standalone.py   # 獨立測試檔案（模擬環境）
├── phone_hash_verification.py  # Hash 驗證腳本
└── simple_phone_test.py        # 簡單效能測試
```

### 主測試檔案 (test_loader.py)

**特色**:
- 使用真實的 DataLoader 類別
- 在完整 Docker Compose 環境中執行
- 包含 9 個綜合測試案例

**執行命令**:
```shell
docker-compose -f docker-compose.s2s.yml run --rm \
  -v "$(pwd)/tests/s2s/LTA:/app/tests_lta" \
  s2s python -m pytest tests_lta/test_loader.py -v
```

### 獨立測試檔案 (test_loader_standalone.py)

**特色**:
- 不依賴完整 S2S 環境
- 包含額外的效能一致性測試
- 可在任何 Docker 環境中執行

**執行命令**:
```shell
docker-compose -f docker-compose.s2s.yml run --rm \
  -v "$(pwd)/tests/s2s/LTA/test_loader_standalone.py:/app/test_loader_standalone.py" \
  s2s python -m pytest test_loader_standalone.py -v
```

## 詳細測試案例

### 1. Hash 檢測測試

**測試案例**: `test_is_already_hashed_valid_hash`
```python
valid_hashes = [
    'cf676475cec2ee7b7591f39bdefe7ef3dca63b14c1cdba3b19ce4219cbda2b09',  # 小寫
    'CF676475CEC2EE7B7591F39BDEFE7EF3DCA63B14C1CDBA3B19CE4219CBDA2B09',  # 大寫
    '0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef',  # 邊界
]
```

**測試案例**: `test_is_already_hashed_invalid_hash`
```python
invalid_values = [
    '886912345678',           # 原始電話號碼
    '+886-912-345-678',       # 格式化電話號碼
    'invalid_hash_string',    # 無效字符
    '',                       # 空字串
    123,                      # 非字串類型
    None,                     # None 值
]
```

### 2. 智能編碼測試

**已 Hash 號碼處理**:
```python
def test_safe_encode_phone_already_hashed(self):
    hashed_phone = 'cf676475cec2ee7b7591f39bdefe7ef3dca63b14c1cdba3b19ce4219cbda2b09'
    result = self.loader._safe_encode_phone(hashed_phone)
    self.assertEqual(result, hashed_phone.lower())  # 應該保持不變（轉小寫）
```

**原始號碼處理**:
```python
test_cases = [
    ('886912345678', '886912345678'),           # 純數字
    ('+886-912-345-678', '886912345678'),       # 含符號
    ('(886) 912 345 678', '886912345678'),      # 含括號空格
]
```

### 3. AVRO 資料處理測試

**混合資料測試**:
```python
test_user_data = [{
    'phones': [
        '886912345678',                                                         # 原始號碼
        'cf676475cec2ee7b7591f39bdefe7ef3dca63b14c1cdba3b19ce4219cbda2b09',   # 已 hash
        '+886-987-654-321',                                                     # 格式化號碼
    ],
    # ... 其他資料
}]
```

### 4. 整合測試

**一致性驗證**:
```python
def test_integration_real_hash_function(self):
    original_phone = '886912345678'
    expected_hash = 'cf676475cec2ee7b7591f39bdefe7ef3dca63b14c1cdba3b19ce4219cbda2b09'
    
    # 測試原始號碼 hash
    result1 = self.loader._safe_encode_phone(original_phone)
    self.assertEqual(result1, expected_hash)
    
    # 測試已 hash 號碼處理
    result2 = self.loader._safe_encode_phone(expected_hash)
    self.assertEqual(result2, expected_hash)
    
    # 驗證不會產生雙重 hash
    self.assertEqual(result1, result2)
```

## 效能驗證

### 測試結果

根據 `simple_phone_test.py` 的測試結果：

```
=== 效能比較結果 ===
原始處理時間: 0.001234 秒 (全部原始號碼)
智能處理時間: 0.000698 秒 (混合資料)
效能提升: 43.4%
```

### 混合資料場景

在包含 50% 已 hash 資料的混合場景中，智能處理功能顯著提升效能：
- **跳過不必要的 hash 計算**
- **減少 CPU 使用率**
- **提升整體處理速度**

## 測試環境要求

### Docker Compose 配置

確保以下服務正常運行：
```yaml
services:
  s2s:
    container_name: s2s
    image: ad_track_s2s
    env_file: ./s2s/.env
    environment:
      - GOOGLE_APPLICATION_CREDENTIALS=/credentials/event-s2s-vm.json
      - PUBSUB_EMULATOR_HOST=s2s-pubsub:8681
  
  s2s_db:
    container_name: s2s-db
    image: redis:6-alpine
  
  s2s_broker:
    container_name: s2s-broker
    image: rabbitmq:3.8-alpine
  
  s2s_pubsub:
    container_name: s2s-pubsub
    image: messagebird/gcloud-pubsub-emulator
```

### 環境變數

測試使用的關鍵環境變數（來自 `s2s/.env`）：
```bash
MODE=test
GCP_PROJECT_ID=tagtoo-tracking
FACEBOOK_CAPI_TOPIC_CAPI_COMPRESSED=capi-compressed
LTA_FACEBOOK_TOPIC=lta-fb
# ... 其他必要變數
```

## 故障排除

### 常見問題

1. **ImportError: No module named 's2s.s2s'**
   - 解決方案：測試檔案已包含多重導入路徑嘗試
   - 確保在 Docker 環境中執行

2. **環境變數未設定**
   - 確保 `s2s/.env` 檔案存在
   - 檢查 docker-compose 配置

3. **服務依賴問題**
   - 按順序啟動：Redis → RabbitMQ → Pub/Sub → S2S
   - 等待服務完全啟動後再執行測試

### 除錯技巧

**查看容器日誌**:
```shell
docker-compose -f docker-compose.s2s.yml logs s2s
```

**進入容器除錯**:
```shell
docker-compose -f docker-compose.s2s.yml run --rm s2s bash
```

**手動測試核心功能**:
```shell
docker-compose -f docker-compose.s2s.yml run --rm s2s python -c "
from s2s.LTA.loader import DataLoader
from unittest.mock import Mock
loader = DataLoader()
loader._logger = Mock()
print('Hash檢測:', loader._is_already_hashed('cf676475cec2ee7b7591f39bdefe7ef3dca63b14c1cdba3b19ce4219cbda2b09'))
print('智能編碼:', loader._safe_encode_phone('886912345678')[:16] + '...')
"
```