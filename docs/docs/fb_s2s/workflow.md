<script src="https://unpkg.com/mermaid@8.13.3/dist/mermaid.min.js"></script>

## Flowchart

```mermaid
flowchart LR
    A[Compressor] --> B(Batch Operator)
    B --> C{Request Success?}
    C -->|n| E[Decompressor]
    C -->|y| Z[End]
    E --> F(Singleton Operator)
    F --> G{Request Success?}
    G -->|n| H(Error Reporting)
    G -->|y| Z
```

## Compression for Batch

Facebook Conversion API has a request quota(~=100). If we only post event in singleton mode (1 event per request), the
request might be blocked by Facebook(503 Service Unavailable), more over, when the producing rate greater than consuming 
rate, the messages will keep accumulating in the Pub/Sub subscription until they reach the TTL.

Therefore, we use Apache Beam's Map/Reduce flow to compress the messages which are:

1. In the same [Fixed Time Window](https://beam.apache.org/documentation/programming-guide/#fixed-time-windows).
2. Has same `ec_id` attribute.
3. Has same `version` attribute.

The interval of the window is adjustable. If the current interval (5 seconds) still lead to the request quota, please
extend the interval second.

??? Reference "dataflow/fb_capi_compressor.py"
    ```python hl_lines="41-52 74 76"
    --8<-- "../dataflow/fb_capi_compressor.py"
    ```

## Decompression for Singleton

For a failed batch CAPI request, it's impossible to tell which event cause the error. Therefore, we need to break
down the batch and resend event one-by-one.

We handle this case using a MQTT worker, which will de-compress the event batch into single event and publish them
to the singleton channel.

??? Reference "s2s/s2s/facebook_capi/tasks.py"
    ```python hl_lines="15-27"
    --8<-- "../s2s/s2s/facebook_capi/tasks.py"
    ```