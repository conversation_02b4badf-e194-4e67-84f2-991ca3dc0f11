locals {
  user_unify_prefix = "user-unify"
  user_unify_env    = "${local.user_unify_prefix}-env"
}

resource "kubernetes_config_map_v1" "user_unify_env" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = local.user_unify_env
  }
  data = {
    GCP_PROJECT                           = var.gcp_project
    BQ_DATASET                            = google_bigquery_dataset.event.dataset_id
    BQ_GROUPING_RESULT_DATASET            = google_bigquery_dataset.event_grouping_result.dataset_id
    BQ_SNAPSHOT_DATASET                   = google_bigquery_dataset.snapshot.dataset_id
    BQ_TABLE_USER_UNIFY_GROUPS            = google_bigquery_table.user_unify_groups.table_id
    BQ_TABLE_USER_UNIFY_GROUP_PERMANENT   = google_bigquery_table.user_unify_group_permanent.table_id
    BQ_TABLE_PREDICT_INTERMEDIATE         = google_bigquery_table.user_unify__intermediate_predict.table_id
    BQ_TABLE_FUNCTION_GET_ENTITY          = google_bigquery_routine.table_function_get_entity_by_date.routine_id
    BQ_TABLE_FUNCTION_GET_GROUPING_RESULT = google_bigquery_routine.table_function_get_grouping_result_by_date.routine_id
  }
}

resource "kubernetes_cron_job_v1" "user_unify_candidate_prediction_job" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = "user-unify-candidate-prediction-job"
    labels = {
      "app.kubernetes.io/name" = "user-unify-candidate-prediction-job"
      "application"            = "event"
      "component"              = "user-unify"
    }
  }
  spec {
    schedule = "30 16 * * *"
    job_template {
      metadata {
        labels = {
          "app.kubernetes.io/name" = local.user_unify_prefix
          "application"            = "event"
          "component"              = "user-unify"
        }
      }
      spec {
        backoff_limit = 2
        template {
          metadata {}
          spec {
            container {
              image   = "asia.gcr.io/tagtoo-tracking/event-user-unify:${var.user_unify.candidate_prediction_job.image_tag}"
              name    = "user-unify-candidate-prediction-job"
              command = ["python", "update_grouping_result.py"]
              env_from {
                config_map_ref {
                  name = kubernetes_config_map_v1.user_unify_env.metadata[0].name
                }
              }
              resources {
                limits = {
                  cpu    = var.user_unify.candidate_prediction_job.limits.cpu
                  memory = var.user_unify.candidate_prediction_job.limits.memory
                }
                requests = {
                  cpu    = var.user_unify.candidate_prediction_job.requests.cpu
                  memory = var.user_unify.candidate_prediction_job.requests.memory
                }
              }
            }
            node_selector = {
              "application" = "event"
              "name"        = "user-unify-job"
            }
            toleration {
              effect   = "NoSchedule"
              key      = "name"
              operator = "Equal"
              value    = "user-unify-job"
            }
          }
        }
      }
    }
  }
}
