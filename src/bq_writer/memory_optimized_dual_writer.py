"""
記憶體最佳化的雙寫入器

實施改良版序列式雙寫入策略，支援非阻塞次要寫入和智慧重試機制。
基於 tracking_id 核心策略，無需額外表格。

版本: v2.1 (tracking_id 核心策略)
日期: 2025-08-18
"""

import asyncio
import os
import random
import time
import logging
from typing import Dict, Any, Optional
from datetime import datetime

from .optimized_integrated_event_transformer import OptimizedIntegratedEventTransformer

logger = logging.getLogger(__name__)


class ObjectPool:
    """簡單的物件池實作，減少 GC 壓力"""

    def __init__(self, max_size: int = 100):
        self.pool = []
        self.max_size = max_size

    def get(self) -> dict:
        """從池中獲取物件"""
        if self.pool:
            return self.pool.pop()
        return {}

    def put(self, obj: dict):
        """將物件歸還到池中"""
        if len(self.pool) < self.max_size:
            obj.clear()
            self.pool.append(obj)


class BackgroundIntegratedWriter:
    """背景 integrated_event 寫入器"""

    def __init__(self, queue: asyncio.Queue, bq_writer):
        self.queue = queue
        self.bq_writer = bq_writer
        self.running = False
        self.task = None

    async def start(self):
        """啟動背景寫入任務"""
        if not self.running:
            self.running = True
            self.task = asyncio.create_task(self._process_queue())
            logger.info("背景 integrated_event 寫入器已啟動")

    async def stop(self):
        """停止背景寫入任務"""
        self.running = False
        if self.task:
            self.task.cancel()
            try:
                await self.task
            except asyncio.CancelledError:
                pass
            logger.info("背景 integrated_event 寫入器已停止")

    async def _process_queue(self):
        """處理寫入佇列"""
        while self.running:
            try:
                # 等待佇列中的項目，超時 1 秒
                item = await asyncio.wait_for(self.queue.get(), timeout=1.0)
                await self._write_integrated_event(item)
                self.queue.task_done()
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"背景寫入處理失敗: {e}")

    async def _write_integrated_event(self, item: dict):
        """寫入 integrated_event"""
        try:
            data = item['data']
            message_id = item['message_id']
            retry_count = item.get('retry_count', 0)

            # 執行寫入
            errors = await self.bq_writer.insert_rows_json("integrated_event", [data])

            if errors:
                logger.warning(f"integrated_event 寫入錯誤: {errors}, message_id: {message_id}")
                # 重試邏輯
                if retry_count < 3:
                    item['retry_count'] = retry_count + 1
                    await asyncio.sleep(2 ** retry_count)  # 指數退避
                    await self.queue.put(item)
                else:
                    logger.error(f"integrated_event 寫入最終失敗: {message_id}")
            else:
                logger.debug(f"integrated_event 寫入成功: {message_id}")

        except Exception as e:
            logger.error(f"integrated_event 寫入異常: {e}")


class MemoryOptimizedDualWriter:
    """記憶體最佳化的雙寫入器"""

    def __init__(self, bq_writer):
        """
        初始化雙寫入器

        Args:
            bq_writer: BigQuery 寫入器實例
        """
        self.bq_writer = bq_writer
        self.transformer = OptimizedIntegratedEventTransformer()

        # 物件池減少 GC 壓力
        self.transform_pool = ObjectPool(max_size=100)

        # 非同步佇列處理背景寫入
        queue_maxsize_env = os.environ.get("INTEGRATED_QUEUE_MAXSIZE", "2000")
        try:
            queue_maxsize = int(queue_maxsize_env)
        except ValueError:
            logger.warning(f"Invalid INTEGRATED_QUEUE_MAXSIZE value '{queue_maxsize_env}', using default 2000")
            queue_maxsize = 2000
        self.integrated_queue = asyncio.Queue(maxsize=queue_maxsize)
        self.background_writer = BackgroundIntegratedWriter(self.integrated_queue, bq_writer)
        logger.info(f"integrated_queue maxsize set to: {queue_maxsize}")

        # 功能開關
        self.integrated_write_enabled = os.environ.get(
            "INTEGRATED_WRITE_ENABLED", "false"
        ).lower() == "true"

        # 採樣率控制 (漸進式部署)
        self.sample_rate = float(os.environ.get("INTEGRATED_WRITE_SAMPLE_RATE", "1.0"))

        # 統計資訊
        self.stats = {
            "total_messages": 0,
            "primary_success": 0,
            "primary_failed": 0,
            "integrated_queued": 0,
            "integrated_skipped": 0,
            "queue_full_drops": 0
        }

        logger.info(f"MemoryOptimizedDualWriter 初始化完成")
        logger.info(f"integrated_write_enabled: {self.integrated_write_enabled}")
        logger.info(f"sample_rate: {self.sample_rate}")

    async def start(self):
        """啟動雙寫入器"""
        await self.background_writer.start()

    async def stop(self):
        """停止雙寫入器"""
        await self.background_writer.stop()

    async def dual_write(self, message, tagtoo_data: dict) -> bool:
        """
        執行雙寫入邏輯

        Args:
            message: Pub/Sub 訊息物件
            tagtoo_data: tagtoo_event 格式的資料

        Returns:
            是否成功處理
        """
        self.stats["total_messages"] += 1

        try:
            # 1. 主要寫入 (tagtoo_event) - 同步
            primary_success = await self._write_tagtoo_event(tagtoo_data)
            if not primary_success:
                self.stats["primary_failed"] += 1
                message.nack()
                return False

            self.stats["primary_success"] += 1

            # 2. 立即 ACK 主要寫入成功
            message.ack()

            # 3. 次要寫入 (integrated_event) - 非阻塞
            if self._should_process_integrated_write(tagtoo_data):
                await self._queue_integrated_write(tagtoo_data, message.message_id)
                self.stats["integrated_queued"] += 1
            else:
                self.stats["integrated_skipped"] += 1

            return True

        except Exception as e:
            logger.error(f"雙寫入處理失敗: {e}")
            self.stats["primary_failed"] += 1
            message.nack()
            return False

    async def _write_tagtoo_event(self, tagtoo_data: dict) -> bool:
        """
        寫入 tagtoo_event (主要寫入)

        Args:
            tagtoo_data: 資料

        Returns:
            是否成功
        """
        try:
            errors = await self.bq_writer.insert_rows_json("tagtoo_event", [tagtoo_data])
            if errors:
                logger.error(f"tagtoo_event 寫入失敗: {errors}")
                return False
            return True
        except Exception as e:
            logger.error(f"tagtoo_event 寫入異常: {e}")
            return False

    def _should_process_integrated_write(self, tagtoo_data: dict) -> bool:
        """
        判斷是否應該處理 integrated_event 寫入

        Args:
            tagtoo_data: 資料

        Returns:
            是否應該處理
        """
        if not self.integrated_write_enabled:
            return False

        # 採樣率控制
        if random.random() > self.sample_rate:
            return False

        # 事件類型過濾
        event_name = tagtoo_data.get("event", {}).get("name")
        return self.transformer.should_write_to_integrated(event_name)

    async def _queue_integrated_write(self, tagtoo_data: dict, message_id: str):
        """
        將 integrated_event 寫入加入佇列

        Args:
            tagtoo_data: 原始資料
            message_id: 訊息 ID
        """
        try:
            # 轉換資料
            integrated_data = self.transformer.transform(tagtoo_data, message_id)

            # 非阻塞加入佇列
            queue_item = {
                'data': integrated_data,
                'message_id': message_id,
                'retry_count': 0,
                'timestamp': time.time()
            }

            self.integrated_queue.put_nowait(queue_item)

        except asyncio.QueueFull:
            # 佇列滿時的降級策略
            self.stats["queue_full_drops"] += 1
            logger.warning(f"Integrated write queue full, dropping message {message_id}")
        except Exception as e:
            logger.error(f"Failed to queue integrated write: {e}")

    def get_stats(self) -> dict:
        """獲取統計資訊"""
        stats = self.stats.copy()
        stats["queue_size"] = self.integrated_queue.qsize()
        stats["success_rate"] = (
            self.stats["primary_success"] / max(self.stats["total_messages"], 1) * 100
        )
        return stats

    def reset_stats(self):
        """重置統計資訊"""
        self.stats = {
            "total_messages": 0,
            "primary_success": 0,
            "primary_failed": 0,
            "integrated_queued": 0,
            "integrated_skipped": 0,
            "queue_full_drops": 0
        }
