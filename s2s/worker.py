import argparse


TASKS = [
    'failed_batch_handler',
]


def parse_arguments():
    parser = argparse.ArgumentParser()
    parser.add_argument('task', choices=TASKS, help='Task name.')
    args = parser.parse_args()
    return args


def main():
    args = parse_arguments()
    if args.task == 'failed_batch_handler':
        from s2s.facebook_capi.tasks import failed_batch_handler

        failed_batch_handler()


if __name__ == '__main__':
    main()
