# IP2Location 位置資訊修復工具

這個工具用於修復 tagtoo_event 數據表中缺失的 IP 位置資訊。它使用 IP2Location 資料庫查詢 IP 地址的地理位置，並將結果更新到 BigQuery 中的 tagtoo_event 表。

## 前置需求

- Docker 和 Docker Compose
- GCP 服務帳號金鑰文件（具有 BigQuery 讀寫權限）
- 足夠的磁碟空間（IP2Location 資料庫約需要 4GB 空間）

## 使用方法

### 1. 設置環境變數

```bash
# 設置 GCP 認證
export GOOGLE_APPLICATION_CREDENTIALS=/path/to/your/service-account-key.json
```

### 2. 測試連接

在運行完整修復前，先測試環境設置是否正確：

```bash
cd tools/ip2location_repair
./scripts/test.sh
```

此腳本會啟動測試容器來驗證與 IP2Location 資料庫和 BigQuery 的連接。

### 3. 執行修復

確認測試通過後，執行修復程序：

```bash
cd tools/ip2location_repair
./scripts/run.sh
```

此腳本會啟動 Docker 容器，查詢缺失位置資訊的 IP 地址，並將位置資訊更新到 BigQuery 中。

### 4. 使用命令行參數

修復腳本支援多種命令行參數，可以針對不同需求進行設定：

```bash
# 使用自訂日期範圍
docker-compose run ip2location_fix python /app/fix_location.py --start-date 2025-02-17 --end-date 2025-03-27

# 調整批次處理參數
docker-compose run ip2location_fix python /app/fix_location.py --batch-size 2000 --query-size 50000 --max-workers 40

# 只執行資料驗證
docker-compose run ip2location_fix python /app/fix_location.py --validate-only

# 只執行更新步驟
docker-compose run ip2location_fix python /app/fix_location.py --update-only
```

可用參數說明：

- `--start-date`: 開始日期，格式 YYYY-MM-DD （預設：2025-02-17）
- `--end-date`: 結束日期，格式 YYYY-MM-DD （預設：2025-03-27）
- `--batch-size`: 每批處理的 IP 數量 （預設：1000）
- `--query-size`: 每次查詢的 IP 數量 （預設：25000）
- `--max-workers`: 並行處理的執行緒數量 （預設：30）
- `--validate-only`: 只驗證資料，不執行更新
- `--update-only`: 只執行更新步驟，不處理 IP

## 檔案說明

- `fix_location.py`: 主要的修復腳本
- `test_connection.py`: 連接測試腳本
- `docker-compose.yml`: Docker Compose 配置文件
- `Dockerfile`: Docker 映像配置
- `mysql-conf/ip2location.cnf`: MySQL 配置文件（用於 IP2Location 資料庫）
- `scripts/run.sh`: 執行主要修復的腳本
- `scripts/test.sh`: 測試連接的腳本

## 故障排除

### 無法連接到 IP2Location 資料庫

檢查 Docker 容器是否正確啟動，可以嘗試：

```bash
docker ps -a  # 檢查容器狀態
docker logs ip2location  # 查看 IP2Location 容器日誌
```

### 無法連接到 BigQuery

確認 GCP 憑證設置正確：

```bash
echo $GOOGLE_APPLICATION_CREDENTIALS  # 應顯示憑證文件路徑
```

確認服務帳號具有適當的權限，包括 BigQuery 資料讀寫權限。

### 修復過程中斷

如果修復過程中斷，可以直接重新運行 `run.sh` 腳本。腳本設計為可恢復執行，會從上次中斷的地方繼續。

## 範例

修復特定日期範圍的數據（通過修改 `fix_location.py` 中的日期範圍）：

```python
# 在 fix_location.py 中，找到並修改以下部分：
query = """
SELECT DISTINCT ip_address
FROM `tagtoo-tracking.event_prod.tagtoo_event`
WHERE event_time BETWEEN '2025-02-17' AND '2025-03-27'
AND location IS NULL
AND ip_address IS NOT NULL
"""
```
