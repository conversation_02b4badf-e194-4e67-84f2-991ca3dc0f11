from environs import Env

env = Env()

MODE = env.str('MODE', 'test')

LOGGING_LEVEL = env.str('LOGGING_LEVEL', 'INFO')

GCP_PROJECT_ID = env.str('GCP_PROJECT_ID')

FACEBOOK_CAPI_TOPIC_CAPI_COMPRESSED = env.str('FACEBOOK_CAPI_TOPIC_CAPI_COMPRESSED')

FACEBOOK_CAPI_TOPIC_FAILED_BATCH_DECOMPRESSED = env.str(
    'FACEBOOK_CAPI_TOPIC_FAILED_BATCH_DECOMPRESSED'
)

FACEBOOK_CAPI_SUBSCRIPTION_BATCH_V1 = env.str('FACEBOOK_CAPI_SUBSCRIPTION_BATCH_V1')

FACEBOOK_CAPI_SUBSCRIPTION_SINGLETON_V1 = env.str(
    'FACEBOOK_CAPI_SUBSCRIPTION_SINGLETON_V1'
)

RABBITMQ_HOST = env.str('RABBITMQ_HOST')

REDIS_HOST = env.str('REDIS_HOST')

S2S_PIXELS_CONFIG_API = env.str('S2S_PIXELS_CONFIG_API')

CONFIG_API_AUTH_TOKEN = env.str('CONFIG_API_AUTH_TOKEN')

CONFIG_FACEBOOK_CAPI_TTL = env.int('CONFIG_FACEBOOK_CAPI_TTL')

FACEBOOK_API_VERSION = env.str('FACEBOOK_API_VERSION')

LTA_FACEBOOK_TOPIC = env.str('LTA_FACEBOOK_TOPIC')

LTA_FACEBOOK_SUBSCRIPTION_V1 = env.str('LTA_FACEBOOK_SUBSCRIPTION_V1')

LTA_FACEBOOK_CAPI_SUBSCRIPTION_BATCH_V1 = env.str(
    'LTA_FACEBOOK_CAPI_SUBSCRIPTION_BATCH_V1'
)

LTA_FACEBOOK_DATA_LOADER_IMAGE = env.str('LTA_FACEBOOK_DATA_LOADER_IMAGE')
