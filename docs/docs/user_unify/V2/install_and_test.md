## Docker Compose
- User Unify V2 是改成使用 k8s cron job 來處理，相關的設定可以參考 [user_unify.tf](https://github.com/Tagtoo/ad-track/blob/master/deploy/user_unify.tf)
- 若有需要在本機手動執行，流程如下
    1. 啟動 User Unify 環境
    ```cmd
    docker compose -f docker-compose.user_unify.yml up
    ```
    2. 連上該 container
    ```cmd
    docker exec -it user-unify /bin/bash
    ```
    3. 執行 [update_grouping_result](./commands.md#update_grouping_result)
    ```cmd
    python update_grouping_result.py -s start_date -e end_date
    ```
    4. 關閉 container
    ```cmd
    docker compose -f docker-compose.user_unify.yml down
    ```

## Test
目前尚未實作這部分，詳細的部分列在 [To do list](./to_do_list.md#test)
