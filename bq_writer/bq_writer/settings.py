from environs import Env

env = Env()

TEST_MODE = env.bool('TEST_MODE')

GCP_PROJECT_ID = env.str('GCP_PROJECT_ID')

TAGTOO_EVENT_SUBSCRIPTION_V1 = env.str('TAGTOO_EVENT_SUBSCRIPTION_V1')

BIGQUERY_DATASET_ID = env.str('BIGQUERY_DATASET_ID')

BIGQUERY_TAGTOO_EVENT_TABLE_ID = env.str('BIGQUERY_TAGTOO_EVENT_TABLE_ID')

IP2LOCATION_DB_HOST = env.str('IP2LOCATION_DB_HOST')

# 雙寫入架構配置 (v2.1 tracking_id 核心策略)
INTEGRATED_WRITE_ENABLED = env.bool('INTEGRATED_WRITE_ENABLED', default=False)

INTEGRATED_WRITE_SAMPLE_RATE = env.float('INTEGRATED_WRITE_SAMPLE_RATE', default=0.0)
