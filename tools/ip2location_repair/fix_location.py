import os
from dataclasses import dataclass
from contextlib import contextmanager
import mariadb
from google.cloud import bigquery
from concurrent.futures import ThreadPoolExecutor
import time
from datetime import datetime
import logging
import sys
import argparse

# 設定 logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ip2location_fix.log'),
        logging.StreamHandler()
    ]
)

@dataclass
class Location:
    country_code: str
    region_name: str
    city_name: str
    latitude: float
    longitude: float
    zip_code: str

@contextmanager
def ip2location_connection():
    host = os.environ.get('MYSQL_HOST', 'ip2location')  # 使用環境變數，預設為服務名稱
    port = int(os.environ.get('MYSQL_PORT', 3306))
    user = os.environ.get('MYSQL_USER', 'root')
    password = os.environ.get('MYSQL_PASSWORD', '')
    database = os.environ.get('MYSQL_DATABASE', 'ip2location_database')

    # 嘗試連接
    try:
        conn = mariadb.connect(
            user=user,
            host=host,
            port=port,
            password=password,
            database=database,
            connect_timeout=15  # 增加連接超時時間
        )
        yield conn
    except Exception as e:
        logging.error(f"連接 IP2Location 資料庫時出錯: {e}")
        raise
    finally:
        if 'conn' in locals() and conn:
            conn.close()

def process_ip_batch(ips: list, batch_id: int) -> tuple:
    results = []  # 有位置資訊的 IP
    processed_without_location = []  # 處理過但沒有位置資訊的 IP
    logging.info(f"處理批次 {batch_id}，包含 {len(ips)} 個 IP")

    if not ips:
        return results, processed_without_location

    try:
        with ip2location_connection() as conn:
            cursor = conn.cursor()

            # 直接查詢每個 IP，但減少查詢次數
            for ip in ips:
                try:
                    cursor.execute(
                        """
                        SELECT
                            `country_code`,
                            `region_name`,
                            `city_name`,
                            `latitude`,
                            `longitude`,
                            `zip_code`
                        FROM `ip2location_database`
                        WHERE INET_ATON(%s) <= ip_to
                        LIMIT 1
                        """,
                        (ip,)
                    )
                    result = cursor.fetchone()
                    if result and result[0] != '-' and result[0] is not None and result[0].strip() != '':
                        location = Location(*[
                            None if x == '-' or x == 0.0 else x
                            for x in result
                        ])
                        # 確保 country_code 不是 null 或空字串
                        if location.country_code is not None and location.country_code.strip() != '':
                            results.append((ip, location))
                        else:
                            # 如果 country_code 是 null 或空字串，視為無位置資訊
                            processed_without_location.append(ip)
                    else:
                        # 記錄處理過但沒有找到位置的 IP
                        processed_without_location.append(ip)
                except Exception as e:
                    logging.error(f"處理 IP {ip} 時出錯: {e}")
                    # 將出錯的 IP 也標記為已處理但無位置
                    processed_without_location.append(ip)
    except Exception as e:
        logging.error(f"批次 {batch_id} 連接資料庫時出錯: {e}")

    logging.info(f"批次 {batch_id} 完成：找到 {len(results)} 個位置資訊，處理但無位置的 IP: {len(processed_without_location)} 個")
    return results, processed_without_location

def create_temp_table(client):
    """建立暫存表來儲存處理結果"""
    query = """
    CREATE SCHEMA IF NOT EXISTS `tagtoo-tracking.temp`;

    CREATE TABLE IF NOT EXISTS `tagtoo-tracking.temp.ip2location_updates` (
        ip_address STRING,
        processed BOOL DEFAULT FALSE,
        location STRUCT<
            country_code STRING,
            region_name STRING,
            city_name STRING,
            latitude FLOAT64,
            longitude FLOAT64,
            zip_code STRING
        >
    )
    """
    try:
        # 分開執行兩個查詢，因為 BigQuery 不允許在一次查詢中執行多個陳述式
        schema_query = "CREATE SCHEMA IF NOT EXISTS `tagtoo-tracking.temp`"
        job = client.query(schema_query)
        job.result()
        logging.info("暫存資料集已建立或已存在")

        table_query = """
        CREATE TABLE IF NOT EXISTS `tagtoo-tracking.temp.ip2location_updates` (
            ip_address STRING,
            processed BOOL DEFAULT FALSE,
            location STRUCT<
                country_code STRING,
                region_name STRING,
                city_name STRING,
                latitude FLOAT64,
                longitude FLOAT64,
                zip_code STRING
            >
        )
        """
        job = client.query(table_query)
        job.result()
        logging.info("暫存表已建立或已存在")
    except Exception as e:
        logging.error(f"建立暫存表時出錯: {e}")
        raise

def update_bigquery_batch(client, all_results, all_processed_without_location=None):
    """一次性更新所有批次的結果到 BigQuery"""
    if not all_results and not all_processed_without_location:
        logging.info("沒有結果需要更新")
        return

    total_results = sum(len(batch) for batch in all_results) if all_results else 0
    total_processed_without_location = sum(len(batch) for batch in all_processed_without_location) if all_processed_without_location else 0

    logging.info(f"更新 BigQuery，總共有 {total_results} 個有位置資訊的 IP 和 {total_processed_without_location} 個無位置資訊的 IP")

    # 使用固定的臨時表名稱
    temp_table_id = "tagtoo-tracking.temp.ip2location_batch_temp"

    try:
        # 檢查並準備臨時表
        try:
            client.get_table(temp_table_id)
            clear_query = f"TRUNCATE TABLE `{temp_table_id}`"
            client.query(clear_query).result()
        except Exception:
            schema = [
                bigquery.SchemaField("ip_address", "STRING", mode="REQUIRED"),
                bigquery.SchemaField("country_code", "STRING"),
                bigquery.SchemaField("region_name", "STRING"),
                bigquery.SchemaField("city_name", "STRING"),
                bigquery.SchemaField("latitude", "FLOAT"),
                bigquery.SchemaField("longitude", "FLOAT"),
                bigquery.SchemaField("zip_code", "STRING"),
            ]
            table = bigquery.Table(temp_table_id, schema=schema)
            client.create_table(table, exists_ok=True)

        # 準備所有數據
        all_rows = []
        for batch_results in all_results:
            for ip, location in batch_results:
                all_rows.append({
                    "ip_address": ip,
                    "country_code": location.country_code or "",
                    "region_name": location.region_name or "",
                    "city_name": location.city_name or "",
                    "latitude": location.latitude,
                    "longitude": location.longitude,
                    "zip_code": location.zip_code or "",
                })

        # 分批寫入數據，每批最多 50000 筆
        batch_size = 25000
        for i in range(0, len(all_rows), batch_size):
            batch = all_rows[i:i + batch_size]
            logging.info(f"寫入批次 {i//batch_size + 1}/{(len(all_rows) + batch_size - 1)//batch_size}，大小：{len(batch)}")

            errors = client.insert_rows_json(temp_table_id, batch)
            if errors:
                logging.error(f"插入臨時表時出錯: {errors}")
                raise Exception(f"插入臨時表時出錯: {errors}")

            # 短暫休息，避免 API 限制
            time.sleep(1)

        # 一次性更新有位置資訊的 IP
        if all_rows:
            update_query = f"""
            UPDATE `tagtoo-tracking.temp.ip2location_updates` u
            SET
                processed = TRUE,
                location = STRUCT<
                    country_code STRING,
                    region_name STRING,
                    city_name STRING,
                    latitude FLOAT64,
                    longitude FLOAT64,
                    zip_code STRING
                >(
                    t.country_code,
                    t.region_name,
                    t.city_name,
                    t.latitude,
                    t.longitude,
                    t.zip_code
                )
            FROM `{temp_table_id}` t
            WHERE u.ip_address = t.ip_address
            """

            update_job = client.query(update_query)
            update_job.result()
            logging.info(f"已更新有位置資訊的 IP: {total_results} 筆")

        # 處理沒有位置資訊的 IP，只標記為已處理
        if all_processed_without_location and total_processed_without_location > 0:
            # 準備無位置資訊 IP 的臨時表
            no_location_temp_table_id = "tagtoo-tracking.temp.ip2location_no_location_temp"

            # 檢查並準備無位置資訊的臨時表
            try:
                client.get_table(no_location_temp_table_id)
                clear_query = f"TRUNCATE TABLE `{no_location_temp_table_id}`"
                client.query(clear_query).result()
            except Exception:
                schema = [
                    bigquery.SchemaField("ip_address", "STRING", mode="REQUIRED"),
                ]
                table = bigquery.Table(no_location_temp_table_id, schema=schema)
                client.create_table(table, exists_ok=True)

            # 準備無位置資訊的 IP 資料
            no_location_rows = []
            for ip_batch in all_processed_without_location:
                for ip in ip_batch:
                    no_location_rows.append({"ip_address": ip})

            # 分批寫入無位置資訊的 IP
            for i in range(0, len(no_location_rows), batch_size):
                batch = no_location_rows[i:i + batch_size]
                logging.info(f"寫入無位置資訊 IP 批次 {i//batch_size + 1}/{(len(no_location_rows) + batch_size - 1)//batch_size}，大小：{len(batch)}")

                errors = client.insert_rows_json(no_location_temp_table_id, batch)
                if errors:
                    logging.error(f"插入無位置資訊臨時表時出錯: {errors}")
                    raise Exception(f"插入無位置資訊臨時表時出錯: {errors}")

                # 短暫休息，避免 API 限制
                time.sleep(1)

            # 更新無位置資訊的 IP 為已處理
            no_location_update_query = f"""
            UPDATE `tagtoo-tracking.temp.ip2location_updates` u
            SET processed = TRUE
            FROM `{no_location_temp_table_id}` t
            WHERE u.ip_address = t.ip_address
            """

            no_location_update_job = client.query(no_location_update_query)
            no_location_update_job.result()
            logging.info(f"已標記無位置資訊的 IP 為已處理: {total_processed_without_location} 筆")

        logging.info(f"所有資料已更新: 總共 {total_results + total_processed_without_location} 筆")
    except Exception as e:
        logging.error(f"批量更新時出錯: {e}")
        raise

def process_query_batch(client, ips, max_workers=30, batch_size=2000):
    """處理一個 query size 的所有 IP"""
    all_results = []
    all_processed_without_location = []

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = []

        # 只進行 IP 解析，不做 BigQuery 更新
        for i in range(0, len(ips), batch_size):
            batch = ips[i:i + batch_size]
            futures.append(executor.submit(process_ip_batch, batch, i // batch_size))

        # 收集所有結果
        for future in futures:
            results, processed_without_location = future.result()
            if results:
                all_results.append(results)
            if processed_without_location:
                all_processed_without_location.append(processed_without_location)

    # 一次性更新所有結果到 BigQuery
    total_processed_count = 0
    if all_results or all_processed_without_location:
        update_bigquery_batch(client, all_results, all_processed_without_location)
        total_processed_count = sum(len(batch) for batch in all_results) + sum(len(batch) for batch in all_processed_without_location)

    return total_processed_count

def initialize_temp_table(client, start_date, end_date):
    """初始化暫存表，將所有需要處理的 IP 寫入"""
    query = f"""
    INSERT INTO `tagtoo-tracking.temp.ip2location_updates` (ip_address, processed, location)
    WITH all_missing_ips AS (
        SELECT DISTINCT ip_address
        FROM `tagtoo-tracking.event_prod.tagtoo_event`
        WHERE event_time BETWEEN '{start_date}' AND '{end_date}'
        AND location IS NULL
        AND ip_address IS NOT NULL
    ),
    existing_ips AS (
        SELECT ip_address
        FROM `tagtoo-tracking.temp.ip2location_updates`
    )
    SELECT
        ip_address,
        FALSE as processed,
        STRUCT<
            country_code STRING,
            region_name STRING,
            city_name STRING,
            latitude FLOAT64,
            longitude FLOAT64,
            zip_code STRING
        >(NULL, NULL, NULL, NULL, NULL, NULL) as location
    FROM all_missing_ips
    WHERE ip_address NOT IN (
        SELECT ip_address
        FROM existing_ips
    )
    """

    try:
        logging.info(f"開始初始化暫存表，處理 {start_date} 至 {end_date} 的數據...")
        job = client.query(query)
        result = job.result()
        rows_added = job.num_dml_affected_rows
        logging.info(f"成功初始化暫存表：新增了 {rows_added} 個待處理的 IP")
    except Exception as e:
        logging.error(f"初始化暫存表時出錯: {e}")
        raise

def get_distinct_missing_ips(client, limit=10000, offset=0):
    """取得缺失位置資訊的不重複 IP"""
    query = f"""
    SELECT ip_address
    FROM `tagtoo-tracking.temp.ip2location_updates`
    WHERE processed = FALSE
    LIMIT {limit} OFFSET {offset}
    """

    try:
        query_job = client.query(query)
        ips = [row.ip_address for row in query_job]
        return ips
    except Exception as e:
        logging.error(f"查詢缺失 IP 時出錯: {e}")
        return []

def get_progress_stats(client):
    """取得目前進度統計"""
    query = """
    SELECT
        COUNT(*) as total_ips,
        COUNTIF(processed = TRUE) as processed_count,
        COUNTIF(location IS NOT NULL) as with_location_count
    FROM `tagtoo-tracking.temp.ip2location_updates`
    """

    try:
        query_job = client.query(query)
        for row in query_job:
            total = row.total_ips
            processed = row.processed_count
            with_location = row.with_location_count

        return {
            "total_missing": total,
            "processed": processed,
            "with_location": with_location,
            "remaining": total - processed,
            "progress_percent": round((processed / total) * 100, 2) if total > 0 else 100
        }
    except Exception as e:
        logging.error(f"取得進度統計時出錯: {e}")
        return {
            "total_missing": "未知",
            "processed": "未知",
            "with_location": "未知",
            "remaining": "未知",
            "progress_percent": "未知"
        }

def test_ip2location_connection():
    """測試與 IP2Location 資料庫的連接"""
    try:
        with ip2location_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            logging.info(f"成功連接到 IP2Location 資料庫，版本: {version[0]}")

            # 測試查詢 IP
            test_ip = "*******"  # Google DNS
            cursor.execute(
                """
                SELECT
                    `country_code`,
                    `region_name`,
                    `city_name`,
                    `latitude`,
                    `longitude`,
                    `zip_code`
                FROM `ip2location_database`
                WHERE INET_ATON(%s) <= ip_to
                LIMIT 1
                """,
                (test_ip,)
            )
            result = cursor.fetchone()
            if result:
                logging.info(f"測試 IP 查詢成功: {test_ip} => {result}")
                return True
            else:
                logging.error(f"測試 IP 查詢失敗: 未找到 {test_ip} 的資料")
                return False
    except Exception as e:
        logging.error(f"測試 IP2Location 連接時出錯: {e}")
        return False

def test_bigquery_connection(client):
    """測試與 BigQuery 的連接"""
    try:
        query_job = client.query("SELECT 1")
        result = list(query_job)[0]
        logging.info(f"成功連接到 BigQuery: {result}")
        return True
    except Exception as e:
        logging.error(f"測試 BigQuery 連接時出錯: {e}")
        return False

def validate_temp_data(client):
    """驗證暫存表的資料"""
    validation_query = """
    SELECT
        COUNT(*) as total_records,
        COUNTIF(processed = TRUE) as processed_records,
        COUNTIF(location.country_code IS NOT NULL AND location.country_code != '') as records_with_location
    FROM `tagtoo-tracking.temp.ip2location_updates`
    """

    try:
        query_job = client.query(validation_query)
        results = query_job.result()
        row = next(results)

        logging.info(f"""
        資料驗證結果：
        - 總記錄數：{row.total_records}
        - 已處理記錄數：{row.processed_records}
        - 有有效位置資訊的記錄數：{row.records_with_location}
        """)

        # 新的驗證邏輯：只要所有記錄都已處理即可，不要求所有記錄都有位置資訊
        is_valid = row.total_records == row.processed_records

        # 計算無位置資訊但已處理的記錄數
        no_location_but_processed = row.processed_records - row.records_with_location

        if is_valid:
            logging.info(f"資料驗證成功: 所有 IP 都已處理，其中 {no_location_but_processed} 個 IP 沒有有效位置資訊")
        else:
            logging.error(f"資料驗證失敗: 還有 {row.total_records - row.processed_records} 個 IP 未處理")

        return is_valid
    except Exception as e:
        logging.error(f"驗證資料時出錯: {e}")
        return False

def update_tagtoo_event_table(client, start_date, end_date):
    """第二階段：驗證後一次性更新到 production"""
    # 先驗證資料
    if not validate_temp_data(client):
        logging.error("資料驗證失敗，取消更新")
        return False

    # 取得統計資訊
    query = """
    SELECT
        COUNT(*) as total_records,
        COUNTIF(processed = TRUE) as processed_records,
        COUNTIF(location.country_code IS NOT NULL AND location.country_code != '') as records_with_location
    FROM `tagtoo-tracking.temp.ip2location_updates`
    """
    query_job = client.query(query)
    row = next(query_job.result())

    logging.info(f"""
    準備更新 tagtoo_event 表：
    - 總 IP 數量: {row.total_records}
    - 已處理 IP 數量: {row.processed_records}
    - 有位置資訊的 IP 數量: {row.records_with_location}
    - 無位置資訊但已處理的 IP 數量: {row.processed_records - row.records_with_location}
    - 只有有效位置資訊（country_code 不為空）的 IP 才會被更新到 tagtoo_event 表
    """)

    # 移除使用者互動，直接更新
    logging.info("自動執行更新到 production 表")

    # 一次性更新所有資料，使用正確的欄位結構，並嚴格檢查 country_code
    update_query = f"""
    UPDATE `tagtoo-tracking.event_prod.tagtoo_event` t
    SET location = STRUCT<
        country_code STRING,
        region_name STRING,
        city_name STRING,
        latitude FLOAT64,
        longitude FLOAT64,
        zip_code STRING
    >(
        u.location.country_code,
        u.location.region_name,
        u.location.city_name,
        u.location.latitude,
        u.location.longitude,
        u.location.zip_code
    )
    FROM `tagtoo-tracking.temp.ip2location_updates` u
    WHERE t.ip_address = u.ip_address
        AND u.processed = TRUE
        AND u.location IS NOT NULL
        AND u.location.country_code IS NOT NULL
        AND u.location.country_code != ''
        AND t.event_time BETWEEN '{start_date}' AND '{end_date}'
    """

    try:
        start_time = time.time()
        logging.info("開始更新 tagtoo_event 表...")
        update_job = client.query(update_query)
        result = update_job.result()
        end_time = time.time()

        logging.info(f"""
        更新完成：
        - 執行時間：{end_time - start_time:.2f} 秒
        - 影響的資料列數：{update_job.num_dml_affected_rows}
        - 只更新了有有效位置資訊的 IP (country_code 不為空)
        """)

        return True
    except Exception as e:
        logging.error(f"更新 production 資料時出錯: {e}")
        return False

def main():
    # 解析命令行參數
    parser = argparse.ArgumentParser(description='IP2Location 資訊修復工具')
    parser.add_argument('--start-date', type=str, default='2025-02-17', help='開始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, default='2025-03-27', help='結束日期 (YYYY-MM-DD)')
    parser.add_argument('--batch-size', type=int, default=1000, help='每批處理的 IP 數量')
    parser.add_argument('--query-size', type=int, default=25000, help='每次查詢的 IP 數量')
    parser.add_argument('--max-workers', type=int, default=30, help='並行處理的執行緒數量')
    parser.add_argument('--validate-only', action='store_true', help='只驗證資料，不執行更新')
    parser.add_argument('--update-only', action='store_true', help='只執行更新步驟，不處理 IP')

    args = parser.parse_args()

    # 檢查日期格式
    try:
        datetime.strptime(args.start_date, '%Y-%m-%d')
        datetime.strptime(args.end_date, '%Y-%m-%d')
    except ValueError:
        logging.error("日期格式不正確，請使用 YYYY-MM-DD 格式")
        sys.exit(1)

    # 檢查 GOOGLE_APPLICATION_CREDENTIALS 環境變數
    if 'GOOGLE_APPLICATION_CREDENTIALS' not in os.environ:
        logging.error("GOOGLE_APPLICATION_CREDENTIALS 環境變數未設定")
        logging.info("請設定 GOOGLE_APPLICATION_CREDENTIALS 環境變數指向你的 Google Cloud 服務帳號金鑰檔案")
        logging.info("例如: export GOOGLE_APPLICATION_CREDENTIALS=\"/path/to/your/service-account-file.json\"")
        sys.exit(1)

    # 建立 BigQuery 客戶端
    try:
        client = bigquery.Client(location='asia-east1')
    except Exception as e:
        logging.error(f"無法建立 BigQuery 客戶端: {e}")
        sys.exit(1)

    # 測試連接
    logging.info("正在測試 IP2Location 資料庫連接...")
    if not test_ip2location_connection():
        logging.error("IP2Location 資料庫連接測試失敗，請確認 Docker 容器是否正確運行")
        logging.info("嘗試執行以下命令啟動 Docker 容器:")
        logging.info("docker run -d --name ip2location -p 3306:3306 asia.gcr.io/tagtoo-tracking/ip2location-mysql:2024-08-08")
        sys.exit(1)

    logging.info("正在測試 BigQuery 連接...")
    if not test_bigquery_connection(client):
        logging.error("BigQuery 連接測試失敗，請檢查認證設定")
        sys.exit(1)

    # 建立暫存表
    try:
        create_temp_table(client)
    except Exception as e:
        logging.error(f"無法建立暫存表: {e}")
        sys.exit(1)

    # 顯示處理參數
    logging.info(f"""
    開始處理，參數設定：
    - 日期範圍: {args.start_date} 至 {args.end_date}
    - 批次大小: {args.batch_size}
    - 查詢大小: {args.query_size}
    - 並行數量: {args.max_workers}
    - 僅驗證: {args.validate_only}
    - 僅更新: {args.update_only}
    """)

    # 如果只是驗證資料
    if args.validate_only:
        validate_temp_data(client)
        sys.exit(0)

    # 如果只是執行更新
    if args.update_only:
        update_tagtoo_event_table(client, args.start_date, args.end_date)
        sys.exit(0)

    # 初始化暫存表
    initialize_temp_table(client, args.start_date, args.end_date)

    # 取得統計資料
    stats = get_progress_stats(client)
    logging.info(f"開始修復: 總共有 {stats['total_missing']} 個不同的 IP 需要處理，已處理 {stats['processed']} 個")

    # 執行參數
    batch_size = args.batch_size
    query_size = args.query_size
    max_workers = args.max_workers
    offset = 0

    # 修改主處理迴圈
    while True:
        try:
            # 查詢需要處理的 IP
            ips = get_distinct_missing_ips(client, query_size, offset)

            if not ips:
                logging.info("所有 IP 已處理完畢")
                break

            logging.info(f"取得 {len(ips)} 個尚未處理的 IP")

            # 處理這一批 IP 並一次性更新到 BigQuery
            processed_count = process_query_batch(client, ips, max_workers, batch_size)
            logging.info(f"已處理並更新 {processed_count} 個 IP 的位置資訊")

            # 更新 offset 繼續下一批
            offset += query_size

            # 取得最新進度
            stats = get_progress_stats(client)
            logging.info(f"目前進度: {stats['progress_percent']}% ({stats['processed']}/{stats['total_missing']})")

            # 短暫休息，避免 API 限制
            time.sleep(5)

        except Exception as e:
            logging.error(f"主處理迴圈出錯: {e}", exc_info=True)
            time.sleep(60)  # 出錯時暫停一分鐘再繼續

    # 完成所有 IP 處理後，確保主表已完全更新
    logging.info("執行最終主表更新...")
    update_tagtoo_event_table(client, args.start_date, args.end_date)

if __name__ == "__main__":
    main()
