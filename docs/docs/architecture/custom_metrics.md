# Kubernetes 自定義指標

## 概述

Kubernetes 自定義指標（Custom Metrics）是一種機制，允許 Kubernetes 水平 Pod 自動擴展器（HPA）基於標準 CPU 和內存指標之外的指標進行擴展。在我們的系統中，我們使用 Stackdriver Custom Metrics Adapter 來將 Google Cloud 指標（如 Pub/Sub 訂閱中未處理的消息數量）暴露給 Kubernetes，以實現更智能的自動擴展。

## 架構

```mermaid
flowchart LR
    GCP[Google Cloud\nPub/Sub, BigQuery, etc.] -->|收集指標| Stackdriver[Stackdriver\nMonitoring]
    Stackdriver -->|提供指標| Adapter[Custom Metrics\nStackdriver Adapter]
    Adapter -->|轉換為\nKubernetes 指標| API[Custom Metrics API]
    API -->|查詢指標| HPA[Horizontal Pod\nAutoscaler]
    HPA -->|擴展/縮減| Pods[Kubernetes Pods]
```

自定義指標系統的工作流程如下：

1. Stackdriver 收集 Google Cloud 服務（如 Pub/Sub）的指標
2. Custom Metrics Stackdriver Adapter 從 Stackdriver 獲取這些指標
3. Adapter 將這些指標轉換為 Kubernetes 自定義指標 API 格式
4. HPA 控制器查詢這些指標並據此調整 Pod 數量

## 配置

自定義指標系統通過 Terraform 進行配置，相關配置位於 `deploy/custom_metrics.tf` 文件中：

```hcl
# 創建 custom-metrics namespace
resource "kubernetes_namespace" "custom_metrics" {
  metadata {
    name = "custom-metrics"
  }
}

# 創建 ServiceAccount
resource "kubernetes_service_account" "custom_metrics_stackdriver_adapter" {
  metadata {
    name      = "custom-metrics-stackdriver-adapter"
    namespace = kubernetes_namespace.custom_metrics.metadata[0].name
  }
}

# 創建 ClusterRoleBinding
resource "kubernetes_cluster_role_binding" "custom_metrics_stackdriver_adapter" {
  metadata {
    name = "custom-metrics-stackdriver-adapter"
  }
  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "ClusterRole"
    name      = "custom-metrics-server-resources"
  }
  subject {
    kind      = "ServiceAccount"
    name      = kubernetes_service_account.custom_metrics_stackdriver_adapter.metadata[0].name
    namespace = kubernetes_namespace.custom_metrics.metadata[0].name
  }
}

# 創建 Deployment
resource "kubernetes_deployment" "custom_metrics_stackdriver_adapter" {
  metadata {
    name      = "custom-metrics-stackdriver-adapter"
    namespace = kubernetes_namespace.custom_metrics.metadata[0].name
    labels = {
      run     = "custom-metrics-stackdriver-adapter"
      k8s-app = "custom-metrics-stackdriver-adapter"
    }
  }
  spec {
    replicas = 1
    selector {
      match_labels = {
        run     = "custom-metrics-stackdriver-adapter"
        k8s-app = "custom-metrics-stackdriver-adapter"
      }
    }
    template {
      metadata {
        labels = {
          run                      = "custom-metrics-stackdriver-adapter"
          k8s-app                  = "custom-metrics-stackdriver-adapter"
          "kubernetes.io/cluster-service" = "true"
        }
      }
      spec {
        service_account_name = kubernetes_service_account.custom_metrics_stackdriver_adapter.metadata[0].name
        container {
          image = "gcr.io/gke-release/custom-metrics-stackdriver-adapter:v0.12.2-gke.0"
          name  = "custom-metrics-stackdriver-adapter"
          command = [
            "/adapter",
            "--use-new-resource-model=true"
          ]
          resources {
            limits = {
              cpu    = "250m"
              memory = "200Mi"
            }
            requests = {
              cpu    = "250m"
              memory = "200Mi"
            }
          }
        }
      }
    }
  }
}
```

## 使用自定義指標的 HPA 配置

以下是使用 Pub/Sub 訂閱未處理消息數量作為擴展指標的 HPA 配置示例：

```hcl
resource "kubernetes_horizontal_pod_autoscaler_v2" "tagtoo_event_subscriber" {
  metadata {
    name      = kubernetes_deployment_v1.tagtoo_event_subscriber.metadata[0].name
    namespace = var.kubernetes_namespace
  }
  spec {
    min_replicas = var.bq_writer.subscriber.min_replicas
    max_replicas = var.bq_writer.subscriber.max_replicas

    metric {
      type = "Resource"
      resource {
        name = "cpu"
        target {
          type                = "Utilization"
          average_utilization = var.bq_writer.subscriber.target_cpu_utilization_percentage
        }
      }
    }

    metric {
      type = "External"
      external {
        metric {
          name = "pubsub.googleapis.com|subscription|num_undelivered_messages"
          selector {
            match_labels = {
              "resource.labels.subscription_id" = replace(google_pubsub_subscription.tagtoo_event_to_bigquery_subs["v1"].name, "projects/${var.gcp_project}/subscriptions/", "")
            }
          }
        }
        target {
          type          = "AverageValue"
          average_value = "1000"  # 每個 Pod 處理 1000 條消息
        }
      }
    }

    scale_target_ref {
      api_version = "apps/v1"
      kind        = "Deployment"
      name        = kubernetes_deployment_v1.tagtoo_event_subscriber.metadata[0].name
    }

    behavior {
      scale_up {
        stabilization_window_seconds = 60  # 減少擴展冷卻期
        select_policy                = "Max"
        policy {
          type           = "Percent"
          value          = 100
          period_seconds = 60
        }
        policy {
          type           = "Pods"
          value          = 5
          period_seconds = 60
        }
      }
      scale_down {
        stabilization_window_seconds = 300
        select_policy                = "Max"
        policy {
          type           = "Percent"
          value          = 10
          period_seconds = 60
        }
      }
    }
  }
  lifecycle {
    ignore_changes = [
      spec[0].min_replicas,
    ]
  }
}
```

## 可用的自定義指標

以下是一些可用於 HPA 的常見 Google Cloud 指標：

| 指標名稱 | 描述 | 用途 |
|---------|------|------|
| pubsub.googleapis.com\|subscription\|num_undelivered_messages | Pub/Sub 訂閱中未處理的消息數量 | 根據消息積壓情況擴展消費者 |
| pubsub.googleapis.com\|subscription\|oldest_unacked_message_age | 最舊未確認消息的年齡（秒） | 根據處理延遲擴展消費者 |
| loadbalancing.googleapis.com\|https\|request_count | HTTP 負載均衡器的請求數 | 根據流量擴展 Web 服務 |

## 故障排除

如果自定義指標不可用或 HPA 無法獲取指標，請檢查以下幾點：

1. 確保 custom-metrics-stackdriver-adapter pod 正在運行：

   ```bash
   kubectl get pods -n custom-metrics
   ```

2. 檢查 API 服務是否可用：

   ```bash
   kubectl get apiservice | grep metrics
   ```

3. 檢查 adapter 的日誌：

   ```bash
   kubectl logs -n custom-metrics $(kubectl get pods -n custom-metrics -o jsonpath='{.items[0].metadata.name}')
   ```

4. 確保所需的 ClusterRole 和 ClusterRoleBinding 存在：

   ```bash
   kubectl get clusterrole | grep metrics
   kubectl get clusterrolebinding | grep metrics
   ```

## 最佳實踐

- 結合使用多種指標（如 CPU 和自定義指標）以實現更穩健的自動擴展
- 設置適當的擴展行為（behavior）以避免頻繁的擴展和縮減
- 定期監控自定義指標的可用性和準確性
- 為不同類型的工作負載選擇最合適的指標
