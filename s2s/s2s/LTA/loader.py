import json
import logging
import re
import time
import uuid
from collections import deque
from copy import deepcopy
from hashlib import sha256
from typing import Iterator, List, Tuple, TypedDict

from ..service import LTAErrorReportingService
from .publishers import LTAToFacebookCApiPublisher
from .storages import LTAGoogleStorage
from ..logging_utils import CustomStructuredLogHandler

logger = logging.getLogger('s2s')


class PixelUserData(TypedDict):
    em: List[str]
    ph: List[str]
    ge: List[str]
    db: List[str]
    zp: List[str]
    fbp: str
    fbc: str
    client_ip_address: str


class PixelCustomData(TypedDict):
    segment_id: str


class PixelDatum(TypedDict):
    event_name: str
    event_time: int
    action_source: str
    user_data: PixelUserData
    custom_data: PixelCustomData


class DataLoader(object):
    message_limit = 8000000
    default_retry_period = 30
    default_max_retry_times = 10
    chunk_size = 1000

    error_report_service = LTAErrorReportingService(version='v1')

    class LogAdapter(logging.LoggerAdapter):
        def process(self, msg, kwargs):
            process_id = self.extra['process_id']
            # 確保 kwargs 包含 'extra' 字典
            if 'extra' not in kwargs:
                kwargs['extra'] = {}

            # 獲取正確的日誌級別
            level_no = kwargs.get('levelno', self.logger.level)
            level_name = logging.getLevelName(level_no)

            # 將 Python logging 級別映射到 Google Cloud Logging 級別
            if level_no >= logging.CRITICAL:
                severity = 'CRITICAL'
            elif level_no >= logging.ERROR:
                severity = 'ERROR'
            elif level_no >= logging.WARNING:
                severity = 'WARNING'
            elif level_no >= logging.INFO:
                severity = 'INFO'
            elif level_no >= logging.DEBUG:
                severity = 'DEBUG'
            else:
                severity = 'DEFAULT'

            # 添加自定義字段到 extra 字典，這些字段會被 Google Cloud Logging 處理器使用
            kwargs['extra'].update({
                'process_id': str(process_id),
                'component': 'LTA',
                'service': 'Facebook C-API',
                # 添加日誌級別信息，確保 Google Cloud Logging 正確處理
                'severity': severity,
                # 添加 labels 字段，用於 StructuredLogHandler
                'labels': {
                    'process_id': str(process_id),
                    'component': 'LTA',
                    'service': 'Facebook C-API'
                }
            })

            # 返回格式化的消息和更新後的 kwargs
            return f'[LTA][Facebook C-API][{process_id}] {msg}', kwargs

        def _log(self, level, msg, *args, **kwargs):
            """
            重寫 _log 方法，確保日誌級別正確設置
            """
            if self.isEnabledFor(level):
                # 確保 kwargs 包含 'extra' 字典
                if 'extra' not in kwargs:
                    kwargs['extra'] = {}

                # 將 Python logging 級別映射到 Google Cloud Logging 級別
                if level >= logging.CRITICAL:
                    severity = 'CRITICAL'
                elif level >= logging.ERROR:
                    severity = 'ERROR'
                elif level >= logging.WARNING:
                    severity = 'WARNING'
                elif level >= logging.INFO:
                    severity = 'INFO'
                elif level >= logging.DEBUG:
                    severity = 'DEBUG'
                else:
                    severity = 'DEFAULT'

                # 添加自定義字段到 extra 字典
                kwargs['extra'].update({
                    'severity': severity,
                })

                # 調用父類的 _log 方法
                super()._log(level, msg, *args, **kwargs)

    def _publish_message(
        self,
        ec_id: int,
        data: str,
        version: str,
    ) -> None:
        with LTAToFacebookCApiPublisher(max_messages=1000) as publisher:
            publisher.add_message(
                data=data,
                version=version,
                ec_id=str(ec_id),
            )

    def _encode_sha_256(self, text: str) -> str:
        sha_256 = sha256(text.encode('utf8'))
        return sha_256.hexdigest()

    def _transform_str_to_list(self, text: str) -> List[str]:
        regex = r'[^\[\]\'\s",]+'
        return re.findall(regex, text)

    def _compose_pixel_zip_code(self, zip_codes: List[str]) -> List[str]:
        return [self._encode_sha_256(zip_code) for zip_code in zip_codes]

    def _compose_pixel_segment_id(self, segment_id: List[str]) -> str:
        return f'_{"_".join(segment_id)}_' if segment_id else ''

    def _get_common_attributes(self, attrs: dict) -> Tuple[str, int, str]:
        event_name = 'LTA' or attrs.get('event_name')
        event_time = int(time.time()) or attrs.get('event_time')
        action_source = 'other' or attrs.get('action_source')
        return (event_name, event_time, action_source)

    def _compose_pixel_datum_from_csv(
        self, user_data: List[dict], **kwargs
    ) -> Iterator[List[dict]]:
        event_name, event_time, action_source = self._get_common_attributes(kwargs)
        res = []
        for user_datum in user_data:
            datum: dict = PixelDatum(
                event_name=event_name,
                event_time=event_time,
                action_source=action_source,
                user_data=PixelUserData(
                    em=self._transform_str_to_list(user_datum.get('emails', '')),
                    ph=self._transform_str_to_list(user_datum.get('mobiles', '')),
                    ge=self._transform_str_to_list(user_datum.get('gender', '')),
                    db=self._transform_str_to_list(user_datum.get('birth', '')),
                    zp=self._compose_pixel_zip_code(
                        self._transform_str_to_list(user_datum.get('zip_code', ''))
                    ),
                ),
                custom_data=PixelCustomData(
                    segment_id=self._compose_pixel_segment_id(
                        self._transform_str_to_list(user_datum.get('segment_id', ''))
                    ),
                ),
            )
            res.append(datum)
            if len(res) == 100000:
                yield res
                res = []
        if res:
            yield res

    def _compose_pixel_datum_from_avro(
        self, user_data: List[dict], **kwargs
    ) -> Iterator[List[dict]]:
        event_name, event_time, action_source = self._get_common_attributes(kwargs)
        res = []
        for user_datum in user_data:
            emails = user_datum.get('emails', [])
            phones = user_datum.get('phones', [])
            genders = user_datum.get('genders', [])
            births = user_datum.get('births', [])
            self._logger.debug(
                f'{len(emails)=}, {len(phones)=}, {len(genders)=}, {len(births)=}'
            )

            default_pixel_datum: dict = PixelDatum(
                event_name=event_name,
                event_time=event_time,
                action_source=action_source,
                user_data=PixelUserData(
                    em=emails,
                    ph=phones,
                    ge=genders,
                    db=births,
                ),
                custom_data=PixelCustomData(
                    segment_id=self._compose_pixel_segment_id(
                        user_datum.get('segment_id', [])
                    ),
                ),
            )

            for fb_info in user_datum.get('fb_info', []):
                fbp, fbc, ip = fb_info['fbp_fbc_ip']
                cur_pixel_datum = deepcopy(default_pixel_datum)
                cur_pixel_datum['user_data']['fbp'] = fbp
                cur_pixel_datum['user_data']['fbc'] = fbc
                cur_pixel_datum['user_data']['client_ip_address'] = ip
                res.append(cur_pixel_datum)
                if len(res) == 10000:
                    yield res
                    res = []
        if res:
            yield res

    def compose_pixel_data(
        self, user_data: List[dict], file_type: str = None
    ) -> Iterator[List[dict]]:
        compose_method = (
            self._compose_pixel_datum_from_csv
            if file_type == 'csv'
            else self._compose_pixel_datum_from_avro
        )
        yield from compose_method(user_data)

    def process(
        self,
        file_name,
        ec_id,
        version,
        message_limit=None,
    ):
        message_limit = message_limit or self.message_limit

        process_id = uuid.uuid4()
        self._logger = self.LogAdapter(logger, {'process_id': process_id})

        GCS_service = LTAGoogleStorage()
        file_type = file_name.split('.')[-1]

        self._logger.info(f'Start to process file: {file_name}.')

        try:
            LTA_data = GCS_service.batch_read_file(file_name=file_name)
        except Exception as error:
            self._logger.critical(
                f'CRITICAL: Failed to read file {file_name} from GCS. This is a serious issue that requires immediate attention.'
            )
            self.error_report_service.report_error(error, version=version, file_name=file_name)
            return False
        else:
            self._logger.debug(f'Successfully connect to GCS. ({file_name})')

        fail_publish_queue = deque()
        pixel_count, batch_count = 0, 0
        while True:
            try:
                user_data = next(LTA_data, None)
            except Exception as error:
                self.error_report_service.report_error(
                    error, version=version, file_name=file_name
                )
            else:
                if user_data is None:
                    break

                self._logger.debug(f'Start to process batch {batch_count}.')
                batch_process_count = 0

                try:
                    pixel_data = self.compose_pixel_data(user_data, file_type)
                except Exception as error:
                    self._logger.critical(
                        f'CRITICAL: Failed to compose pixel data for file {file_name}. This is a serious issue that requires immediate attention.'
                    )
                    self.error_report_service.report_error(error, version=version, file_name=file_name)
                    break
                else:
                    # 因為 fbp 跟 fbc 展開後的數量可能會過大，所以再次切分，避免超過 message 的上限
                    for data in pixel_data:
                        data_queue = deque(
                            [
                                data[i : i + self.chunk_size]
                                for i in range(0, len(data), self.chunk_size)
                            ]
                        )
                        chunk_count = 0

                        while data_queue:
                            self._logger.debug(
                                f'Start to process batch {batch_count} chunk {chunk_count}.'
                            )

                            chunk = data_queue.popleft()
                            dumps_data = json.dumps(chunk, separators=(',', ':'))
                            chunk_size = len(dumps_data.encode('utf8'))
                            if chunk_size > message_limit:
                                half_length = len(chunk) // 2
                                if len(chunk) == 1:
                                    self._logger.error(
                                        f'Message too large. data: {dumps_data[:300]}...'
                                    )
                                else:
                                    data_queue.append(chunk[:half_length])
                                    data_queue.append(chunk[half_length:])
                                self._logger.debug(
                                    f'Batch {batch_count} '
                                    f'chunk {chunk_count} was too large, divided into two parts.'
                                )
                            else:
                                try:
                                    self._publish_message(
                                        ec_id=ec_id,
                                        data=dumps_data,
                                        version=version,
                                    )
                                except Exception as error:
                                    self.error_report_service.report_error(error)
                                    fail_publish_queue.append(
                                        (ec_id, chunk, 1, batch_count)
                                    )
                                    self._logger.warning(
                                        f'Fail to publish batch {batch_count} with {len(chunk)} messages.'
                                    )
                                else:
                                    pixel_count += len(chunk)
                                    batch_process_count += len(chunk)
                                    chunk_count += 1
                                    self._logger.debug(
                                        f'Successfully publish batch '
                                        f'{batch_count} chunk {chunk_count} with {len(chunk)} messages.'
                                    )
                        self._logger.info(
                            f'Successfully publish batch '
                            f'{batch_count} with {batch_process_count} messages.'
                        )
                        batch_count += 1

        if pixel_count == 0:
            self._logger.critical(
                f'CRITICAL: No messages were published from {file_name}. This is a serious issue that requires immediate attention.'
            )
            self.error_report_service.report_error(
                Exception(f'No messages published from {file_name}'),
                version=version,
                file_name=file_name
            )
        else:
            self._logger.info(
                f'Successfully publish {pixel_count} messages from {file_name}'
            )

        while fail_publish_queue:
            ec_id, pixel_data, retry_times, batch_idx = fail_publish_queue.popleft()
            time.sleep(self.default_retry_period * retry_times)

            try:
                self._publish_message(
                    ec_id,
                    json.dumps(pixel_data, separators=(',', ':')),
                    version,
                )
            except Exception as error:
                self.error_report_service.report_error(error)
                self._logger.warning(
                    f'Fail to publish batch {batch_idx} with {len(pixel_data)} messages'
                )
                if (retry_times := retry_times + 1) <= self.default_max_retry_times:
                    fail_publish_queue.append(
                        (ec_id, pixel_data, retry_times, batch_idx)
                    )
            else:
                self._logger.info(
                    f'Successfully publish batch {batch_idx} with {len(pixel_data)} messages'
                )
