MERGE `$groups_table` T USING (
    WITH next_day_result AS (
        SELECT
            *
        FROM
            `$grouping_result_table`
    ),
    contact_res AS (
        SELECT
            cnds.group_id,
            IFNULL(temp.group_time, cnds.group_time) group_time,
            IFNULL(
                (temp.permanents || cnds.permanents),
                cnds.permanents
            ) permanents,
            IFNULL((temp.emails || cnds.emails), cnds.emails) emails,
            IFNULL((temp.phones || cnds.phones), cnds.phones) phones,
            IFNULL(
                (temp.usernames || cnds.usernames),
                cnds.usernames
            ) usernames,
            -- IFNULL((temp.fbps || cnds.fbps), cnds.fbps) fbps,
            IFNULL((temp.gids || cnds.gids), cnds.gids) gids,
        FROM
            next_day_result cnds
            LEFT JOIN `$groups_table` temp ON temp.group_id = cnds.group_id
    ),
    DISTINST_res AS (
        SELECT
            group_id,
            group_time,
            ARRAY(
                SELECT
                    DISTINCT p
                FROM
                    contact_res.permanents p
            ) permanents,
            ARRAY(
                SELECT
                    DISTINCT em
                FROM
                    contact_res.emails em
            ) emails,
            ARRAY(
                SELECT
                    DISTINCT ph
                FROM
                    contact_res.phones ph
            ) phones,
            ARRAY(
                SELECT
                    DISTINCT un
                FROM
                    contact_res.usernames un
            ) usernames,
            -- ARRAY(
            --     SELECT
            --         DISTINCT fbp
            --     FROM
            --         contact_res.fbps fbp
            -- ) fbps,
            ARRAY(
                SELECT
                    DISTINCT gid
                FROM
                    contact_res.gids gid
            ) gids,
        FROM
            contact_res
    )
    SELECT
        *
    FROM
        DISTINST_res
) Q ON T.group_id = Q.group_id
WHEN NOT MATCHED THEN
INSERT
    (
        group_id,
        group_time,
        permanents,
        emails,
        phones,
        usernames,
        -- fbps,
        gids
    )
VALUES
    (
        Q.group_id,
        Q.group_time,
        Q.permanents,
        Q.emails,
        Q.phones,
        Q.usernames,
        -- Q.fbps,
        Q.gids
    )
    WHEN MATCHED THEN
UPDATE
SET
    permanents = Q.permanents,
    emails = Q.emails,
    phones = Q.phones,
    usernames = Q.usernames,
    -- fbps = Q.fbps,
    gids = Q.gids