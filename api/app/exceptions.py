class EventConfigTokenNotFoundError(Exception):
    def __init__(self, token):
        self.token = token
        super().__init__(token)

    def __str__(self):
        return f'Corresponding event config of token is not found: (token={self.token})'


class FacebookUserClientIPNotGlobalException(Exception):
    def __init__(self, ip):
        self.ip = ip
        super().__init__(ip)

    def __str__(self):
        return (
            f'Got an IP string {self.ip} which does not appear to be an global IPv4 or IPv6 address. '
            'We\'ll replace it with empty string and still publish to Facebook CAPI topic.'
        )
