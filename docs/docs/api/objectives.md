## Event

A map that represents an event for multiple channels

| field      | type   | optional | description                                     |
| ---------- | ------ | -------- | ----------------------------------------------- |
| token      | string | x        | Access token                                    |
| event_time | string | v        | ISO 8601 format in UTC+0                        |
| tagtoo     | object | v        | [Event.Tagtoo](/api/objectives#eventtagtoo)     |
| facebook   | object | v        | [Event.Facebook](/api/objectives#eventfacebook) |

## Event.Tagtoo

A map that represents an event for Tagtoo channel

| field      | type   | optional | description                                 |
| ---------- | ------ | -------- | ------------------------------------------- |
| permanent  | string | x        | an unique and long-lived user ID            |
| language   | enum   | x        | [LanguageEnum](/api/enums#languageenum)     |
| link       | url    | x        | current page link                           |
| referrer   | url    | v        | previous page link                          |
| ip_address | string | v        | client ip address                           |
| user_agent | string | x        | client user agent                           |
| event      | object | x        | [Event.Tagtoo.Event](#eventtagtooevent)     |
| user       | object | v        | [Event.Tagtoo.User](#eventtagtoouser)       |
| session    | object | x        | [Event.Tagtoo.Session](#eventtagtoosession) |

!!! info
    It's recommended to use a compounded value (fingerprint + IP address) for permanent
    to increase the cross-site mapping rate in user unify service.

# Event.Tagtoo.Event

A map that contains event details

| field       |  type  | optional | description                                                  |
| ----------- | :----: | -------- | ------------------------------------------------------------ |
| name        | string | x        | [EventNameEnum](/api/enums#eventnameenum)                    |
| value       | number | v        | the total value (price) of event                             |
| currency    | string | v        | [CurrencyEnum](/api/enums#currencyenum)                      |
| items       | array  | v        | [Event.Tagtoo.Event.Item](#eventtagtooeventitem)             |
| custom_data | object | v        | [Event.Tagtoo.Event.CustomData](#eventtagtooeventcustomdata) |

## Event.Tagtoo.Event.Item

A map that contains product item information

| field        |  type  | optional | description                                             |
| ------------ | :----: | -------- | ------------------------------------------------------- |
| id           | string | x        | product id                                              |
| name         | string | x        | product name                                            |
| price        | number | v        | product unit price                                      |
| quantity     | number | v        | product quantity                                        |
| availability | string | v        | [ItemAvailabilityEnum](/api/enums#itemavailabilityenum) |

## Event.Tagtoo.Event.CustomData

A map that contains event additional data

| field           | type   | optional | description                                         |
| --------------- | ------ | -------- | --------------------------------------------------- |
| payment_method  | string | v        | [PaymentMethodEnum](/api/enums#paymentmethodenum)   |
| shipping_method | string | v        | [ShippingMethodENum](/api/enums#shippingmethodenum) |
| copy_string     | string | v        | The web context copied by the user                  |
| status          | bool   | v        | To show the on/off or entry/exit status of an event |
| focus_minutes   | number | v        | To show how long an user stays on one page          |
| campaign_name   | string | v        | Ecommerce campaign or form title                    |
| auth_method     | string | v        | [AuthMethodEnum](/api/enums#authmethodenum)         |
| order_id        | string | v        | The unique ID for an order                          |
| document_height | number | v        | The height of a web page                            |
| scroll_top      | number | v        | The scroll top of a web page                        |
| search_string   | string | v        | The string entered by the user for the search       |
| breadcrumb      | string | v        | The breadcrumb for a product page                   |

**payment_method**

Use with the [add_payment_info](/api/events#addpaymentinfo) event.

**shipping_method**

Use with the [add_shipping_info](/api/events#addshippinginfo) event.

**copy_string**

Use with the [copy](/api/events#copy) event.

**status**

Use with the [focus](/api/events#focus) and [switch_tab](/api/events#switchtab) event.

**focus_minutes**

Use with the [focus](/api/events#focus) event.

**campaign_name**

Use with the [lead](/api/events#lead) and [view_promotion](/api/events#viewpromotion) event.

**auth_method**

Use with the [login](/api/events#login) and [register](/api/events#register) event.

**order_id**

Use with the [purchase](/api/events#purchase) and [refund](/api/events#refund) event.

**document_height**

Use with the [scroll](/api/events#scroll) event.

**scroll_top**

Use with the [scroll](/api/events#scroll) event.

**search_string**

Use with the [search](/api/events#search) event.

## Event.Tagtoo.User

A map that contains user information

| field | type   | hashed | optional | description                 |
| ----- | ------ | ------ | -------- | --------------------------- |
| em    | string | v      | v        | email                       |
| ph    | string | v      | v        | phone                       |
| un    | string | v      | v        | username                    |
| gd    | string | v      | v        | gender                      |
| db    | string | v      | v        | date of birth               |
| fbp   | string | x      | v        | facebook session id         |
| fbc   | string | x      | v        | facebook click id           |
| ga    | string | x      | v        | google analytics session id |
| gid   | string | x      | v        | google analytics user id    |
| lmid  | string | x      | v        | cresclab LINE user id       |

## Event.Tagtoo.Session

A map that contains session information

| field    | type   | optional | description                                        |
| -------- | ------ | -------- | -------------------------------------------------- |
| id       | string | x        | A short-lived session ID                           |
| source   | string | v        | the session source or `utm_source` query param     |
| medium   | string | v        | the session medium or `utm_medium` query param     |
| campaign | string | v        | the session campaign or `utm_campaign` query param |
| term     | string | v        | the session keyword or `utm_term` query param      |
| content  | string | v        | the session content or `utm_content` query param   |

# Event.Facebook

A map that represent a server event for Facebook channel. Please refer to [Server Event Parameters](https://developers.facebook.com/docs/marketing-api/conversions-api/parameters/server-event)
for all details.

**Schema differences**

The schema is mostly following Facebook's standard, but there are some differences:

* `event_time`: Do not specify this field here, instead, provide it at the [Event](/api/objectives#event) object for abstraction.
