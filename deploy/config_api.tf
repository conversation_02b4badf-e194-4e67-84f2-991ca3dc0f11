locals {
  event_config_prefix  = "config-api"
  event_config_api_env = "${local.event_config_prefix}-env"
  event_config_api_web = "${local.event_config_prefix}-web"
}

resource "google_sql_database_instance" "config_db" {
  name             = "event-config-db-${var.environment}"
  database_version = "POSTGRES_13"
  region           = "asia-east1"

  settings {
    tier      = "db-f1-micro"
    disk_type = "PD_HDD"
    ip_configuration {
      authorized_networks {
        name  = "Tagtoo Office"
        value = "***************"
      }
    }
  }
  deletion_protection = true
}

resource "google_sql_database" "config_db" {
  name     = "config"
  instance = google_sql_database_instance.config_db.name
}

resource "google_sql_user" "config_db_user" {
  name     = "config-api"
  instance = google_sql_database_instance.config_db.name
  password = "GWXYXyCuPmW8Mzev"
}

resource "google_storage_bucket" "config_api_statics" {
  name          = "config-api-statics-${var.environment}"
  storage_class = "REGIONAL"
  location      = "ASIA-EAST1"
  force_destroy = true
}

resource "google_storage_bucket_access_control" "public_rule" {
  bucket = google_storage_bucket.config_api_statics.name
  role   = "READER"
  entity = "allUsers"
}

resource "kubernetes_config_map_v1" "event_config_api_env" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = local.event_config_api_env
  }
  data = {
    DEBUG             = var.event_config_api.web.env.debug
    SECRET_KEY        = var.event_config_api.web.env.secret_key
    DOMAIN            = "https://event-cnf.tagtoo.co"
    ALLOWED_HOSTS     = var.event_config_api.web.env.allowed_hosts
    GS_PROJECT_ID     = var.gcp_project
    GS_STATIC_BUCKET  = google_storage_bucket.config_api_statics.name
    POSTGRES_HOST     = "127.0.0.1"
    POSTGRES_DB       = google_sql_database.config_db.name
    POSTGRES_USER     = google_sql_user.config_db_user.name
    POSTGRES_PASSWORD = google_sql_user.config_db_user.password
  }
}

resource "kubernetes_deployment_v1" "event_config_api_web" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = local.event_config_api_web
    labels = {
      "app.kubernetes.io/name" = local.event_config_api_web
    }
  }
  spec {
    replicas = 1
    selector {
      match_labels = {
        "app.kubernetes.io/name" = local.event_config_api_web
      }
    }
    template {
      metadata {
        labels = {
          "app.kubernetes.io/name" = local.event_config_api_web
        }
      }
      spec {
        container {
          image = "gcr.io/cloud-sql-connectors/cloud-sql-proxy:2.16.0"
          name  = "sql-proxy"
          args  = ["--structured-logs", "--port=5432", "--max-sigterm-delay=120s", "${var.gcp_project}:asia-east1:${google_sql_database_instance.config_db.name}"]
        }
        container {
          image             = "asia.gcr.io/tagtoo-tracking/event-config-api:${var.event_config_api.web.image_tag}"
          image_pull_policy = "Always"
          name              = "web"
          command           = ["./scripts/entrypoint.sh"]
          args              = ["./scripts/gunicorn.sh"]
          env_from {
            config_map_ref {
              name = kubernetes_config_map_v1.event_config_api_env.metadata[0].name
            }
          }
          resources {
            limits = {
              cpu    = var.event_config_api.web.limits.cpu
              memory = var.event_config_api.web.limits.memory
            }
            requests = {
              cpu    = var.event_config_api.web.requests.cpu
              memory = var.event_config_api.web.requests.memory
            }
          }
          liveness_probe {
            http_get {
              path = "/liveness"
              port = 8000
            }
            timeout_seconds = 3
          }
          readiness_probe {
            http_get {
              path = "/readiness"
              port = 8000
            }
            timeout_seconds = 3
          }
        }
        node_selector = {
          "application" = "event"
          "name"        = "config-api"
        }
        toleration {
          effect   = "NoSchedule"
          key      = "name"
          operator = "Equal"
          value    = "config-api"
        }
      }
    }
  }
  lifecycle {
    ignore_changes = [
      spec[0].replicas,
    ]
  }
  wait_for_rollout = var.wait_for_rollout
}

resource "kubernetes_service_v1" "event_config_api_web" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = local.event_config_api_web
  }
  spec {
    selector = {
      "app.kubernetes.io/name" = local.event_config_api_web
    }
    type             = "NodePort"
    session_affinity = null
    port {
      port        = 80
      target_port = 8000
    }
  }
}
