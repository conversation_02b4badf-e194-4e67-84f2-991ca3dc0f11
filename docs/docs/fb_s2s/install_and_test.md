# Facebook S2S 安裝與測試指南

<script src="https://unpkg.com/mermaid@8.13.3/dist/mermaid.min.js"></script>

## Docker Compose 環境設置

S2S 微服務依賴於 Event Config 資料庫。請按照特定順序在本地啟動這些服務。

```mermaid
flowchart TB
  subgraph docker bridge network
    subgraph network:config
        config-api --- config-db[(config-db)]
    end
    subgraph network:s2s
        s2s-pubsub -- pull --> capi-sub-batch
        s2s-pubsub -- pull --> capi-sub-singleton
        s2s-pubsub -- pull --> lta-sub
        capi-sub-batch -- query --> s2s-db[(s2s-db)]
        capi-sub-singleton -- query --> s2s-db
        capi-sub-batch -- publish --> s2s-broker
        capi-sub-singleton -- publish --> s2s-broker
        lta-sub -- query --> fake-GCS
        lta-sub -- publish --> capi-sub-batch
        s2s-broker -- subscribe --> capi-failed-batch-handler
    end
    config-db -. sync .-> s2s-db
  end
  config-api o-. 10001:8000 .-o Host
```

### 1. 啟動 Event Config 服務
```shell
docker-compose -f docker-compose.config.yml up
```

### 2. 啟動 S2S 服務
```shell
docker-compose -f docker-compose.s2s.yml up
```

### 3. 關閉服務
```shell
docker-compose -f docker-compose.config.yml down
docker-compose -f docker-compose.s2s.yml down
```

!!! info "Pub/Sub 模擬器"
    Pub/Sub 本地服務使用 `messagebird/gcloud-pubsub-emulator` 建構的模擬器。

## 單元測試

### LTA Loader 智能 Hash 處理測試

我們為 LTA Loader 的智能電話號碼 hash 處理功能提供了完整的測試套件。

#### 測試檔案位置
- **主測試檔案**: `tests/s2s/LTA/test_loader.py`
- **獨立測試檔案**: `tests/s2s/LTA/test_loader_standalone.py`
- **驗證腳本**: `tests/s2s/LTA/phone_hash_verification.py`
- **效能測試**: `tests/s2s/LTA/simple_phone_test.py`

#### 使用 Docker Compose 執行測試

**執行完整測試套件**:
```shell
docker-compose -f docker-compose.s2s.yml run --rm \
  -v "$(pwd)/tests/s2s/LTA:/app/tests_lta" \
  s2s python -m pytest tests_lta/test_loader.py -v
```

**執行特定測試**:
```shell
docker-compose -f docker-compose.s2s.yml run --rm \
  -v "$(pwd)/tests/s2s/LTA:/app/tests_lta" \
  s2s python -m pytest tests_lta/test_loader.py::TestDataLoaderPhoneHashing::test_integration_real_hash_function -v
```

**執行獨立測試**:
```shell
docker-compose -f docker-compose.s2s.yml run --rm \
  -v "$(pwd)/tests/s2s/LTA/test_loader_standalone.py:/app/test_loader_standalone.py" \
  s2s python -m pytest test_loader_standalone.py -v
```

#### 測試覆蓋範圍

測試套件涵蓋以下功能：

1. **Hash 檢測邏輯**
   - 有效的 SHA256 hash 識別
   - 無效 hash 的正確處理
   - 大小寫處理

2. **智能電話號碼編碼**
   - 已 hash 號碼的直接使用
   - 原始號碼的正規化和 hash
   - 格式化號碼的處理

3. **AVRO 資料處理**
   - 混合資料的正確處理
   - 空資料的處理
   - 批次資料處理

4. **邊界情況**
   - 空字串處理
   - 無效輸入處理
   - 類型安全性

5. **整合測試**
   - 真實 hash 函數驗證
   - 一致性檢查
   - 效能驗證

#### 測試結果範例

```
============================= test session starts ==============================
platform linux -- Python 3.8.20, pytest-6.2.1, py-1.11.0, pluggy-1.0.0.dev0 -- /usr/local/bin/python
cachedir: .pytest_cache
rootdir: /app, configfile: pytest.ini
collecting ... collected 9 items

tests_lta/test_loader.py::TestDataLoaderPhoneHashing::test_compose_pixel_datum_empty_phones PASSED
tests_lta/test_loader.py::TestDataLoaderPhoneHashing::test_compose_pixel_datum_from_avro_phone_processing PASSED
tests_lta/test_loader.py::TestDataLoaderPhoneHashing::test_integration_real_hash_function PASSED
tests_lta/test_loader.py::TestDataLoaderPhoneHashing::test_is_already_hashed_invalid_hash PASSED
tests_lta/test_loader.py::TestDataLoaderPhoneHashing::test_is_already_hashed_valid_hash PASSED
tests_lta/test_loader.py::TestDataLoaderPhoneHashing::test_safe_encode_phone_already_hashed PASSED
tests_lta/test_loader.py::TestDataLoaderPhoneHashing::test_safe_encode_phone_edge_cases PASSED
tests_lta/test_loader.py::TestDataLoaderPhoneHashing::test_safe_encode_phone_raw_numbers PASSED
tests_lta/test_loader.py::TestDataLoaderPhoneHashing::test_safe_encode_phone_warning_logging PASSED

======================== 9 passed, 6 warnings in 1.00s ========================
```

## 整合測試

### 使用 Shell 腳本測試跨服務功能

在 `tests/s2s` 資料夾中啟動服務並執行以下命令：

* **測試 Batch 通道**
    ```shell
    ./test_batch.sh
    ```

* **測試 Singleton 通道**
    ```shell
    ./test_failed_batch.sh
    ```

* **測試 LTA S2S**
    ```shell
    ./test_lta.sh
    ```

## 開發指南

### 新增測試

1. **單元測試**: 在 `tests/s2s/LTA/` 目錄中新增測試檔案
2. **命名慣例**: 使用 `test_*.py` 格式
3. **測試類別**: 繼承 `unittest.TestCase`
4. **Mock 使用**: 適當使用 `unittest.mock` 來隔離測試

### 測試最佳實踐

1. **環境隔離**: 所有測試都在 Docker 環境中執行
2. **資料安全**: 使用模擬資料，不影響正式環境
3. **完整覆蓋**: 包含正常情況、邊界情況和錯誤情況
4. **效能驗證**: 對關鍵功能進行效能測試

### 持續整合

測試套件已整合到 PR 流程中，確保：
- 所有新功能都有相應測試
- 修改不會破壞現有功能
- 效能改進得到量化驗證