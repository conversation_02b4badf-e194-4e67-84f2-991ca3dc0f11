from enum import Enum


class PaymentMethodEnum(str, Enum):
    CREDIT_CARD = 'credit_card'
    REMIT = 'remit'
    COD = 'cod'
    TELECOM = 'telecom'
    E_PAYMENT = 'e_payment'
    GIFT_CARD = 'gift_card'


class ShippingMethodEnum(str, Enum):
    DROP_SHIPPING = 'drop_shipping'
    IN_STORE = 'in_store'
    SEVEN_ELEVEN = '711'
    CVS = 'cvs'


class AuthMethodEnum(str, Enum):
    MEMBER = 'member'
    FACEBOOK = 'facebook'
    GOOGLE = 'google'
    APPLE = 'apple'
    LINE = 'line'


class ItemAvailabilityEnum(str, Enum):
    IN_STOCK = 'in_stock'
    OUT_OF_STOCK = 'out_of_stock'
