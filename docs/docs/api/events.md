## add_payment_info

Required: `custom_data.payment_method`

Optional: `value`, `currency`, `items`

## add_shipping_info

Required: `value`, `currency`, `items`, `custom_data.shipping_method`

## add_to_cart

Required: `value`, `currency`, `items`

## add_to_wishlist

Required: `value`, `currency`, `items`

## checkout

Required: `value`, `currency`, `items`

## lead

Required: `custom_data.campaign_name`

Optional: `value`, `currency`

## copy

Required: `custom_data.copy_string`

## focus

Required: `custom_data.status` or `custom_data.focus_minutes`

!!! info
    This event should trigger at:
    1. Page enter: `status=true`, or
    2. Page exit: `status=false`, or
    3. Every n minutes stays in the same page: `focus_minutes=n`

## login

Required: `custom_data.auth_method`

## page_view

## purchase

Required: `value`, `currency`, `items`, `custom_data.order_id`

## refund

Required: `value`, `currency`, `custom_data.order_id`

Optional: `items`

## register

Required: `custom_data.auth_method`

## remove_from_cart

Required: `value`, `currency`, `items`

## scroll

Required: `custom_data.document_height`, `custom_data.scroll_top`

!!! info
    This event should trigger at 
    1. Scroll exceed 10% document height, or 
    2. Scroll exceed window inner height

## search

Required: `custom_data.search_string`

## share

## switch_tab

Required: `custom_data.status`

## view_cart

Required: `value`, `currency`, `items`

## view_item_list

Required: `custom_data.breadcrumb`, `items`

!!! warning
    `items` can only contain max 10 items

## view_item

Required: `value`, `currency`, `items`

## view_promotion

Required: `items`, `custom_data.campaign_name` 

!!! warning
    `items` can only contain max 10 items