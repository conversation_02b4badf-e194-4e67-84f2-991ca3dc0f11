from pytest_cases import parametrize_with_cases

from app.schemas.tagtoo import TagtooEvent

from .cases.case_test_model import (
    TagtooEventModelFailedCases,
    TagtooEventModelOKCases,
)


class TestTagtooEvent(object):
    @parametrize_with_cases(
        'input',
        cases=[
            TagtooEventModelOKCases,
            TagtooEventModelFailedCases,
        ],
    )
    def test_event_data_validation(
        self,
        input,
    ):
        TagtooEvent(**input)
