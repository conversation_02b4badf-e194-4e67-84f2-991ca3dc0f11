#!/bin/bash

error() { echo -e "\e[91m$1\e[m"; exit 1; }
success() { echo -e "\e[92m$1\e[m"; }

function init_config_data() {
docker exec -i config-api python manage.py shell << END
import sys
from apps.event_api.models import Client, AccessToken, Scope
try:
  AccessToken.objects.all().delete(soft=False)
  token = AccessToken.objects.create(
    ec_id=$1,
    token='$2',
    client=Client.objects.first(),
  )
  token.scopes.add(*Scope.objects.all())
  sys.exit(0)
except Exception as exc:
  print(exc)
  sys.exit(-1)
END
}

function update_api_db() {
docker exec -i api python << END
import sys
import asyncio
from app.utils import update_event_configs
async def update():
  await update_event_configs()
try:
  asyncio.run(update())
  sys.exit(0)
except Exception as exc:
  print(exc)
  sys.exit(-1)
END
}

function post_test_data() {
docker run -i -v "$1":/test_data.json --network config nyurik/alpine-python3-requests python3 << END
import sys
import json
import requests
import time
try:
  with open('/test_data.json') as file:
    data = json.load(file)
    url = "http://api:8000/event/$3"
    events = data if isinstance(data, list) else [data]
    for event in events:
      event.update({
        'event_time': int(time.time()),
        'token': '$2'
      })
      res = requests.post(url=url, json=event)
      assert res.status_code == 200, print(res.content)
  sys.exit(0)
except Exception as exc:
  print(exc)
  sys.exit(-1)
END
}

function setup_subscription() {
docker exec -i api python << END
import sys
from datetime import datetime
from google.cloud import pubsub_v1
from google.api_core.exceptions import NotFound
subscriber = pubsub_v1.SubscriberClient()
try:
  try:
    subscriber.get_subscription(request={'subscription': '$2'})
  except NotFound:
    subscriber.create_subscription(
        request={'topic': '$1', 'name': '$2'}
    )
  subscriber.seek(request={'subscription': '$2', 'time': datetime.now()})
  sys.exit(0)
except Exception as exc:
  print(exc)
  sys.exit(-1)
END
}

function check_subscription_message_count() {
docker exec -i api python << END
import sys
from google.cloud import pubsub_v1
subscriber = pubsub_v1.SubscriberClient()
try:
  res = subscriber.pull(request={'subscription': '$1', 'max_messages': 100}, timeout=3)
  count = len(res.received_messages)
  assert count == $2, f"{count} != $2"
  ack_ids = [msg.ack_id for msg in res.received_messages]
  if ack_ids:
  # nack the messages
    subscriber.modify_ack_deadline(
      request={
        "subscription": '$1',
        "ack_ids": ack_ids,
        "ack_deadline_seconds": 0,
      }
    )
  sys.exit(0)
except Exception as exc:
  print(exc)
  sys.exit(-1)
END
}

function validate_event_message() {
docker exec -i api python << END
import sys
import json
from google.cloud import pubsub_v1
subscriber = pubsub_v1.SubscriberClient()
try:
  res = subscriber.pull(request={'subscription': '$1', 'max_messages': 100}, timeout=3)
  for received_message in res.received_messages:
    message = received_message.message
    data = json.loads(message.data)
    assert 'ec_id' in message.attributes, 'AssertionError: Attribute ec_id missing.'
    ec_id = int(message.attributes['ec_id'])
    assert ec_id == $2, f'AssertionError: {ec_id} != $2.'
    assert 'version' in message.attributes, 'AssertionError: Attribute version missing.'
    version = str(message.attributes['version'])
    assert version == '$3', f'AssertionError: {version} != "$2".'
  sys.exit(0)
except Exception as exc:
  print(exc)
  sys.exit(-1)
END
}