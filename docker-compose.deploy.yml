version: '3.7'

volumes:
  terraform_local_cache: {}

services:
  deploy:
    container_name: deploy
    build:
      context: ./deploy
    volumes:
      # Terraform Meta Data
      - terraform_local_cache:/src/.terraform
      # Source Code
      - ./deploy:/src
      # Service Account
      - ./gcloud/sa/terraform-user.json:/credentials/terraform-user.json
      # Environment
      - ./scripts/config_gcloud.sh:/scripts/config_gcloud.sh
      # Reference Files
      - ./helm:/helm
    environment:
      - GOOGLE_CLOUD_KEYFILE_JSON=/credentials/terraform-user.json
      - GOOGLE_BACKEND_CREDENTIALS=/credentials/terraform-user.json
      - GCP_PROJECT_NAME=tagtoo-tracking
      - GCP_COMPUTE_REGION=asia-east1
      - GCP_COMPUTE_ZONE=asia-east1-a
    entrypoint: [""]
    command:
      - /bin/sh
      - -c
      - |
        /scripts/config_gcloud.sh
        tail -F /dev/null
    networks:
      - deploy

  dataflow:
    container_name: dataflow
    build:
      context: ./dataflow
    volumes:
      # Source Code
      - ./dataflow:/src
      # Service Account
      - ./gcloud/sa/dataflow-template-manager.json:/credentials/dataflow-template-manager.json
      # Environment
      - ./scripts/config_gcloud.sh:/scripts/config_gcloud.sh
    environment:
      - GOOGLE_APPLICATION_CREDENTIALS=/credentials/dataflow-template-manager.json
      - GCP_PROJECT_NAME=tagtoo-tracking
      - GCP_COMPUTE_REGION=asia-east1
      - GCP_COMPUTE_ZONE=asia-east1-a
    command:
      - /bin/sh
      - -c
      - |
        /scripts/config_gcloud.sh
        tail -F /dev/null
    networks:
      - deploy

networks:
  deploy:
    driver: bridge