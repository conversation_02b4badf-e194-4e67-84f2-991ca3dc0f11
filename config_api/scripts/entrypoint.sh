#!/usr/bin/env bash

set -o errexit
set -o pipefail
cmd="$@"

function postgres_ready(){
python << END
import sys
import psycopg2
import environ
try:
    env = environ.Env()
    host = env.str('$1')
    dbname = env.str('$2')
    user = env.str('$3')
    password = env.str('$4')
    port = env.str('$5', 5432)
    conn = psycopg2.connect(dbname=dbname, user=user, password=password, host=host, port=port)
except psycopg2.OperationalError:
    sys.exit(-1)
sys.exit(0)
END
}

until postgres_ready POSTGRES_HOST POSTGRES_DB POSTGRES_USER POSTGRES_PASSWORD POSTGRES_PORT; do
  >&2 echo "Postgres: Database is unavailable - sleeping"
  sleep 1
done

>&2 echo "Postgres: Database is up - continuing..."

exec $cmd