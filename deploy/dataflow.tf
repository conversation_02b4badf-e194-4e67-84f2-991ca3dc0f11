# https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/storage_bucket
resource "google_storage_bucket" "dataflow_fb_compressor_temp_bucket" {
  name          = "fb-capi-compressor-dataflow-temp-bucket-${var.environment}"
  location      = "US"
  force_destroy = true
}

# https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/dataflow_job
resource "google_dataflow_job" "facebook_capi_compressor" {
  count             = var.environment == "prod" ? 1 : 0
  name              = "fb-capi-compressor-${var.environment}"
  on_delete         = "cancel"
  region            = "asia-east1"
  zone              = "asia-east1-a"
  max_workers       = var.dataflow_facebook_capi_compressor.max_workers
  machine_type      = var.dataflow_facebook_capi_compressor.machine_type
  template_gcs_path = "gs://tagtoo-dataflow-templates/templates/prod/2022-03-31/230601/Facebook_CAPI_Compressor"
  temp_gcs_location = "gs://${google_storage_bucket.dataflow_fb_compressor_temp_bucket.name}"
  # TODO: These parameters currently doesn't have any effect to the template, just for demonstration, because
  #       currently ReadFromPubSub and WriteToPubSub connector doesn't accept ValueProvider in Python SDK
  #       see: https://cloud.google.com/dataflow/docs/guides/templates/creating-templates#pipeline-io-and-runtime-parameters
  parameters = {
    input_subscription = "projects/${var.gcp_project}/subscriptions/${google_pubsub_subscription.facebook_capi_s2s_to_compress.name}"
    output_topic       = "projects/${var.gcp_project}/topics/${google_pubsub_topic.facebook_capi_compressed.name}"
    window_size        = 5
  }
  # Only applicable when updating a pipeline
  transform_name_mapping = {}
  lifecycle {
    ignore_changes = [
      additional_experiments,
    ]
  }
}
