[mysql]
default-character-set = utf8
local-infile

[mysqld]
bind-address = 0.0.0.0
skip-name-resolve
key_buffer_size = 150M
max_allowed_packet = 1M
max_connections = 100
max_heap_table_size = 4M
net_buffer_length = 2K
query_cache_limit = 512K
query_cache_size = 0
query_cache_type = 0
read_buffer_size = 256K
read_rnd_buffer_size = 256K
sort_buffer_size = 64K
table_open_cache = 100
thread_stack = 128K
collation_server = utf8_bin
character-set-server = utf8
innodb = off
performance_schema = on
default_storage_engine = MyISAM