#!/usr/bin/env python3
"""
更安全的 Pub/Sub 訂閱 seek 操作腳本。
在執行 seek 前，會先嘗試讀取並保存當前未確認的消息。

用法：
    python safe_seek.py --subscription tagtoo-event-to-bigquery-v1-prod --backup-file backup_messages.json
    
    # 使用特定時間點進行 seek
    python safe_seek.py --subscription tagtoo-event-to-bigquery-v1-prod --seek-time "2025-05-08T00:00:00Z"
    
    # 乾跑模式（不執行實際的 seek 操作）
    python safe_seek.py --subscription tagtoo-event-to-bigquery-v1-prod --dry-run
"""

import argparse
import json
import datetime
import os
from google.cloud import pubsub_v1

def parse_args():
    parser = argparse.ArgumentParser(description='Safely seek a Pub/Sub subscription')
    parser.add_argument('--subscription', required=True, help='Subscription ID')
    parser.add_argument('--project', default='tagtoo-tracking', help='GCP project ID')
    parser.add_argument('--backup-dir', default='pubsub_backups', 
                       help='Directory to save backup messages')
    parser.add_argument('--backup-file', default='pubsub_backup_{timestamp}.json', 
                       help='File to save backup messages (use {timestamp} for current time)')
    parser.add_argument('--max-messages', type=int, default=1000, 
                       help='Maximum number of messages to backup')
    parser.add_argument('--seek-time', help='Time to seek to (ISO format, e.g., 2025-05-08T00:00:00Z). Default is now.')
    parser.add_argument('--dry-run', action='store_true', help='Dry run mode (do not perform seek)')
    return parser.parse_args()

def backup_messages(project, subscription, max_messages, backup_dir, backup_file):
    """備份訂閱中的未確認消息"""
    subscriber = pubsub_v1.SubscriberClient()
    subscription_path = subscriber.subscription_path(project, subscription)
    
    # 替換時間戳
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_file = backup_file.replace('{timestamp}', timestamp)
    
    # 確保備份目錄存在
    os.makedirs(backup_dir, exist_ok=True)
    backup_path = os.path.join(backup_dir, backup_file)
    
    print(f"正在從訂閱 {subscription} 中備份最多 {max_messages} 條消息...")
    
    try:
        response = subscriber.pull(
            request={"subscription": subscription_path, "max_messages": max_messages},
            timeout=90.0
        )
        
        messages = []
        for received_message in response.received_messages:
            message = received_message.message
            try:
                data = message.data.decode('utf-8')
                # 嘗試解析 JSON
                try:
                    data_json = json.loads(data)
                    data = data_json  # 如果成功解析，使用解析後的 JSON
                except json.JSONDecodeError:
                    pass  # 如果不是 JSON，保持原始字符串
            except UnicodeDecodeError:
                data = f"<binary data, length: {len(message.data)}>"
                
            attributes = dict(message.attributes)
            messages.append({
                'message_id': message.message_id,
                'publish_time': message.publish_time.isoformat(),
                'data': data,
                'attributes': attributes,
                'ack_id': received_message.ack_id
            })
            
        # 將消息寫入文件
        with open(backup_path, 'w') as f:
            json.dump(messages, f, indent=2)
            
        print(f"已備份 {len(messages)} 條消息到文件 {backup_path}")
        
        # 修改確認期限，以便消息不會因為我們的拉取而被確認
        ack_ids = [msg.ack_id for msg in response.received_messages]
        if ack_ids:
            subscriber.modify_ack_deadline(
                request={
                    "subscription": subscription_path,
                    "ack_ids": ack_ids,
                    "ack_deadline_seconds": 0
                }
            )
            
        return len(messages)
    except Exception as e:
        print(f"備份消息時出錯：{e}")
        return 0
    finally:
        subscriber.close()

def perform_seek(project, subscription, seek_time=None):
    """執行 seek 操作"""
    subscriber = pubsub_v1.SubscriberClient()
    subscription_path = subscriber.subscription_path(project, subscription)
    
    try:
        if seek_time:
            # 轉換為 datetime 對象
            seek_time_dt = datetime.datetime.fromisoformat(seek_time.replace('Z', '+00:00'))
            print(f"正在將訂閱 {subscription} 的時間點設置為 {seek_time}...")
            subscriber.seek(
                request={"subscription": subscription_path, "time": seek_time_dt}
            )
        else:
            # 使用當前時間
            now = datetime.datetime.now(datetime.timezone.utc)
            print(f"正在將訂閱 {subscription} 的時間點設置為當前時間 {now.isoformat()}...")
            subscriber.seek(
                request={"subscription": subscription_path, "time": now}
            )
        print("Seek 操作成功完成")
    except Exception as e:
        print(f"執行 seek 操作時出錯：{e}")
    finally:
        subscriber.close()

def main():
    args = parse_args()
    
    # 備份消息
    message_count = backup_messages(
        args.project,
        args.subscription,
        args.max_messages,
        args.backup_dir,
        args.backup_file
    )
    
    if message_count > 0:
        print(f"警告：訂閱中有 {message_count} 條未確認的消息將被跳過！")
        confirm = input("是否繼續執行 seek 操作？(y/N): ")
        if confirm.lower() != 'y':
            print("操作已取消")
            return
    
    # 執行 seek
    if not args.dry_run:
        perform_seek(args.project, args.subscription, args.seek_time)
    else:
        print("乾跑模式，未執行實際的 seek 操作")

if __name__ == '__main__':
    main()
