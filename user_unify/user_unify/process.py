import logging
import os
import time
from datetime import date
from typing import List

from google.cloud import bigquery
from google.cloud.exceptions import NotFound

from . import settings
from .utils import resolve_multiple_candidates

logger = logging.getLogger(__file__)
SQL_PATH = os.path.join(os.path.dirname(__file__), 'bigquery', 'sql')


def get_sql(
    filepath: str,
    context: dict = None,
) -> str:
    with open(filepath, 'rt') as f:
        sql = f.read()

    context = context or {}
    for k, v in context.items():
        sql = sql.replace(f'${k}', v)
    return sql


def execute_sql(
    client: bigquery.Client,
    query: str,
    job_name: str = None,
    sleep_time: int = 5,
    query_time_out: int = 300,
    job_config: bigquery.QueryJobConfig = None,
    event_date: date = '',
):
    job_name = job_name or 'sql'
    log_job_name = job_name.replace("_", " ").capitalize()
    date_info = f'({event_date})' if event_date else ''
    start_info = f'Start to execute {log_job_name} job. {date_info}'
    not_down_info = f'{log_job_name} job is still running ... {date_info}'
    finish_info = f'{log_job_name} job is finished! {date_info}'

    logger.info(start_info)
    sql_job = client.query(
        query,
        timeout=query_time_out,
        job_id_prefix=f'tagtoo_uu__{job_name}_',
        job_config=job_config,
    )
    while not sql_job.done():
        logger.info(not_down_info)
        time.sleep(sleep_time)
    result = sql_job.result()
    logger.info(finish_info)
    return result


def get_daily_grouping_result(
    client: bigquery.Client,
    dataset: str,
    grouping_result_table: str,
    event_date: date,
):
    job_config = bigquery.QueryJobConfig(
        destination=grouping_result_table,
        write_disposition=bigquery.WriteDisposition.WRITE_TRUNCATE,
    )

    sql = f'''
        SELECT *
        FROM `{dataset}.get_grouping_result_by_date`('{event_date}', '{event_date}')
    '''

    _ = execute_sql(
        client=client,
        query=sql,
        job_name='create_daily_grouping_result',
        sleep_time=30,
        query_time_out=None,
        job_config=job_config,
        event_date=event_date,
    )


def check_table_exist(
    client: bigquery.Client,
    table_name: str,
) -> bool:
    exist = False
    try:
        _ = client.get_table(table_name)
    except NotFound:
        logger.info(f'Table not found ({table_name}). Shell create table.')
    else:
        exist = True
    return exist


def generate_group_result_snapshot(
    event_date: date,
    client: bigquery.Client,
    dataset: str,
    snapshot_dataset: str,
    expiration_interval: int = 30,
) -> None:
    create_snapshot_filepath = os.path.join(SQL_PATH, 'create_group_table_snapshot.sql')
    groups_table = f'{dataset}.{settings.BQ_TABLE_USER_UNIFY_GROUPS}'
    snapshot_table = (
        f'{snapshot_dataset}.user_unify_groups_{event_date.strftime("%Y_%m_%d")}'
    )

    snapshot_exist = check_table_exist(client, snapshot_table)
    if snapshot_exist:
        logger.info(
            f'Snapshot table {snapshot_table} exist. Shell delete and recreate.'
        )
        client.delete_table(snapshot_table)

    snapshot_sql = get_sql(
        create_snapshot_filepath,
        context={
            'snapshot_table': snapshot_table,
            'groups_table': groups_table,
            'expiration_interval': str(expiration_interval),
        },
    )

    _ = execute_sql(
        client=client,
        query=snapshot_sql,
        job_name='create_snapshot',
        event_date=event_date,
    )

    return


def rollback_user_unify_groups_table(
    event_date: date,
    client: bigquery.Client,
    dataset: str,
    snapshot_dataset: str,
):
    rollback_filepath = os.path.join(SQL_PATH, 'rollback_user_unify_groups.sql')
    snapshot_table = (
        f'{snapshot_dataset}.user_unify_groups_{event_date.strftime("%Y_%m_%d")}'
    )
    groups_table = f'{dataset}.{settings.BQ_TABLE_USER_UNIFY_GROUPS}'

    snapshot_exist = check_table_exist(client, snapshot_table)
    if snapshot_exist:
        rollback_sql = get_sql(
            rollback_filepath,
            context={
                'snapshot_table': snapshot_table,
                'groups_table': groups_table,
            },
        )
        logger.info(
            f'Snapshot table {snapshot_table} exist. '
            'Start rolling back user unify groups table.'
        )
        _ = execute_sql(
            client=client,
            query=rollback_sql,
            job_name='rollback_groups',
            event_date=event_date,
        )
    else:
        logger.warning(
            f'Snapshot table {snapshot_table} is not exist. Rollback job fail.'
        )

    return


def determine_groups(
    event_date: date,
    client: bigquery.Client,
    dataset: str,
    grouping_result_dataset: str,
    recalc_daily_grouping_result: bool = False,
):
    '''The main process of candidate groups determination

    Steps:
    * Calculate the daily grouping result from event data (or directly get if it already exists)
    * Merge new entities into existed grouping result table (user_unify_groups)
    * Predict the final group for groups with multiple candidates (Union Find)
    * Write the result into an intermediate table (Truncate -> Load)
    * Merge the above result into user_unify_groups
    * Delete the useless rows (The groups that should be merged into another group)

    Args:
        event_date (date): The date of event data from which the entity data is extracted
        client (bigquery.Client): The bigquery client object
        dataset (str): The base dataset string
        grouping_result_dataset (str): The base daily grouping result dataset string
        recalc_daily_grouping_result (bool): If True, recalculate the daily grouping result
            and overwrite the existed one.
    '''

    grouping_result_table = f'{grouping_result_dataset}.result-{event_date}'

    # Get daily grouping result and store to a table
    if recalc_daily_grouping_result:
        # Overwrite the existed grouping result anyway if the flag is set.
        get_daily_grouping_result(
            client=client,
            dataset=dataset,
            grouping_result_table=grouping_result_table,
            event_date=event_date,
        )
    else:
        # Check if the table really exists
        try:
            table = client.get_table(grouping_result_table)
        except NotFound:
            logger.info(
                f'Table not found ({grouping_result_table}). Shell recalculate the table.'
            )
            get_daily_grouping_result(
                client=client,
                dataset=dataset,
                grouping_result_table=grouping_result_table,
                event_date=event_date,
            )
            table = client.get_table(grouping_result_table)
        else:
            logger.info(f'Directly use existed grouping result table: {table}.')
        finally:
            logger.info(
                f'{table} has {table.num_rows:,} rows with {table.num_bytes:,} bytes.'
            )

    # Merge new entities
    merge_job_filepath = os.path.join(SQL_PATH, 'merge_new_entities.sql')
    merge_sql = get_sql(
        merge_job_filepath,
        context={
            'start_date': event_date.strftime('%Y-%m-%d'),
            'end_date': event_date.strftime('%Y-%m-%d'),
            'grouping_result_table': grouping_result_table,
            'groups_table': f'{dataset}.{settings.BQ_TABLE_USER_UNIFY_GROUPS}',
        },
    )

    _ = execute_sql(
        client=client,
        query=merge_sql,
        job_name='merge_groups',
        query_time_out=14400,
        event_date=event_date,
    )

    # Predict candidates
    predict_job_filepath = os.path.join(SQL_PATH, 'predict_candidate.sql')
    predict_sql = get_sql(
        predict_job_filepath,
        context={
            'start_date': event_date.strftime('%Y-%m-%d'),
            'end_date': event_date.strftime('%Y-%m-%d'),
            'get_entity_function': f'{dataset}.{settings.BQ_TABLE_FUNCTION_GET_ENTITY}',
            'groups_table': f'{dataset}.{settings.BQ_TABLE_USER_UNIFY_GROUPS}',
        },
    )

    multiple_candidate_data: List[dict] = execute_sql(
        client=client,
        query=predict_sql,
        job_name='predict_candidate',
        query_time_out=300,
        sleep_time=10,
        event_date=event_date,
    )

    # Resolve candidate > 1 (Union Find) and write to intermeidate table
    logger.info('Processing intermediate table ...')
    result: List[dict] = [
        {'predict_result': result_id, 'group_id': group_id}
        for group_id, result_id in resolve_multiple_candidates(
            multiple_candidate_data
        ).items()
    ]

    # Writing intermediate table (Truncate -> Load)
    truncate_job = client.query(
        f'TRUNCATE TABLE `{dataset}._intermediate_predict`',
        timeout=120,
        job_id_prefix='tagtoo_uu__truncate_intermediate_',
    )
    truncate_job.result()

    load_job = client.load_table_from_json(
        json_rows=result,
        destination=f'{dataset}._intermediate_predict',
        job_id_prefix='tagtoo_uu__load_intermediate_',
    )
    load_job.result()
    logger.info(f'Intermediate prediction result has been loaded ({event_date})')

    # Merge resolved candidates
    resolved_filepath = os.path.join(SQL_PATH, 'merge_resolved_candidates.sql')
    resolved_sql = get_sql(
        resolved_filepath,
        context={
            'intermediate_table': f'{dataset}.{settings.BQ_TABLE_PREDICT_INTERMEDIATE}',
            'groups_table': f'{dataset}.{settings.BQ_TABLE_USER_UNIFY_GROUPS}',
        },
    )

    _ = execute_sql(
        client=client,
        query=resolved_sql,
        job_name='merge_resolved_candidate',
        query_time_out=1800,
        event_date=event_date,
    )

    # Delete useless candidates
    delete_filepath = os.path.join(SQL_PATH, 'delete_useless_group.sql')
    delete_sql = get_sql(
        delete_filepath,
        context={
            'intermediate_table': f'{dataset}.{settings.BQ_TABLE_PREDICT_INTERMEDIATE}',
            'groups_table': f'{dataset}.{settings.BQ_TABLE_USER_UNIFY_GROUPS}',
        },
    )
    _ = execute_sql(
        client=client,
        query=delete_sql,
        job_name='delete_useless_groups',
        query_time_out=1800,
        event_date=event_date,
    )


def generate_group_permanent_table(
    client: bigquery.Client,
    dataset: str,
):
    group_permanent_table = f'{dataset}.{settings.BQ_TABLE_USER_UNIFY_GROUP_PERMANENT}'
    user_unify_groups_table = f'{dataset}.{settings.BQ_TABLE_USER_UNIFY_GROUPS}'
    gp_table = client.get_table(group_permanent_table)

    sql = f'''
        SELECT
            permanent,
            group_id
        FROM
            `{user_unify_groups_table}`,
            `{user_unify_groups_table}`.permanents permanent
    '''

    job_config = bigquery.QueryJobConfig(
        destination=group_permanent_table,
        write_disposition=bigquery.WriteDisposition.WRITE_TRUNCATE,
    )
    _ = execute_sql(
        client=client,
        query=sql,
        job_name='generate_group_permanent',
        query_time_out=None,
        job_config=job_config,
    )

    logger.info(
        f'{gp_table.num_rows:,} rows with {gp_table.num_bytes:,} bytes '
        f'has been written into {gp_table.table_id}'
    )
