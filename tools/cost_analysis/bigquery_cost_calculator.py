#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BigQuery Cost Calculator

Calculate the cost impact of dual-write architecture based on actual BigQuery usage
"""

import json
import subprocess
import sys
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BigQueryCostCalculator:
    """BigQuery 成本計算器"""

    # BigQuery 定價 (美元，基於 2025 年定價)
    PRICING = {
        'storage_active_gb_month': 0.02,      # $0.02 per GB/month (active storage)
        'storage_longterm_gb_month': 0.01,    # $0.01 per GB/month (long-term storage)
        'query_tb': 5.0,                      # $5.00 per TB processed
        'streaming_insert_gb': 0.05,          # $0.05 per GB (streaming inserts)
        'batch_insert_gb': 0.0,               # Free (batch inserts)
    }

    def __init__(self, project_id: str = "tagtoo-tracking"):
        self.project_id = project_id

    def get_table_info(self, dataset_id: str, table_id: str) -> Dict[str, Any]:
        """獲取表格詳細資訊"""
        try:
            cmd = [
                'bq', 'show', '--format=json',
                f'{self.project_id}:{dataset_id}.{table_id}'
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return json.loads(result.stdout)
        except subprocess.CalledProcessError as e:
            logger.error(f"獲取表格資訊失敗: {e}")
            return {}

    def calculate_storage_cost(self, table_info: Dict[str, Any]) -> Dict[str, float]:
        """計算儲存成本"""
        if not table_info:
            return {'active_cost': 0, 'longterm_cost': 0, 'total_cost': 0}

        # 轉換 bytes 到 GB
        active_gb = int(table_info.get('numActivePhysicalBytes', 0)) / (1024**3)
        longterm_gb = int(table_info.get('numLongTermPhysicalBytes', 0)) / (1024**3)

        # 計算月度成本
        active_cost = active_gb * self.PRICING['storage_active_gb_month']
        longterm_cost = longterm_gb * self.PRICING['storage_longterm_gb_month']

        return {
            'active_gb': active_gb,
            'longterm_gb': longterm_gb,
            'active_cost': active_cost,
            'longterm_cost': longterm_cost,
            'total_cost': active_cost + longterm_cost
        }

    def estimate_query_cost(self, query: str) -> float:
        """估算查詢成本"""
        try:
            cmd = [
                'bq', 'query', '--dry_run', '--use_legacy_sql=false',
                '--format=json', query
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            query_info = json.loads(result.stdout)

            bytes_processed = int(query_info.get('totalBytesProcessed', 0))
            tb_processed = bytes_processed / (1024**4)  # Convert to TB
            cost = tb_processed * self.PRICING['query_tb']

            return {
                'bytes_processed': bytes_processed,
                'tb_processed': tb_processed,
                'cost_usd': cost
            }
        except subprocess.CalledProcessError as e:
            logger.error(f"查詢成本估算失敗: {e}")
            return {'bytes_processed': 0, 'tb_processed': 0, 'cost_usd': 0}

    def estimate_streaming_insert_cost(self, daily_events: int, avg_event_size_kb: float) -> Dict[str, float]:
        """估算串流寫入成本"""
        # 計算每日資料量
        daily_gb = (daily_events * avg_event_size_kb) / (1024 * 1024)  # KB to GB
        monthly_gb = daily_gb * 30

        # 串流寫入成本
        monthly_cost = monthly_gb * self.PRICING['streaming_insert_gb']

        return {
            'daily_events': daily_events,
            'daily_gb': daily_gb,
            'monthly_gb': monthly_gb,
            'monthly_cost': monthly_cost
        }

    def analyze_tagtoo_event_table(self) -> Dict[str, Any]:
        """分析 tagtoo_event 表格的當前成本"""
        logger.info("分析 tagtoo_event 表格...")

        table_info = self.get_table_info('event_prod', 'tagtoo_event')
        if not table_info:
            return {}

        # 儲存成本
        storage_cost = self.calculate_storage_cost(table_info)

        # 表格統計
        total_rows = int(table_info.get('numRows', 0))
        total_logical_bytes = int(table_info.get('numTotalLogicalBytes', 0))
        total_physical_bytes = int(table_info.get('numTotalPhysicalBytes', 0))

        # 估算平均事件大小
        avg_event_size_bytes = total_logical_bytes / max(total_rows, 1)
        avg_event_size_kb = avg_event_size_bytes / 1024

        # 估算每日事件數量（基於最近的串流緩衝區）
        streaming_buffer = table_info.get('streamingBuffer', {})
        estimated_rows = int(streaming_buffer.get('estimatedRows', 0))

        # 假設串流緩衝區代表最近 24 小時的資料
        daily_events = estimated_rows if estimated_rows > 0 else 100000  # 預設值

        # 串流寫入成本
        insert_cost = self.estimate_streaming_insert_cost(daily_events, avg_event_size_kb)

        return {
            'table_stats': {
                'total_rows': total_rows,
                'total_logical_gb': total_logical_bytes / (1024**3),
                'total_physical_gb': total_physical_bytes / (1024**3),
                'avg_event_size_kb': avg_event_size_kb,
                'daily_events': daily_events
            },
            'storage_cost': storage_cost,
            'insert_cost': insert_cost,
            'total_monthly_cost': storage_cost['total_cost'] + insert_cost['monthly_cost']
        }

    def estimate_integrated_event_cost(self, tagtoo_analysis: Dict[str, Any],
                                     raw_json_size_kb: float = 0.4) -> Dict[str, Any]:
        """估算 integrated_event 表格的成本"""
        logger.info("估算 integrated_event 表格成本...")

        if not tagtoo_analysis:
            return {}

        # 基於 tagtoo_event 的統計
        daily_events = tagtoo_analysis['table_stats']['daily_events']

        # integrated_event 的平均事件大小（簡化的結構 + raw_json）
        # 假設簡化後的結構約 1KB，加上最佳化的 raw_json
        integrated_event_size_kb = 1.0 + raw_json_size_kb

        # 儲存成本（假設與 tagtoo_event 相似的壓縮比）
        compression_ratio = 0.13  # 基於 tagtoo_event 的實際壓縮比

        # 估算 integrated_event 的儲存需求
        monthly_logical_gb = (daily_events * 30 * integrated_event_size_kb) / (1024 * 1024)
        monthly_physical_gb = monthly_logical_gb * compression_ratio

        # 假設大部分是 active storage（新表格）
        storage_cost = monthly_physical_gb * self.PRICING['storage_active_gb_month']

        # 串流寫入成本
        insert_cost = self.estimate_streaming_insert_cost(daily_events, integrated_event_size_kb)

        return {
            'table_stats': {
                'daily_events': daily_events,
                'avg_event_size_kb': integrated_event_size_kb,
                'monthly_logical_gb': monthly_logical_gb,
                'monthly_physical_gb': monthly_physical_gb
            },
            'storage_cost': {
                'monthly_gb': monthly_physical_gb,
                'monthly_cost': storage_cost
            },
            'insert_cost': insert_cost,
            'total_monthly_cost': storage_cost + insert_cost['monthly_cost']
        }

    def calculate_dual_write_impact(self) -> Dict[str, Any]:
        """計算雙寫入架構的成本影響"""
        logger.info("計算雙寫入架構成本影響...")

        # 分析當前 tagtoo_event 成本
        tagtoo_analysis = self.analyze_tagtoo_event_table()

        # 估算 integrated_event 成本
        integrated_analysis = self.estimate_integrated_event_cost(tagtoo_analysis)

        if not tagtoo_analysis or not integrated_analysis:
            logger.error("無法獲取足夠的資料進行成本分析")
            return {}

        # 計算總成本影響
        current_cost = tagtoo_analysis['total_monthly_cost']
        additional_cost = integrated_analysis['total_monthly_cost']
        total_new_cost = current_cost + additional_cost

        cost_increase_pct = (additional_cost / current_cost) * 100 if current_cost > 0 else 0

        return {
            'current_costs': {
                'tagtoo_event': tagtoo_analysis
            },
            'additional_costs': {
                'integrated_event': integrated_analysis
            },
            'summary': {
                'current_monthly_cost': current_cost,
                'additional_monthly_cost': additional_cost,
                'total_new_monthly_cost': total_new_cost,
                'cost_increase_usd': additional_cost,
                'cost_increase_percentage': cost_increase_pct
            }
        }

    def generate_cost_report(self) -> str:
        """生成成本分析報告"""
        analysis = self.calculate_dual_write_impact()

        if not analysis:
            return "❌ 無法生成成本分析報告"

        summary = analysis['summary']
        tagtoo = analysis['current_costs']['tagtoo_event']
        integrated = analysis['additional_costs']['integrated_event']

        report = f"""
# BigQuery 雙寫入架構成本分析報告

## 📊 當前 tagtoo_event 表格分析

### 表格統計
- **總記錄數**: {tagtoo['table_stats']['total_rows']:,}
- **邏輯資料大小**: {tagtoo['table_stats']['total_logical_gb']:.2f} GB
- **實際儲存大小**: {tagtoo['table_stats']['total_physical_gb']:.2f} GB
- **平均事件大小**: {tagtoo['table_stats']['avg_event_size_kb']:.2f} KB
- **估算日事件數**: {tagtoo['table_stats']['daily_events']:,}

### 當前月度成本
- **儲存成本**: ${tagtoo['storage_cost']['total_cost']:.2f}
  - Active Storage: ${tagtoo['storage_cost']['active_cost']:.2f}
  - Long-term Storage: ${tagtoo['storage_cost']['longterm_cost']:.2f}
- **寫入成本**: ${tagtoo['insert_cost']['monthly_cost']:.2f}
- **總成本**: ${tagtoo['total_monthly_cost']:.2f}

## 📈 integrated_event 表格成本預估

### 預估統計
- **日事件數**: {integrated['table_stats']['daily_events']:,}
- **平均事件大小**: {integrated['table_stats']['avg_event_size_kb']:.2f} KB
- **月度邏輯資料**: {integrated['table_stats']['monthly_logical_gb']:.2f} GB
- **月度實際儲存**: {integrated['table_stats']['monthly_physical_gb']:.2f} GB

### 預估月度成本
- **儲存成本**: ${integrated['storage_cost']['monthly_cost']:.2f}
- **寫入成本**: ${integrated['insert_cost']['monthly_cost']:.2f}
- **總成本**: ${integrated['total_monthly_cost']:.2f}

## 💰 雙寫入架構總成本影響

- **當前月度成本**: ${summary['current_monthly_cost']:.2f}
- **新增月度成本**: ${summary['additional_monthly_cost']:.2f}
- **總月度成本**: ${summary['total_new_monthly_cost']:.2f}
- **成本增加**: ${summary['cost_increase_usd']:.2f} (+{summary['cost_increase_percentage']:.1f}%)

## 📅 年度成本影響

- **年度新增成本**: ${summary['additional_monthly_cost'] * 12:.2f}
- **年度總成本**: ${summary['total_new_monthly_cost'] * 12:.2f}

---
*報告生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
*基於實際 BigQuery 表格統計資料*
        """

        return report.strip()


class GKECostCalculator:
    """GKE 成本計算器"""

    # GKE 定價 (美元，基於 2025 年 asia-east1 定價)
    PRICING = {
        'e2_highcpu_2_hour': 0.0336,      # $0.0336 per hour
        'n4_highcpu_2_hour': 0.0504,      # $0.0504 per hour (preemptible: 50% discount)
        'e2_standard_4_hour': 0.1344,     # $0.1344 per hour
        'e2_medium_hour': 0.0336,         # $0.0336 per hour
        'e2_standard_2_hour': 0.0672,     # $0.0672 per hour
        'disk_pd_balanced_gb_month': 0.10, # $0.10 per GB/month
        'disk_hyperdisk_balanced_gb_month': 0.144, # $0.144 per GB/month
    }

    def __init__(self, project_id: str = "tagtoo-tracking"):
        self.project_id = project_id

    def get_cluster_info(self, cluster_name: str, zone: str) -> Dict[str, Any]:
        """獲取叢集詳細資訊"""
        try:
            cmd = [
                'gcloud', 'container', 'clusters', 'describe',
                cluster_name, f'--zone={zone}', '--format=json',
                f'--project={self.project_id}'
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return json.loads(result.stdout)
        except subprocess.CalledProcessError as e:
            logger.error(f"獲取叢集資訊失敗: {e}")
            return {}

    def calculate_node_pool_cost(self, node_pool: Dict[str, Any]) -> Dict[str, float]:
        """計算節點池成本"""
        config = node_pool.get('config', {})
        autoscaling = node_pool.get('autoscaling', {})

        machine_type = config.get('machineType', '')
        min_nodes = autoscaling.get('minNodeCount', 0)
        max_nodes = autoscaling.get('maxNodeCount', 0)
        disk_size_gb = config.get('diskSizeGb', 20)
        disk_type = config.get('diskType', 'pd-balanced')
        is_preemptible = config.get('preemptible', False)

        # 計算機器成本
        hourly_rate = 0
        if 'e2-highcpu-2' in machine_type:
            hourly_rate = self.PRICING['e2_highcpu_2_hour']
        elif 'n4-highcpu-2' in machine_type:
            hourly_rate = self.PRICING['n4_highcpu_2_hour']
            if is_preemptible:
                hourly_rate *= 0.5  # 50% discount for preemptible
        elif 'e2-standard-4' in machine_type:
            hourly_rate = self.PRICING['e2_standard_4_hour']
        elif 'e2-medium' in machine_type:
            hourly_rate = self.PRICING['e2_medium_hour']
        elif 'e2-standard-2' in machine_type:
            hourly_rate = self.PRICING['e2_standard_2_hour']
        elif 'e2-highcpu-4' in machine_type:
            hourly_rate = self.PRICING['e2_highcpu_2_hour'] * 2  # 估算

        # 計算磁碟成本
        disk_rate_gb_month = self.PRICING.get(f'disk_{disk_type.replace("-", "_")}_gb_month', 0.10)

        # 估算平均節點數（min + 50% of range）
        avg_nodes = min_nodes + (max_nodes - min_nodes) * 0.5

        # 月度成本
        monthly_compute_cost = hourly_rate * 24 * 30 * avg_nodes
        monthly_disk_cost = disk_rate_gb_month * disk_size_gb * avg_nodes

        return {
            'node_pool_name': node_pool.get('name', ''),
            'machine_type': machine_type,
            'min_nodes': min_nodes,
            'max_nodes': max_nodes,
            'avg_nodes': avg_nodes,
            'hourly_rate': hourly_rate,
            'monthly_compute_cost': monthly_compute_cost,
            'monthly_disk_cost': monthly_disk_cost,
            'total_monthly_cost': monthly_compute_cost + monthly_disk_cost,
            'is_preemptible': is_preemptible
        }

    def analyze_bq_writer_cost(self) -> Dict[str, Any]:
        """分析 BQ Writer 節點池的當前成本"""
        logger.info("分析 BQ Writer 節點池成本...")

        cluster_info = self.get_cluster_info('event-prod', 'asia-east1-a')
        if not cluster_info:
            return {}

        # 找到 bq-writer-prod 節點池
        bq_writer_pool = None
        for pool in cluster_info.get('nodePools', []):
            if pool.get('name') == 'bq-writer-prod':
                bq_writer_pool = pool
                break

        if not bq_writer_pool:
            logger.error("找不到 bq-writer-prod 節點池")
            return {}

        return self.calculate_node_pool_cost(bq_writer_pool)

    def estimate_dual_write_resource_impact(self, current_cost: Dict[str, Any]) -> Dict[str, Any]:
        """估算雙寫入架構的資源影響"""
        if not current_cost:
            return {}

        # 基於效能測試結果，CPU 使用率增加約 15%，記憶體使用率增加約 0%
        # 但為了安全起見，我們預估需要增加 35% 的資源
        resource_increase = 0.35

        # 計算新的資源需求
        current_avg_nodes = current_cost['avg_nodes']
        new_avg_nodes = current_avg_nodes * (1 + resource_increase)

        # 重新計算成本
        hourly_rate = current_cost['hourly_rate']
        monthly_compute_cost = hourly_rate * 24 * 30 * new_avg_nodes
        monthly_disk_cost = current_cost['monthly_disk_cost'] * (1 + resource_increase)

        additional_cost = (monthly_compute_cost + monthly_disk_cost) - current_cost['total_monthly_cost']

        return {
            'current_avg_nodes': current_avg_nodes,
            'new_avg_nodes': new_avg_nodes,
            'resource_increase_pct': resource_increase * 100,
            'current_monthly_cost': current_cost['total_monthly_cost'],
            'new_monthly_cost': monthly_compute_cost + monthly_disk_cost,
            'additional_monthly_cost': additional_cost,
            'cost_increase_pct': (additional_cost / current_cost['total_monthly_cost']) * 100
        }


def main():
    """主函數"""
    print("🔍 開始完整成本分析...")
    print("=" * 60)

    # BigQuery 成本分析
    bq_calculator = BigQueryCostCalculator()
    bq_report = bq_calculator.generate_cost_report()

    # GKE 成本分析
    gke_calculator = GKECostCalculator()
    bq_writer_cost = gke_calculator.analyze_bq_writer_cost()
    gke_impact = gke_calculator.estimate_dual_write_resource_impact(bq_writer_cost)

    print(bq_report)

    if bq_writer_cost and gke_impact:
        print("\n" + "=" * 60)
        print("# GKE BQ Writer 節點池成本分析")
        print(f"""
## 📊 當前 BQ Writer 節點池配置

- **節點池名稱**: {bq_writer_cost['node_pool_name']}
- **機器類型**: {bq_writer_cost['machine_type']}
- **節點範圍**: {bq_writer_cost['min_nodes']}-{bq_writer_cost['max_nodes']} 節點
- **平均節點數**: {bq_writer_cost['avg_nodes']:.1f}
- **Preemptible**: {bq_writer_cost['is_preemptible']}

## 💰 當前月度成本

- **運算成本**: ${bq_writer_cost['monthly_compute_cost']:.2f}
- **磁碟成本**: ${bq_writer_cost['monthly_disk_cost']:.2f}
- **總成本**: ${bq_writer_cost['total_monthly_cost']:.2f}

## 📈 雙寫入架構資源影響

- **資源增加**: +{gke_impact['resource_increase_pct']:.0f}%
- **新平均節點數**: {gke_impact['new_avg_nodes']:.1f}
- **新月度成本**: ${gke_impact['new_monthly_cost']:.2f}
- **額外成本**: ${gke_impact['additional_monthly_cost']:.2f} (+{gke_impact['cost_increase_pct']:.1f}%)
        """)

    # 儲存完整報告
    full_report = bq_report
    if bq_writer_cost and gke_impact:
        full_report += f"""

# GKE BQ Writer 節點池成本分析

## 📊 當前 BQ Writer 節點池配置

- **節點池名稱**: {bq_writer_cost['node_pool_name']}
- **機器類型**: {bq_writer_cost['machine_type']}
- **節點範圍**: {bq_writer_cost['min_nodes']}-{bq_writer_cost['max_nodes']} 節點
- **平均節點數**: {bq_writer_cost['avg_nodes']:.1f}
- **Preemptible**: {bq_writer_cost['is_preemptible']}

## 💰 當前月度成本

- **運算成本**: ${bq_writer_cost['monthly_compute_cost']:.2f}
- **磁碟成本**: ${bq_writer_cost['monthly_disk_cost']:.2f}
- **總成本**: ${bq_writer_cost['total_monthly_cost']:.2f}

## 📈 雙寫入架構資源影響

- **資源增加**: +{gke_impact['resource_increase_pct']:.0f}%
- **新平均節點數**: {gke_impact['new_avg_nodes']:.1f}
- **新月度成本**: ${gke_impact['new_monthly_cost']:.2f}
- **額外成本**: ${gke_impact['additional_monthly_cost']:.2f} (+{gke_impact['cost_increase_pct']:.1f}%)

# 💰 總成本影響摘要

## BigQuery 成本
- **當前月度成本**: $36.91
- **新增月度成本**: $0.47
- **成本增加**: +1.3%

## GKE 成本
- **當前月度成本**: ${bq_writer_cost['total_monthly_cost']:.2f}
- **新增月度成本**: ${gke_impact['additional_monthly_cost']:.2f}
- **成本增加**: +{gke_impact['cost_increase_pct']:.1f}%

## 總計
- **總當前成本**: ${36.91 + bq_writer_cost['total_monthly_cost']:.2f}
- **總新增成本**: ${0.47 + gke_impact['additional_monthly_cost']:.2f}
- **總成本增加**: {((0.47 + gke_impact['additional_monthly_cost']) / (36.91 + bq_writer_cost['total_monthly_cost'])) * 100:.1f}%
"""

    with open('complete_cost_analysis_report.md', 'w', encoding='utf-8') as f:
        f.write(full_report)

    print("\n" + "=" * 60)
    print("✅ 完整成本分析完成！報告已儲存至 complete_cost_analysis_report.md")


if __name__ == "__main__":
    main()
