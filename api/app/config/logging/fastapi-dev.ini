[loggers]
keys=root,uvicorn

[handlers]
keys=console<PERSON><PERSON><PERSON>,detailedConsoleHand<PERSON>,fileHandler

[formatters]
keys=normalFormatter,detailedFormatter

[logger_root]
level=INFO
handlers=consoleHandler,fileHandler

[logger_uvicorn]
level=INFO
handlers=consoleHandler,fileHandler
qualname=uvicorn
propagate=0

[handler_consoleHandler]
class=StreamHandler
level=DEBUG
formatter=normalFormatter
args=(sys.stdout,)

[handler_detailedConsoleHandler]
class=StreamHandler
level=DEBUG
formatter=detailedFormatter
args=(sys.stdout,)

[handler_fileHandler]
class=handlers.TimedRotatingFileHandler
level=DEBUG
formatter=normalFormatter
kwargs={'filename': '${EVENT_API_LOGGING_FILE}', 'when': 'H', 'interval': 1, 'backupCount': 24}

[formatter_normalFormatter]
format=[%(asctime)s: %(levelname)s] %(message)s

[formatter_detailedFormatter]
format=[%(asctime)s: %(levelname)s/%(name)s] %(funcName)s() L%(lineno)-4d %(message)s   call_trace=%(pathname)s L%(lineno)-4d
