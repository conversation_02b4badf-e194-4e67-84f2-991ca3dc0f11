class RequestsServiceError(Exception):
    def __init__(self, url, status_code, detail):
        self.url = url
        self.status_code = status_code
        self.detail = detail
        super().__init__(url, status_code, detail)

    def __str__(self):
        return (
            f'url: {self.url}\n'
            f'status_code: {self.status_code}\n'
            f'detail: {self.detail}'
        )


class PubSubServiceError(Exception):
    def __init__(
        self,
        project,
        topic_name,
        published_message,
        attrs,
        origin_e=None,
    ):
        self.project = project
        self.topic_name = topic_name
        self.published_message = published_message
        self.attrs = attrs
        self.origin_e = origin_e

    def __str__(self):
        origin_e_message = (
            f'{self.origin_e.__class__.__name__}: {self.origin_e}'
            if self.origin_e
            else ''
        )
        return (
            f'PubSubServiceError: {origin_e_message}\n'
            f'project: {self.project}\n'
            f'topic_name: {self.topic_name}\n'
            f'message: {self.published_message}\n'
            f'attrs: {self.attrs}\n'
        )
