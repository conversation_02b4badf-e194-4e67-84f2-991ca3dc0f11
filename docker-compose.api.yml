version: '3.7'

services:
  api:
    &api
    container_name: api
    image: ad_track_api
    build:
      context: ./api
    command: /start-reload.sh
    entrypoint: ./scripts/entrypoint.sh
    volumes:
      - ./api:/app
      - ./gcloud/sa/event-api-vm.json:/credentials/event-api-vm.json
    environment:
      - GOOGLE_APPLICATION_CREDENTIALS=/credentials/event-api-vm.json
      - PUBSUB_EMULATOR_HOST=api-pubsub:8681
    ports:
      - "10002:8000"
    restart: always
    env_file: ./api/.env
    healthcheck:
      test:
        [
          "CMD",
          "curl",
          "-f",
          "http://localhost:8000/readiness"
        ]
      interval: 5s
      start_period: 5s
    networks:
      - api
    depends_on:
      - api_db
      - api_pubsub

  api_db:
    container_name: api-db
    image: redis:6-alpine
    networks:
      - api

  api_pubsub:
    container_name: api-pubsub
    image: messagebird/gcloud-pubsub-emulator
    environment:
      - PUBSUB_PROJECT1=tagtoo-tracking,facebook-capi:facebook-capi-test,tagtoo-event:tagtoo-event-test
    networks:
      - api

  api_worker:
    <<: *api
    container_name: api-worker
    command: ./scripts/start_worker.sh
    entrypoint: ./scripts/entrypoint.sh
    ports: []
    networks:
      - api
    depends_on:
      - api_db
      - api_pubsub

networks:
  api:
    name: config
    driver: bridge
