# https://registry.terraform.io/providers/cloudflare/cloudflare/latest/docs/resources/record
resource "cloudflare_dns_record" "event_api_ingress_dns_record" {
  zone_id = var.cloudflare.zones["tagtoo.co"]
  name    = "${var.event_api.ingress_subdomain}.tagtoo.co"
  content = google_compute_global_address.event_api.address
  type    = "A"
  ttl     = 1
  proxied = false
}

resource "cloudflare_dns_record" "event_config_api_ingress_dns_record" {
  zone_id = var.cloudflare.zones["tagtoo.co"]
  name    = "${var.event_config_api.ingress_subdomain}.tagtoo.co"
  content = google_compute_global_address.event_api.address
  type    = "A"
  ttl     = 1
  proxied = true
}

resource "cloudflare_dns_record" "event_api_ingress_dns_record_ipv6" {
  zone_id = var.cloudflare.zones["tagtoo.co"]
  name    = "${var.event_api.ingress_subdomain}.tagtoo.co"
  content = google_compute_global_address.event_api_ipv6.address
  type    = "AAAA"
  ttl     = 1
  proxied = false
}

resource "cloudflare_dns_record" "event_config_api_ingress_dns_record_ipv6" {
  zone_id = var.cloudflare.zones["tagtoo.co"]
  name    = "${var.event_config_api.ingress_subdomain}.tagtoo.co"
  content = google_compute_global_address.event_api_ipv6.address
  type    = "AAAA"
  ttl     = 1
  proxied = true
}

resource "cloudflare_ruleset" "event_config_api_admin_block" {
  zone_id     = var.cloudflare.zones["tagtoo.co"]
  name        = "default"
  description = "Custom WAF rules for tagtoo.co"
  kind        = "zone"
  phase       = "http_request_firewall_custom"

  rules = [
    # Event-doc: Only allow access from office IPs
    {
      action      = "block"
      expression  = "(http.host eq \"event-doc.tagtoo.co\" and not ip.src in {************ ************})"
      description = "Block the access to event-doc unless from an office location"
      enabled     = true
    },
    # Event-cnf: Only allow admin access from office IPs
    {
      action      = "block"
      expression  = "(http.host eq \"${var.event_config_api.ingress_subdomain}.tagtoo.co\" and http.request.uri.path contains \"/admin\" and not ip.src in {************ ************})"
      description = "Block admin access to event-cnf for users outside the office"
      enabled     = true
    }
  ]
}
