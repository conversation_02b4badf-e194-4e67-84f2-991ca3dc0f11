# LTA AVRO 檔案參數與 Meta Conversion API 對照分析

## 完整調查結果摘要

基於詳細的程式碼分析和 Meta Conversion API 最新官方文檔，以下是完整的參數要求和發送邏輯：

### 關鍵發現

1. **AVRO 檔案中的必要參數**: 系統主要依賴 `fbp` + `segment_id` 就能正常發送
2. **其他參數為可選**: emails, phones, genders, births 等都是可選的
3. **Meta API 要求**: 只要有至少一個有效的用戶識別參數即可
4. **批次處理**: 系統會根據 `fb_info` 中的每個 fbp/fbc/ip 組合建立獨立的事件

## AVRO 檔案參數結構分析

### 程式碼中定義的 AVRO 參數

根據 `s2s/s2s/LTA/loader.py:194-231` 的處理邏輯：

```python
def _compose_pixel_datum_from_avro(self, user_data: List[dict], **kwargs):
    for user_datum in user_data:
        emails = user_datum.get('emails', [])        # 可選
        phones = user_datum.get('phones', [])        # 可選  
        genders = user_datum.get('genders', [])      # 可選
        births = user_datum.get('births', [])        # 可選
        segment_id = user_datum.get('segment_id', []) # 可選，但通常包含
        
        # 最重要的參數
        for fb_info in user_datum.get('fb_info', []):
            fbp, fbc, ip = fb_info['fbp_fbc_ip']  # 必要
```

### AVRO 檔案必要結構

| AVRO 參數名稱 | 資料類型 | 必要性 | 說明 |
|---------------|----------|--------|------|
| `fb_info` | Array[Object] | **必要** | 包含 Facebook 識別資訊的陣列 |
| `fb_info[].fbp_fbc_ip` | Array[3] | **必要** | [fbp, fbc, client_ip] 的陣列 |
| `segment_id` | Array[String] | 強烈建議 | 用於自訂受眾分群 |
| `emails` | Array[String] | 可選 | 使用者 email 清單 |
| `phones` | Array[String] | 可選 | 使用者電話清單 |
| `genders` | Array[String] | 可選 | 使用者性別清單 |
| `births` | Array[String] | 可選 | 使用者生日清單 |

## Meta Conversion API 參數對照

### 發送到 Meta 的最終格式

程式碼會建立以下結構發送到 Meta API：

```python
PixelDatum = {
    'event_name': 'LTA',                    # 固定值
    'event_time': int(time.time()),         # 當前時間戳
    'action_source': 'other',               # 固定值
    'user_data': {
        'em': emails,                       # AVRO: emails (已 hash)
        'ph': phones,                       # AVRO: phones (已 hash) 
        'ge': genders,                      # AVRO: genders (已 hash)
        'db': births,                       # AVRO: births (已 hash)
        'zp': [],                          # 未使用
        'fbp': fbp,                        # AVRO: fb_info[].fbp_fbc_ip[0]
        'fbc': fbc,                        # AVRO: fb_info[].fbp_fbc_ip[1] 
        'client_ip_address': ip            # AVRO: fb_info[].fbp_fbc_ip[2]
    },
    'custom_data': {
        'segment_id': '_segment_value_'    # AVRO: segment_id (組合後)
    }
}
```

### Meta API 參數要求與驗證

根據 Meta 官方文檔（2025年最新）：

#### 必要參數 (Server Event)
- `event_name`: 事件名稱
- `event_time`: 事件時間  
- `action_source`: 動作來源
- **至少一個用戶識別參數**

#### 用戶識別參數 (user_data) - 至少需要一個
| Meta 參數 | 對應 AVRO | 必要性 | Hash 要求 | 說明 |
|-----------|-----------|--------|-----------|------|
| `fbp` | `fb_info[].fbp_fbc_ip[0]` | **強烈建議** | ❌ 不需要 | Facebook Browser ID |
| `fbc` | `fb_info[].fbp_fbc_ip[1]` | **強烈建議** | ❌ 不需要 | Facebook Click ID |
| `client_ip_address` | `fb_info[].fbp_fbc_ip[2]` | **強烈建議** | ❌ 不需要 | 用戶 IP 位址 |
| `em` | `emails[]` | 可選 | ✅ 需要 SHA256 | Email 地址 |
| `ph` | `phones[]` | 可選 | ✅ 需要 SHA256 | 電話號碼 |
| `ge` | `genders[]` | 可選 | ✅ 需要 SHA256 | 性別 (f/m) |
| `db` | `births[]` | 可選 | ✅ 需要 SHA256 | 生日 (YYYYMMDD) |
| `external_id` | - | 可選 | ✅ 建議 | 外部 ID |

## 最小發送要求

### 情境 1: 僅 fbp + segment_id

**AVRO 最小結構**:
```json
{
  "fb_info": [
    {
      "fbp_fbc_ip": ["fb.1.1596403881668.1116446470", "", "***********"]
    }
  ],
  "segment_id": ["targeting_segment_1"]
}
```

**結果**: ✅ **可以成功發送**
- `fbp` 提供了足夠的用戶識別
- `segment_id` 提供自訂受眾資訊
- 其他參數全部為空陣列

### 情境 2: 僅 email + segment_id

**AVRO 結構**:
```json
{
  "emails": ["<EMAIL>"],
  "segment_id": ["targeting_segment_1"],
  "fb_info": [
    {
      "fbp_fbc_ip": ["", "", "***********"]
    }
  ]
}
```

**結果**: ✅ **可以成功發送**
- 程式碼會自動 hash email
- IP 地址提供額外識別資訊

### 情境 3: 完全沒有識別參數

**AVRO 結構**:
```json
{
  "segment_id": ["targeting_segment_1"],
  "fb_info": [
    {
      "fbp_fbc_ip": ["", "", ""]
    }
  ]
}
```

**結果**: ⚠️ **技術上可發送，但效果很差**
- Meta 無法有效匹配用戶
- 建議至少提供一個識別參數

## 批次處理邏輯

### 重要發現：fb_info 陣列展開

程式碼中的關鍵邏輯 (`loader.py:220-231`)：

```python
for fb_info in user_datum.get('fb_info', []):
    fbp, fbc, ip = fb_info['fbp_fbc_ip']
    cur_pixel_datum = deepcopy(default_pixel_datum)
    cur_pixel_datum['user_data']['fbp'] = fbp
    cur_pixel_datum['user_data']['fbc'] = fbc
    cur_pixel_datum['user_data']['client_ip_address'] = ip
    res.append(cur_pixel_datum)
```

**這意味著**：
- 一個 AVRO 記錄中的每個 `fb_info` 項目都會產生一個獨立的 Meta API 事件
- 如果 `fb_info` 有 3 個項目，就會發送 3 個事件
- 每個事件都共享相同的 emails, phones, segment_id 等資訊

### 範例：多個 fb_info 的處理

**AVRO 輸入**:
```json
{
  "emails": ["<EMAIL>"],
  "segment_id": ["segment_1"],
  "fb_info": [
    {
      "fbp_fbc_ip": ["fb.1.111.111", "fbc.1.222", "***********"]
    },
    {
      "fbp_fbc_ip": ["fb.1.333.333", "fbc.1.444", "***********"]
    }
  ]
}
```

**結果**: 會發送 **2 個獨立事件** 到 Meta API

## Data Hashing 處理

### 自動 Hash 的參數

程式碼會自動對以下參數進行 SHA256 hash (`loader.py:137-139`)：

```python
def _encode_sha_256(self, text: str) -> str:
    sha_256 = sha256(text.encode('utf8'))
    return sha_256.hexdigest()
```

- ✅ `emails` - 自動 hash
- ✅ `phones` - 自動 hash  
- ✅ `genders` - 自動 hash
- ✅ `births` - 自動 hash
- ✅ `zip_codes` - 自動 hash (但 AVRO 中未使用)

### 不 Hash 的參數

- ❌ `fbp` - 直接發送
- ❌ `fbc` - 直接發送
- ❌ `client_ip_address` - 直接發送
- ❌ `segment_id` - 直接發送 (組合後)

## 效能考量和限制

### 批次大小限制

```python
# loader.py:43-46
message_limit = 8000000      # 8MB 限制
chunk_size = 1000           # 每塊 1000 個事件
default_max_retry_times = 10 # 最大重試 10 次
```

### 處理流程

1. **AVRO 讀取**: 從 GCS 批次讀取 AVRO 檔案
2. **數據轉換**: 每個 fb_info 產生一個事件
3. **分塊處理**: 每 1000 個事件為一塊
4. **大小檢查**: 確保每個訊息 < 8MB
5. **發送重試**: 失敗時最多重試 10 次

## 建議的最佳實踐

### 1. 最小有效配置

```json
{
  "fb_info": [
    {
      "fbp_fbc_ip": ["fb.1.timestamp.random", "", "user_ip"]
    }
  ],
  "segment_id": ["your_segment_id"]
}
```

### 2. 推薦配置

```json
{
  "emails": ["hashed_email_or_raw_email"],
  "fb_info": [
    {
      "fbp_fbc_ip": ["fbp_value", "fbc_value", "user_ip"]
    }
  ],
  "segment_id": ["your_segment_id"]
}
```

### 3. 完整配置

```json
{
  "emails": ["<EMAIL>"],
  "phones": ["1234567890"],
  "genders": ["f"],
  "births": ["19900101"],
  "fb_info": [
    {
      "fbp_fbc_ip": ["fbp_value", "fbc_value", "user_ip"]
    }
  ],
  "segment_id": ["targeting_segment_1", "interest_segment_2"]
}
```

## 常見問題解答

### Q: 可以只發送 fbp + segment_id 嗎？
**A: 是的**，這是最常見和有效的配置。

### Q: 需要所有用戶識別參數都存在嗎？
**A: 不需要**，Meta API 只要求至少一個有效識別參數。

### Q: 如果 AVRO 中沒有 emails 會怎樣？
**A: 沒問題**，程式碼會設定為空陣列 `[]`，Meta API 接受空值。

### Q: segment_id 必要嗎？
**A: 技術上不必要**，但對於 LTA 受眾定向非常重要，強烈建議包含。

### Q: fb_info 可以是空陣列嗎？
**A: 技術上可以**，但會導致沒有有效的用戶識別，建議至少包含一個項目。

### Q: 系統如何處理無效的 fbp 格式？
**A: 程式碼會直接發送**，Meta API 會在其端進行驗證和過濾。

## 結論

LTA 系統設計得相當靈活，**最小要求僅為 `fbp` + `segment_id`**，但提供更多參數可以提高匹配準確度。系統會自動處理數據 hash、分塊、重試等複雜邏輯，使得 AVRO 檔案的準備相對簡單。

關鍵是確保 `fb_info` 陣列中至少有一個有效的識別資訊，其他所有參數都是可選的增強功能。