# Error Reporting 機制

本文檔詳細說明了 Tagtoo 追蹤系統中的錯誤報告機制，包括架構、實現和最佳實踐。

## 概述

Tagtoo 追蹤系統使用 Google Cloud Error Reporting 服務來收集、聚合和分析應用程序錯誤。這種機制允許開發團隊快速識別和解決問題，提高系統的穩定性和可靠性。

## 架構

錯誤報告機制基於以下組件：

1. **ErrorReportingService 基類**：定義了錯誤報告的基本接口和功能
2. **服務特定的 ErrorReportingService 子類**：為每個服務定制錯誤報告行為
3. **Google Cloud Error Reporting API**：用於將錯誤報告發送到 GCP

### 類別層次結構

```text
ErrorReportingService (抽象基類)
├── LTAErrorReportingService
├── FacebookCAPIErrorReportingService
├── EventBigQueryWriterService
└── UserUnifyErrorReportingService
```

## 實現詳情

### ErrorReportingService 基類

`ErrorReportingService` 是一個抽象基類，定義了錯誤報告的基本接口：

```python
class ErrorReportingService(metaclass=abc.ABCMeta):
    @property
    @abc.abstractmethod
    def project(self):
        pass

    @property
    @abc.abstractmethod
    def credentials(self):
        pass

    @property
    @abc.abstractmethod
    def service(self):
        pass

    def __init__(self, version):
        self.client = Client(
            project=self.project,
            credentials=self.credentials,
            service=self.service,
            version=version,
        )

    def report(self, msg: str) -> None:
        try:
            self.client.report(msg)
        except ResourceExhausted as err:
            logger.error(f'Error report ResourceExhausted error with {msg=}')
            logger.error(err)
        except Exception as err:
            logger.error(f'Error report {err.__class__.__name__} with {msg=}')
            self.report_exception()

    def report_exception(self, *args, **kwargs) -> None:
        prv_traceback = traceback.format_exc()
        try:
            self.client.report_exception(*args, **kwargs)
        except ResourceExhausted as err:
            logger.error(
                f'Error report ResourceExhausted error with traceback={prv_traceback}'
            )
            logger.error(err)
        except Exception:
            try:
                self.client.report_exception()
            except Exception as err:
                logging.error(
                    f'Error report {err.__class__.__name__} with traceback={traceback.format_exc()}'
                )
```

### 服務特定的實現

#### LTAErrorReportingService

`LTAErrorReportingService` 用於 LTA 數據處理系統的錯誤報告：

```python
class LTAErrorReportingService(ErrorReportingService):
    project = settings.GCP_PROJECT_ID
    credentials = None  # Use VM service account
    service = 'event-s2s/LTA'

    def report_error(self, error: Exception, **kwargs) -> None:
        expected_error = {
            'LtaExceptions': (
                f'Message version: {kwargs.get("version")}, '
                f'File_name: {kwargs.get("file_name")}, Error: {error}'
            )
        }
        if message := expected_error.get(error.__class__.__name__):
            self.report(message)
        else:
            self.report_exception()
```

#### FacebookCAPIErrorReportingService

`FacebookCAPIErrorReportingService` 用於 Facebook Conversion API 相關錯誤的報告：

```python
class FacebookCAPIErrorReportingService(service.ErrorReportingService):
    project = settings.GCP_PROJECT_ID
    credentials = None  # Use VM service account
    service = 'event-s2s/facebook-capi'

    def report_error(self, error: Exception, **kwargs) -> None:
        expected_error = {
            'EcommercePixelDoesNotExist': f'{error} result={kwargs}',
            'FacebookRequestError': f'{error}, Payload: {kwargs.get("data", [])}',
            'FacebookApiConnectTimeout': f'{error}, Payload: {kwargs.get("data", [])}',
            'FacebookPixelDatumInvaild': f'{error}',
        }
        if message := expected_error.get(error.__class__.__name__):
            self.report(message)
        else:
            self.report_exception()
```

## 使用方法

### 初始化錯誤報告服務

```python
# 在服務初始化時創建錯誤報告服務實例
error_report_service = LTAErrorReportingService(version='v1')
```

### 報告錯誤

```python
try:
    # 嘗試執行可能失敗的操作
    result = some_operation()
except Exception as error:
    # 報告錯誤，可以包含上下文信息
    error_report_service.report_error(
        error,
        version='v1',
        file_name='example.avro'
    )
    return False
```

## 查看錯誤報告

錯誤報告可以在 Google Cloud Console 中查看：

1. 前往 [Google Cloud Console](https://console.cloud.google.com/)
2. 選擇專案 "tagtoo-tracking"
3. 在左側導航欄中，找到 "Error Reporting" (在 "Operations" 類別下)
4. 在 Error Reporting 頁面，可以按服務名稱進行過濾

Tagtoo 追蹤系統的統一錯誤報告頁面 URL 為：

```text
https://console.cloud.google.com/errors;filter=%5B%5D?project=tagtoo-tracking
```

此頁面顯示了所有服務的錯誤報告，包括：

- event-s2s/LTA
- event-s2s/facebook-capi
- event-bq-writer
- event-user-unify
- 其他服務

您也可以按特定服務和版本進行過濾，例如：

```text
https://console.cloud.google.com/errors?project=tagtoo-tracking&serviceId=event-s2s%2FLTA&version=v1
```

## 最佳實踐

### 錯誤分類

將錯誤分為以下幾類：

1. **預期錯誤**：可以預見的錯誤，如格式錯誤、連接超時等
2. **非預期錯誤**：未預見的異常，需要完整的堆疊跟踪

### 上下文信息

在報告錯誤時，提供足夠的上下文信息：

- 版本號
- 文件名或資源標識符
- 操作類型
- 相關數據（注意不要包含敏感信息）

### 錯誤處理策略

1. **重試機制**：對於暫時性錯誤，實施適當的重試策略
2. **降級服務**：在關鍵組件失敗時，提供降級服務
3. **錯誤隔離**：確保一個組件的錯誤不會影響整個系統

## 與 Cloud Logging 的集成

### Error Reporting 與 Cloud Logging

Error Reporting 與 Cloud Logging 緊密集成。當使用 `report_exception()` 方法時，錯誤堆疊跟踪會自動發送到 Cloud Logging。

可以在 Cloud Logging 中設置警報，以便在出現特定錯誤模式時收到通知。

### CRITICAL 日誌級別

除了使用 Error Reporting 服務外，Tagtoo 追蹤系統還實現了 CRITICAL 日誌級別，用於記錄嚴重的系統問題。這種方法特別適用於需要立即關注的操作問題，例如：

- 數據處理完全失敗
- 沒有成功發布任何消息
- 無法連接到關鍵服務
- 資源耗盡

#### 實現方式

在 `LogAdapter` 類中，我們添加了對 CRITICAL 級別的支持：

```python
# 將 Python logging 級別映射到 Google Cloud Logging 級別
if level_no >= logging.CRITICAL:
    severity = 'CRITICAL'
elif level_no >= logging.ERROR:
    severity = 'ERROR'
# ... 其他級別
```

使用示例：

```python
# 檢查是否有成功發布消息
if pixel_count == 0:
    self._logger.critical(
        f'CRITICAL: No messages were published from {file_name}. This is a serious issue that requires immediate attention.'
    )
    self.error_report_service.report_error(
        Exception(f'No messages published from {file_name}'),
        version=version,
        file_name=file_name
    )
```

#### 設置 Cloud Logging 警報

可以在 Cloud Logging 中設置警報，以便在出現 CRITICAL 級別的日誌時收到通知：

1. 前往 Google Cloud Console 的 Logging > Logs Explorer
2. 創建查詢：`severity=CRITICAL`
3. 點擊 "Create Alert"
4. 配置通知渠道（電子郵件、Slack、PagerDuty 等）
5. 設置適當的通知頻率和條件

這種方法可以確保運維團隊在出現嚴重問題時立即收到通知，而不必等待人工檢查錯誤報告。

### 整合策略比較

目前，Tagtoo 追蹤系統採用了兩種並行的方法來處理嚴重問題：

1. **Error Reporting**：通過 `error_report_service.report_error()` 將錯誤報告到 GCP Error Reporting
2. **CRITICAL 日誌**：通過 `logger.critical()` 將嚴重問題記錄到 Cloud Logging

這兩種方法各有優缺點，以下是對它們的比較分析：

#### 方法一：保持分離（當前方法）

**優點**：

- 職責分離清晰：Error Reporting 專注於錯誤聚合和分析，Cloud Logging 專注於操作監控
- 靈活性高：可以根據不同需求選擇不同的報告方式
- 兼容現有代碼：不需要修改現有的 Error Reporting 服務
- 更精細的控制：可以決定哪些問題需要記錄為 CRITICAL，哪些需要報告到 Error Reporting

**缺點**：

- 重複報告：同一問題可能同時出現在 Error Reporting 和 Cloud Logging 中
- 管理複雜：需要在兩個不同的系統中設置和管理警報
- 可能導致不一致：如果只在一個系統中報告問題，可能會漏掉某些情況

#### 方法二：整合到 Error Reporting 中

**優點**：

- 統一管理：所有錯誤和嚴重問題都在一個系統中管理
- 減少重複：避免同一問題在多個系統中重複報告
- 簡化代碼：開發人員只需要使用一個 API 來報告所有問題
- 一致性更高：確保所有問題都以相同的方式處理和報告

**缺點**：

- 需要修改現有代碼：需要擴展 `ErrorReportingService` 類以支持日誌級別
- 可能失去某些 Cloud Logging 的功能：如果完全依賴 Error Reporting，可能會失去一些 Cloud Logging 特有的功能
- 實現複雜度增加：需要在 Error Reporting 中實現日誌級別的概念

#### 建議方案

基於 Tagtoo 追蹤系統的當前架構和需求，我們建議採用**混合方法**：

1. **保持現有的分離架構**：繼續使用 Error Reporting 和 Cloud Logging 作為兩個獨立但互補的系統
2. **標準化嚴重問題的報告**：制定明確的指導原則，說明哪些問題應該報告到 Error Reporting，哪些應該記錄為 CRITICAL 日誌
3. **實現自動化關聯**：在報告錯誤和記錄日誌時添加相同的標識符（如 `process_id`），以便在兩個系統中關聯相同的問題
4. **統一警報管理**：使用統一的警報管理系統（如 Cloud Monitoring）來管理來自 Error Reporting 和 Cloud Logging 的警報

這種方法可以保持兩個系統的優點，同時減少它們的缺點，為 Tagtoo 追蹤系統提供更全面的錯誤處理和監控解決方案。

## 結論

錯誤報告機制是 Tagtoo 追蹤系統可靠性的關鍵組成部分。通過正確實施和使用這一機制，開發團隊可以快速識別和解決問題，提高系統的穩定性和可靠性。

結合 Error Reporting 和 CRITICAL 日誌級別，Tagtoo 追蹤系統可以實現全面的錯誤處理和監控，確保在出現問題時能夠及時發現和解決，最大限度地減少對用戶的影響。
