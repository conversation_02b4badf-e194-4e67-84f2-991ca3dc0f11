import argparse
import logging
import sys

from google.cloud.logging.handlers import StructuredLogHandler

from s2s.LTA.loader import DataLoader
from s2s import settings
from s2s.logging_utils import setup_cloud_logging, CustomStructuredLogHandler


def parse_arguments():
    parser = argparse.ArgumentParser()
    parser.add_argument('--filename', help='The target filename from GCS')
    parser.add_argument('--ecid', help='The EC-ID of the client.')
    parser.add_argument('--version', choices=['v1'], help='Version.')
    args = parser.parse_args()
    return args


def setup_logging():
    # 在生產環境中設定 Google Cloud Logging
    if settings.MODE == 'prod':
        # 獲取 root logger 並移除所有處理器
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # 獲取 s2s logger 並移除所有處理器
        logger = logging.getLogger('s2s')
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

        # 使用自定義的 Cloud Logging 設置
        logger = setup_cloud_logging('s2s', project_id=settings.GCP_PROJECT_ID, use_structured=True)
        logger.setLevel(logging.getLevelName(settings.LOGGING_LEVEL))

        # 確保沒有其他處理器
        for handler in logger.handlers[:]:
            if not isinstance(handler, (StructuredLogHandler, CustomStructuredLogHandler)):
                logger.removeHandler(handler)

        return logger
    else:
        # 設定基本的 Python logging
        logging.basicConfig(
            format='[%(asctime)s %(levelname)s] %(message)s',
            level=logging.getLevelName(settings.LOGGING_LEVEL),
            stream=sys.stdout  # 使用 stdout 而不是 stderr
        )
        return logging.getLogger('s2s')


def main():
    # 設定日誌
    logger = setup_logging()

    # 解析參數
    args = parse_arguments()

    # 創建 DataLoader 並處理數據
    loader = DataLoader()
    loader.process(
        file_name=args.filename,
        ec_id=args.ecid,
        version=args.version,
    )


if __name__ == '__main__':
    main()
