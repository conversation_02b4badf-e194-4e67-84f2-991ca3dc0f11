import pickle
from operator import itemgetter

import requests
from redis import Redis

from .. import settings
from .exceptions import (
    EcommercePixelDoesNotExist,
    FacebookRequestError,
    FacebookApiConnectTimeout,
)

FACEBOOK_CAPI_URI_SCHEME = (
    "https://graph.facebook.com/{version}/{pixel_id}/events?access_token={access_token}"
)


def post_facebook_capi(
    data: list,
    pixel_id: str,
    access_token: str,
    timeout=5,
    version=settings.FACEBOOK_API_VERSION,
):
    url = FACEBOOK_CAPI_URI_SCHEME.format(
        version=version,
        pixel_id=pixel_id,
        access_token=access_token,
    )
    payload = {'data': data}
    try:
        response = requests.post(url=url, json=payload, timeout=timeout)
    except requests.exceptions.Timeout as error:
        raise FacebookApiConnectTimeout(f'Facebook api request timed out. {error}')
    if not (200 <= response.status_code < 300):
        raise FacebookRequestError(
            http_status=response.status_code, body=response.content
        )
    return response


def update_ec_pixels(ttl=settings.CONFIG_FACEBOOK_CAPI_TTL):
    """Update ec pixels mapping in Redis database from Event Config API. Format: ec_id: [(pixel_id, pixel_token)]"""
    response = requests.get(
        settings.S2S_PIXELS_CONFIG_API,
        headers={
            'Authorization': f'Token {settings.CONFIG_API_AUTH_TOKEN}',
        },
    )
    data = response.json()
    mapped = dict()
    for item in data:
        ec_id, pixel_id, pixel_token = itemgetter('ec_id', 'pixel_id', 'pixel_token')(
            item
        )
        mapped.setdefault(ec_id, list())
        mapped[ec_id].append((pixel_id, pixel_token))

    db = Redis(host=settings.REDIS_HOST, port=6379, db=1)

    for key, value in mapped.items():
        db.set(key, pickle.dumps(value))
        db.expire(key, ttl)


def get_ec_pixels(ec: int):
    db = Redis(host=settings.REDIS_HOST, port=6379, db=1)
    pixel = db.get(ec)
    if pixel is None:
        raise EcommercePixelDoesNotExist(f"No pixels matching ec={ec} found.")
    return pickle.loads(pixel)
