The user unify database schema is a logical graph model, which use **sparse** columns to stores "Edges" 
across rows. 

## Conceptional Overlays

There are three overlays in the graph:

**1. Raw Data Overlay**

A raw data is called an Entity in user unify, which refers to a combination of all possible user 
attributes, such as email, mobile, etc.

**2. Ground Truth Overlay**

If two permanents has any identical user attributes, they are in the same group. The ground truth overlay basically means
the edges links them or helps them to be linked.

**3. Group Overlay**

Every group is a sub-graph. We must perform a **traversal** across the sub-graph to find out what's in it. The 
nodes in the same graph will be pointed to a root node which is been inserted earliest.

## Entity Table

Stores the raw user data and provide transaction control to prevent operation rework and race condition.

**rowkey**

Combines all possible user attributes in a specific order with delimiter `#`.

```
<permanent>#<email>#<mobile>#<username>#<gid>#<fbp>
```

**columns**

|Column Family|Column|Type before bytes|Description|
|---|---|---|---|
|meta|timestamp|Integer|The insert time, help tracing the operation states.|
|meta|tx1|String {Y, N}|The state of transaction 1.|
|meta|tx2|String {Y, N}|The state of transaction 2.|
|meta|tx3|String {Y, N}|The state of transaction 3.|
|meta|tx4|String {Y, N}|The state of transaction 4.|

## Permanent Table

What we really cares is "What permanents are refers to the same user", therefore, Permanent is the main "Node"
in the graph.

A permanent node stores:

1. Valuable attributes such as emails, mobile 
2. Links between itself and other permanents
3. Pointer to the group's root

**rowkey**

A Tagtoo permanent ID.

**columns**

|Column Family|Column|Type before bytes|Description|
|---|---|---|---|
|meta|timestamp|Integer|The insert time, help determine the root in the graph.|
|meta|group|<nobr>`<Permanent.rowkey>`</nobr>|The pointer to the root in the graph.|
|emails|*sparse/dynamic*|<nobr>`<Email.rowkey>`</nobr>|The emails ever refers to this permanent.| 
|mobiles|*sparse/dynamic*|<nobr>`<Mobile.rowkey>`</nobr>|The mobiles ever refers to this permanent.| 
|permanents|*sparse/dynamic*|<nobr>`<Permanent.rowkey>`</nobr>|The permanents which shares some identical attributes.|

## Email Table

Provide fast lookup for an email.

**rowkey**

A hashed email.

**columns**

|Column Family|Column|Type before bytes|Description|
|---|---|---|---|
|permanents|*sparse/dynamic*|<nobr>`<Permanent.rowkey>`</nobr>|The permanents refers to this user attribute.|

## Mobile Table

Provide fast lookup for a mobile.

**rowkey**

A hashed mobile.

**columns**

|Column Family|Column|Type before bytes|Description|
|---|---|---|---|
|permanents|*sparse/dynamic*|<nobr>`<Permanent.rowkey>`</nobr>|The permanents refers to this user attribute.|

## Username Table

Provide fast lookup for an username.

**rowkey**

A hashed username combined with EC ID with `.` delimiter.

**columns**

|Column Family|Column|Type before bytes|Description|
|---|---|---|---|
|permanents|*sparse/dynamic*|<nobr>`<Permanent.rowkey>`</nobr>|The permanents refers to this user attribute.|

## Gid Table

!!!info
    Column families of a gid has 30 days TTL since it's a short-live value, in order to decrease the storage size.

Provide fast lookup for a gid.

**rowkey**

Short-live user cookie of Google Analytics.

**columns**

|Column Family|Column|Type before bytes|Description|
|---|---|---|---|
|permanents|*sparse/dynamic*|<nobr>`<Permanent.rowkey>`</nobr>|The permanents refers to this user attribute.|

## Fbp Table

!!!info
    Column families of a fbp has 30 days TTL since it's a short-live value, in order to decrease the storage size.

Provide fast lookup for a fbp.

**rowkey**

Short-live user cookie of Facebook.

**columns**

|Column Family|Column|Type before bytes|Description|
|---|---|---|---|
|permanents|*sparse/dynamic*|<nobr>`<Permanent.rowkey>`</nobr>|The permanents refers to this user attribute.|