import binascii
import os
import shortuuid
from django.db import models
from django.utils import timezone
from django.utils.translation import ugettext_lazy as _
from model_utils.models import TimeStampedModel

from .managers import AccessTokenManager


class ChannelType(models.IntegerChoices):
    FRONT = 0, 'Front Channel'
    BACK = 1, 'Back Channel'


class Scope(TimeStampedModel):
    name = models.CharField(max_length=120, verbose_name=_('Name'))

    def __str__(self):
        return self.name


class Client(TimeStampedModel):
    client_id = models.CharField(
        max_length=22, unique=True, verbose_name=_('Client ID')
    )
    name = models.CharField(max_length=120, verbose_name=_('Name'))
    channel_type = models.PositiveSmallIntegerField(
        choices=ChannelType.choices, verbose_name=_('Channel Type')
    )
    is_internal = models.BooleanField(default=False, verbose_name=_('Internal'))

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.client_id:
            self.client_id = shortuuid.uuid()
        super().save(*args, **kwargs)


class AccessToken(TimeStampedModel):
    ec_id = models.PositiveIntegerField(db_index=True)
    client = models.ForeignKey(
        'event_api_app.Client',
        related_name='access_tokens',
        on_delete=models.CASCADE,
        verbose_name=_('Client'),
    )
    token = models.CharField(max_length=60, unique=True, verbose_name=_('Token'))
    scopes = models.ManyToManyField(
        'event_api_app.Scope', related_name='access_tokens', verbose_name=_('Scope')
    )
    revoked = models.DateTimeField(null=True, blank=True, verbose_name=_('Revoked'))

    objects = AccessTokenManager()

    def __str__(self):
        return self.token

    def save(self, *args, **kwargs):
        if not self.token:
            self.token = self.generate_token()
        super().save(*args, **kwargs)

    @staticmethod
    def generate_token():
        return binascii.hexlify(
            os.urandom(30),
        ).decode()

    @property
    def is_revoked(self):
        return bool(self.revoked)

    def revoke(self):
        self.revoked = timezone.now()
        self.save(update_fields=['revoked'])

    def delete(self, soft=True):
        if soft:
            if not self.is_revoked:
                self.revoke()
        else:
            super().delete()
