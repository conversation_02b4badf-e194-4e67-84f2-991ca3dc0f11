#!/bin/bash
#
# Provisioning BigQuery test Table before running the event bigQuery writer services

error() { echo -e "\e[91m$1\e[m"; exit 1; }
success() { echo -e "\e[92m$1\e[m"; }
abs_path() { echo "$(cd "$(dirname "$1")" && pwd -P)/$(basename "$1")"; }

gcloud_helper_container_name=gcloud-helper-$(openssl rand -hex 6)
project=tagtoo-tracking
test_dataset=event_test
test_table=test_$(openssl rand -hex 6)
credential_path=$(abs_path ./gcloud/sa/event-bq-writer-vm.json)
schema_path=$(abs_path ./deploy/bigquery/schemas/tagtoo-event.json)
credential_mount_path=/credentials/event-bq-writer-vm.json
schema_mount_path=/schemas/event-bq-writer-vm.json

trap "exit 1" HUP INT PIPE QUIT TERM
trap "remove_gcloud_helper" EXIT

function setup_gcloud_helper(){
# Create temp container
docker run -d --name "$gcloud_helper_container_name" \
  -v "$credential_path":"$credential_mount_path" \
  -v "$schema_path":"$schema_mount_path" \
  google/cloud-sdk:alpine tail -f /dev/null >/dev/null

# Setup credentials
docker exec -i "$gcloud_helper_container_name" sh << END
gcloud config set project $project
gcloud auth activate-service-account --key-file=$credential_mount_path
END
}

function remove_gcloud_helper(){
docker container rm -f "$gcloud_helper_container_name" >/dev/null
}

function create_test_table(){
docker exec -i "$gcloud_helper_container_name" sh << END
bq mk --table --expiration 86400 $project:$test_dataset.$test_table $schema_mount_path
END
}

echo -e "Setup gcloud helper ... "

if setup_gcloud_helper; then success '[OK]'; else error '[ERROR]'; fi

echo -e "Creating test table from BigQuery ... "

if create_test_table; then success '[OK]'; else error '[ERROR]'; fi

# Export environment variable to config the container
export BIGQUERY_DATASET_ID="$test_dataset"
export BIGQUERY_TAGTOO_EVENT_TABLE_ID="$test_table"

# Pass shell argument to docker-compose command
docker-compose -f docker-compose.bq_writer.yml up "$@"