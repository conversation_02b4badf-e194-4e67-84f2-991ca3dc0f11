- 存放路徑為 `ad-track/user_unify/user_unify/bigquery/sql/`
- 當前的 Rollback 機制是使用 [BigQuery snapshot](https://cloud.google.com/bigquery/docs/table-snapshots-intro)，關於實作上的前因後果，可以參考 [Issue 250](https://github.com/Tagtoo/ad-track/issues/250)
- [Flowchart](./../workflow.md#determine-groups-update-grouping-reslut-detail-flow-chart) 中的任一一個 step 出錯，都會觸發 Rollback 機制，直接將 [Flowchart](./../workflow.md#determine-groups-update-grouping-reslut-detail-flow-chart) 中的 `Updated Group Result` 回復成前一天的結果

### create_group_table_snapshot.sql
```sql
-- Create daily snapshot for rolling back
CREATE SNAPSHOT TABLE `$snapshot_table`
CLONE `$groups_table`
  OPTIONS (
    -- 設定過 table 過期時間
    expiration_timestamp = TIMESTAMP_ADD(
      CURRENT_TIMESTAMP(), INTERVAL $expiration_interval DAY
    )
  );
```

- 使用 [`CREATE SNAPSHOT`](https://cloud.google.com/bigquery/docs/table-snapshots-create#create_a_table_snapshot) 語法建立 Snapshot，目前是在執行 [User Unify SQL](UU_SQL.md) 一系列行為前先執行，為了之後出錯時，可以直接 Rollback
- `$groups_table`: 每日維護的 grouping reslut，通常是 `tagtoo-tracking.event_prod.user_unify_groups`
- `$snapshot_table`: Snapshot table 的名稱，目前是以要執行的那天日期命名，EX: `tagtoo-tracking.event_grouping_result_prod.result-2022-10-01`
- `$expiration_interval`: 當前的預設值為 30 天

### rollback_user_unify_groups.sql
```sql
-- Roll back user unify groups with snapshot
CREATE OR REPLACE TABLE `$groups_table`
CLONE `$snapshot_table`;
```

- 參考文件中的 [Overwrite an existing table](https://cloud.google.com/bigquery/docs/table-snapshots-restore#overwrite_an_existing_table) 實作，大致上就是使用 Snapshot Table 對既有的 Table 進行覆蓋
- `$groups_table`: 每日維護的 grouping reslut，通常是 `tagtoo-tracking.event_prod.user_unify_groups`
- `$snapshot_table`: Snapshot table 的名稱，目前是以要執行的那天日期命名，EX: `tagtoo-tracking.event_grouping_result_prod.result-2022-10-01`