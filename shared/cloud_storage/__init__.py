import csv
import re
from typing import List, Optional, <PERSON><PERSON>

import fastavro
from google.auth.credentials import Credentials
from google.cloud.storage import Client
from google.cloud.storage.blob import Blob
from google.cloud.storage.bucket import Bucket

from .exceptions import GoogleStorageIOError, LtaExceptions


class StorageServiceBase:
    project: str = None
    credentials: Credentials = None
    _batch_size = 1000
    file_reader = {
        'csv': {'reader': csv.DictReader, 'mode': 'r'},
        'avro': {'reader': fastavro.reader, 'mode': 'rb'},
    }

    def __init__(
        self,
        project: str = None,
        credentials: Credentials = None,
    ):
        self.project = project or self.project
        self.credentials = credentials or self.credentials
        if self.project is None:
            raise AttributeError('project is not set.')

    def download_file_as_string(self) -> str:
        raise NotImplementedError

    def download_file_to_path(self) -> None:
        raise NotImplementedError

    def _batch_read_generator(self, reader, batch_size: int = None) -> List[dict]:
        batch_size = batch_size or self._batch_size
        error = LtaExceptions()
        line_count = 0
        while True:
            data = []
            for _ in range(batch_size):
                line_count += 1
                try:
                    datum = next(reader, None)
                except Exception as err:
                    error.add_error(
                        error=f'{err}',
                        line_number=line_count,
                    )
                else:
                    if datum is None:
                        break
                    data.append(datum)
            if len(data) == 0:
                break
            yield data
        if len(error.errors) > 0:
            raise error

    def reader_check(self, file_type: str) -> bool:
        return file_type in self.file_reader

    def batch_read_file(self) -> List[dict]:
        raise NotImplementedError


class GoogleStorageService(StorageServiceBase):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self._client = Client(
            project=self.project,
            credentials=self.credentials,
        )

    def _get_bucket(self, bucket_name: str) -> Optional[Bucket]:
        try:
            bucket = self._client.get_bucket(bucket_name)
        except Exception as err:
            raise GoogleStorageIOError(err)
        return bucket

    def _extract_bucket_and_blob_name(self, file_name: str) -> Tuple[str]:
        # Example path
        # gs://tagtoo-ml-workflow/topic06/
        # user_unify_purchase_power_remarketing_20220115-20220214/
        # 245ba23d-a3k4-24k5-3mk5-3294g40le3iq/purchase_power_mapping/
        # Purchase_Power_S2S_SegmentId_WithUserData_20220217.csv
        bucket_name = (
            result.group(1)
            if (result := re.search('gs://([_\w-]+)/', file_name))
            else ''
        )
        blob_name = re.sub(f'gs://{bucket_name}/', '', file_name)
        return (bucket_name, blob_name)

    def _get_blob(self, file_name: str) -> Optional[Blob]:
        bucket_name, blob_name = self._extract_bucket_and_blob_name(file_name)
        bucket = self._get_bucket(bucket_name)
        return bucket.get_blob(blob_name)

    def download_file_as_string(self, file_name: str) -> str:
        blob = self._get_blob(file_name)
        return blob.download_as_string() if blob else ''

    def download_file_to_path(self, file_name: str, path: str) -> None:
        blob = self._get_blob(file_name)
        if blob:
            blob.download_to_filename(path)

    def batch_read_file(
        self,
        file_name: str,
        batch_size: int = None,
    ) -> List[dict]:
        file_type = file_name.split('.')[-1]
        if not self.reader_check(file_type):
            return self.download_file_as_string(file_name)

        blob = self._get_blob(file_name)
        if blob is None:
            raise GoogleStorageIOError(f'{file_name} not found')
        with blob.open(self.file_reader[file_type]['mode']) as f:
            reader = self.file_reader[file_type]['reader'](f)
            yield from self._batch_read_generator(reader, batch_size)


# Mock Google Storage Service
# TODO: Change name from MockGoogleStorageService to StubGoogleStorageService
class MockGoogleStorageService(StorageServiceBase):
    project = 'Mock-GCS-service'

    def download_file_as_string(self, file_name: str) -> str:
        with open(file_name, 'r') as f:
            file_body = f.read()
        return file_body

    def download_file_to_path(self, file_name: str, path: str) -> None:
        file_body = self.download_file_as_string(file_name)
        with open(path, 'w') as f:
            f.write(file_body)

    def batch_read_file(
        self,
        file_name: str,
        batch_size: int = None,
    ) -> List[dict]:
        file_type = file_name.split('.')[-1]
        if not self.reader_check(file_type):
            return self.download_file_as_string(file_name)

        with open(file_name, self.file_reader[file_type]['mode']) as f:
            reader = self.file_reader[file_type]['reader'](f)
            yield from self._batch_read_generator(reader, batch_size)
