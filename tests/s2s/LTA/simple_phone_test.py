#!/usr/bin/env python3
"""
簡化版電話號碼 hash 處理邏輯驗證
直接測試核心邏輯，不依賴完整模組
"""

import re
from hashlib import sha256


def _encode_sha_256(text: str) -> str:
    """原本的 SHA256 編碼函數"""
    sha_256 = sha256(text.encode('utf8'))
    return sha_256.hexdigest()


def _is_already_hashed(value: str) -> bool:
    """檢測字串是否已經是 SHA256 hash"""
    return len(value) == 64 and all(c in '0123456789abcdef' for c in value.lower())


def _safe_encode_phone(phone: str) -> str:
    """新的安全電話號碼 hash 處理"""
    if not phone or not isinstance(phone, str):
        return ''
        
    # 檢查是否已經是 SHA256 hash
    if _is_already_hashed(phone):
        return phone.lower()  # 統一使用小寫
    else:
        # 原始電話號碼，進行格式化後 hash
        # 移除所有非數字字符（符號、空格、字母等） - 包括 + 號
        normalized = re.sub(r'[^\d]', '', phone)
        if not normalized:
            return ''
        return _encode_sha_256(normalized)


def original_phone_processing(phone: str) -> str:
    """模擬原本的電話號碼處理邏輯"""
    # 根據 re.sub(r'[^\d]', '', phone) 邏輯
    normalized = re.sub(r'[^\d]', '', phone)
    if not normalized:
        return ''
    return _encode_sha_256(normalized)


def main():
    print("📞 電話號碼 Hash 處理邏輯驗證")
    print("=" * 70)
    
    # 測試案例
    test_cases = [
        "+886-912-345-678",      # 包含 + 號的國際格式
        "886-912-345-678",       # 不含 + 號
        "0912-345-678",          # 本地格式
        "09123456789",           # 純數字
        "******-123-4567",       # 美國號碼
        "(02)2345-6789",         # 市話
        "+886 912 345 678",      # 空格分隔
        "",                      # 空字串
        "abc",                   # 純字母
        "+++---",               # 純符號
        # 已經是 hash 的值
        "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",  # 64字符
        "ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF12345678",  # 大寫 hash
    ]
    
    all_consistent = True
    
    for phone in test_cases:
        print(f"\n測試: {phone!r}")
        
        # 原本邏輯
        original_result = original_phone_processing(phone)
        
        # 新邏輯
        new_result = _safe_encode_phone(phone)
        
        # 檢查 + 號是否被正確移除
        normalized = re.sub(r'[^\d]', '', phone)
        plus_removed = '+' not in normalized
        
        # 對於需要 hash 的電話號碼，結果應該一致
        if _is_already_hashed(phone):
            # 已經是 hash，應該回傳小寫版本
            expected = phone.lower()
            consistent = (new_result == expected)
            note = "已 hash - 應直接返回小寫版本"
        else:
            # 需要 hash，結果應該與原邏輯一致
            consistent = (new_result == original_result)
            note = f"需 hash - + 號移除: {plus_removed}"
        
        print(f"  正規化後: {normalized!r}")
        print(f"  原本結果: {original_result!r}")
        print(f"  新的結果: {new_result!r}")
        print(f"  一致性: {'✅' if consistent else '❌'}")
        print(f"  備註: {note}")
        
        if not consistent:
            all_consistent = False
    
    print("\n" + "=" * 70)
    print(f"🎯 整體驗證結果: {'✅ 完全一致' if all_consistent else '❌ 發現不一致'}")
    
    # 特別驗證 + 號處理
    print(f"\n📋 + 號移除驗證:")
    plus_tests = ["+886912345678", "+1555123456", "886+912+345"]
    for test in plus_tests:
        normalized = re.sub(r'[^\d]', '', test)
        print(f"  {test!r} -> {normalized!r} (+ 已移除: {'+' not in normalized})")
    
    return all_consistent


if __name__ == '__main__':
    import sys
    success = main()
    sys.exit(0 if success else 1)