# User Unify 核心演算法

User Unify 系統使用多種算法來處理用戶身份統一的問題，主要包括：

1. 每日分組結果生成
2. Union Find 合併算法
3. 群組合併與更新策略

## Union Find
### 演算法簡介
此演算法主要是用來處理一些 [disjoint sets](https://zh.wikipedia.org/zh-tw/%E4%B8%8D%E4%BA%A4%E9%9B%86) 的合併及查詢，簡單來說就是對節點做分群，並提供查詢每個節點隸屬哪個群組，此演算法會將節點以樹的形式來表示，如下圖顯示，子節點會指向其父節點，而整棵樹的根節點就是該群組代表節點

![disjoin_set](./../../assets/img/UU_v2/disjoin_set.jpg)

此演算法主要提供兩個操作

- `Find(x)`: 找出 `x` 隸屬的群組 (根節點)，但由於樹在做合併時，可能會樹變得不平衡 (左邊的範例圖)，因此每次在做 `Find(x)` 時，要對走訪的路徑進行壓縮，這樣下次路徑上的節點有被查詢時，查詢速度就會變快
![Find & compress](./../../assets/img/UU_v2/compression.jpg)
    - 上圖中，要查詢的點為 `G`，途中會經過 `F` 跟 `D`，最到後到根節點 `A`，這之後，就會將經過的所有節點，將他們的父節點指向更改，全部改成指向根節點 `A`
    - 虛擬碼
    ```
        function Find(x)
            if x.parent = x then
                return x
            else
                x.parent := Find(x.parent)
                return x.parent
            end if
        end function
    ```
- `Union(x, y)`：將 `x` 跟 `y` 所在的群組做合併，找出 `x` 跟 `y` 所屬樹的根節點後，依據自定義的規則，將一邊的根節點指向另一方
![Union](./../../assets/img/UU_v2/union.jpg)
    - 上圖中，`X` 跟 `Y` 所屬的群組做合併，分別找出它們群組根節點 `A` & `B` 後，將 `B` 指向 `A`
    - 虛擬碼
    ```
    function Union(x, y)
        xRoot := Find(x)
        yRoot := Find(y)
        
        if xRoot ≠ yRoot then
            xRoot.parent := yRoot
        end if
    end function
    ```

### User Unify 實際應用
在 [Flow Chart](./workflow.md#determine-groups-update-grouping-reslut-detail-flowchart) 的第三步驟中，從 Big Query 得到的結果，類似下例
```python
predict_result = [
    {"permanent":"P1","group_ids":["G1","G2"]},
    {"permanent":"P2","group_ids":["G1","G3", "G30"]},
    {"permanent":"P3","group_ids":["G3","G4"]},
    {"permanent":"P5","group_ids":["G5","G7"]},
    {"permanent":"P6","group_ids":["G6","G7"]},
    {"permanent":"P7","group_ids":["G10","G12", "G15", "G20"]},
    {"permanent":"P8","group_ids":["G8","G12"]},
]
```
`permanent` 在 Union Find 沒什麼用，它們主要的用處就是當作導致群組合併的樞紐，主要用來做處理的是 `group_ids` 的部分，我們將每個 `group_ids` 視為一顆樹，`group_ids[0]` 預設為根節點，其餘 `group_ids[1:]` 的部分都視為子節點，每個子節點做指向根節點時，同時做 `Union()`，`Union()` 合併的規則為字典序較小者當新的根節點，以下用 `P5` 跟 `P6` 執行為例

-  `P5` 處理完後，會得到下圖的樹

![Union_example1](./../../assets/img/UU_v2/union_example.jpg)

-  接著處理 `P6`，需要將 `G7` 跟 `G6` 做合併，`G7` 的根節點為 `G5`，`G6` 則為自己，基於字典序 `G5 < G6`，所以將 `G6` 指向 `G5`
![Union_example2](./../../assets/img/UU_v2/union_example2.jpg)

- 簡易 Python Code
```python
# 以 dict 作為圖形紀錄
# key：child group id
# value：parent group id
root = {}

def find(node):
    root.setdefault(node, node)
    # 查詢並且壓縮
    if root[node] != node:
        root[node] = find(root[node])
    return root[node]

def union(x, y):
    parent_x = find(x)
    parent_y = find(y)

    # 若群組不同，以字典序小的根節點當新的根節點
    if parent_x < parent_y:
        root[parent_y] = parent_x
    elif parent_x > parent_y:
        root[parent_x] = parent_y

for datum in predict_result:
    head = datum['group_ids'][0]
    for group_id in datum['group_ids']:
        union(head, group_id)

# 重新再做一次查詢，將每一個群組做壓縮
_ = [find(key) for key in root.keys()]
```


### Reference
- [Wiki](https://zh.wikipedia.org/zh-tw/%E5%B9%B6%E6%9F%A5%E9%9B%86)

## 進階演算法與改進建議

### 信心分數系統
- 為不同屬性（email、phone、username、gid）分配不同權重，計算合併決策的信心分數。
- 多個屬性同時匹配時，信心分數更高。
- 設定閾值，只有信心分數超過閾值才進行合併。

### 智能 Union-Find 代表選擇
- 合併時不僅考慮字典序，也可考慮群組創建時間、屬性豐富度、活躍度等。
- 代表節點的選擇更貼近業務需求。

### 模糊匹配與識別碼生命週期
- 對 email、username 等屬性可採用 Levenshtein 距離等字串相似度算法。
- 追蹤識別碼的首次出現與最後活躍時間，提升資料有效性。

### 概率性記錄連結
- 結合多種特徵與上下文，計算兩條記錄屬於同一用戶的概率。
- 可採用 Fellegi-Sunter 等模型，或機器學習方法。

### 撤銷合併機制
- 記錄合併歷史，支持錯誤合併的識別與拆分。
- 提供人工審核與標記介面。

---

更多演算法細節與改進建議，請參考[改進建議與未來方向](improvement.md)。