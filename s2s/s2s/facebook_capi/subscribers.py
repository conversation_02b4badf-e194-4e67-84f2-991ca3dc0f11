import json
import logging
from collections import deque
from dataclasses import dataclass
from itertools import filterfalse, tee
from operator import itemgetter
from typing import List, Tuple

import pika

from shared import pubsub

from .. import amqp, service, settings
from .exceptions import (
    FacebookApiConnectTimeout,
    FacebookPixelDatumInvaild,
    FacebookRequestError,
)
from .utils import get_ec_pixels, post_facebook_capi

logging.basicConfig(format='[%(asctime)s %(levelname)s] %(message)s')
logger = logging.getLogger(__file__)
logger.setLevel(logging.INFO)


class FacebookCAPIErrorReportingService(service.ErrorReportingService):
    project = settings.GCP_PROJECT_ID
    credentials = None  # Use VM service account
    service = 'event-s2s/facebook-capi'

    def report_error(self, error: Exception, **kwargs) -> None:
        expected_error = {
            'EcommercePixelDoesNotExist': f'{error} result={kwargs}',
            'FacebookRequestError': f'{error}, Payload: {kwargs.get("data", [])}',
            'FacebookApiConnectTimeout': f'{error}, Payload: {kwargs.get("data", [])}',
            'FacebookPixelDatumInvaild': f'{error}',
        }
        if message := expected_error.get(error.__class__.__name__):
            self.report(message)
        else:
            self.report_exception()


class FacebookCAPISubscriberBatchV1(
    pubsub.parser.MessageParserMixin, pubsub.BaseSubscriberClient
):
    project_id = settings.GCP_PROJECT_ID
    subscription_name = settings.FACEBOOK_CAPI_SUBSCRIPTION_BATCH_V1
    message_attribute_parsers = {
        'version': pubsub.parser.parse_string,
        'ec_id': pubsub.parser.parse_int,
    }
    service_account_path = None  # Use VM service account
    max_retry_times = 5
    chunk_size = 1000
    timeout_base = 5
    power_base = 1.5
    error_report_service = FacebookCAPIErrorReportingService(version='v1')
    invalid_sets = (
        {'client_user_agent', 'country', 'ct', 'ge', 'st', 'zp'},
        {'client_user_agent', 'db'},
        {'fn', 'ge'},
        {'ge', 'ln'},
    )

    @dataclass
    class DataChunk:
        data: List[dict]
        pixel_id: str
        access_token: str
        counter: int = 1

    # FB Conversion API user_info 參數組合相關規範
    # https://developers.facebook.com/docs/marketing-api/conversions-api/best-practices/#req-rec-params
    def user_info_valid(self, datum: dict) -> bool:
        keys = set(key for key, val in datum.get('user_data', {}).items() if val)
        return all(keys - invalid_key for invalid_key in self.invalid_sets)

    def validate_pixel_data(self, data: List[dict]) -> Tuple[List[dict]]:
        valid_data, invalid_data = tee(data)
        return (
            list(filter(self.user_info_valid, valid_data)),
            list(filterfalse(self.user_info_valid, invalid_data)),
        )

    def callback_function(self, message):
        result = self.parse(message)
        data, version, ec_id = itemgetter('data', 'version', 'ec_id')(result)

        try:
            pixels = get_ec_pixels(ec_id)
        except Exception as error:  # noqa
            message.ack()
            self.error_report_service.report_error(
                error,
                version=version,
                ec_id=ec_id,
            )
            return False

        data, invalid_data = self.validate_pixel_data(data)

        # You can send up to 1,000 events in data
        queue = deque(
            [
                self.DataChunk(data[i : i + self.chunk_size], pixel_id, access_token)
                for i in range(0, len(data), self.chunk_size)
                for pixel_id, access_token in pixels
            ]
        )
        while queue:
            send_singleton = False
            chunk = queue.popleft()
            try:
                post_facebook_capi(
                    data=chunk.data,
                    pixel_id=chunk.pixel_id,
                    access_token=chunk.access_token,
                    timeout=self.timeout_base
                    * (self.power_base ** (chunk.counter - 1)),
                )
            except FacebookRequestError:
                send_singleton = True
                logging.info(
                    f' Batch with size {len(chunk.data)} will be '
                    'decompressed and reprocess in singleton mode.'
                )
            except FacebookApiConnectTimeout:
                if chunk.counter <= self.max_retry_times:
                    chunk.counter += 1
                    queue.append(chunk)
                else:
                    send_singleton = True

            if send_singleton:
                # Batch failed if any single item failed, cannot tell which one is failed, thus
                # queue this chunk to be split in the future and process in singleton mode
                with amqp.channel_scope() as channel:
                    fail_result = {
                        'data': chunk.data,
                        'version': version,
                        'pixel_id': chunk.pixel_id,
                        'access_token': chunk.access_token,
                    }
                    channel.basic_publish(
                        exchange='',  # Use default exchange
                        routing_key='facebook_capi_failed_batch',
                        body=json.dumps(fail_result).encode('utf-8'),
                        properties=pika.BasicProperties(
                            delivery_mode=2,  # make message persistent
                        ),
                    )
        message.ack()

        for invalid_datum in invalid_data:
            self.error_report_service.report_error(
                FacebookPixelDatumInvaild(invalid_datum)
            )


class LTAFacebookCAPIBatchSubscriber(FacebookCAPISubscriberBatchV1):
    subscription_name = settings.LTA_FACEBOOK_CAPI_SUBSCRIPTION_BATCH_V1


class FacebookCAPISubscriberSingletonV1(
    pubsub.parser.MessageParserMixin, pubsub.BaseSubscriberClient
):
    project_id = settings.GCP_PROJECT_ID
    subscription_name = settings.FACEBOOK_CAPI_SUBSCRIPTION_SINGLETON_V1
    message_attribute_parsers = {
        'version': pubsub.parser.parse_string,
        'pixel_id': pubsub.parser.parse_string,
        'access_token': pubsub.parser.parse_string,
    }
    service_account_path = None  # Use VM service account
    max_retry_times = 5
    timeout_base = 5
    power_base = 1.5

    def callback_function(self, message):
        result = self.parse(message)
        data, version, pixel_id, access_token = itemgetter(
            'data', 'version', 'pixel_id', 'access_token'
        )(result)
        error_report_service = FacebookCAPIErrorReportingService(version=version)
        retry_count = 1

        while retry_count <= self.max_retry_times:
            try:
                post_facebook_capi(
                    data=[data],
                    pixel_id=pixel_id,
                    access_token=access_token,
                    timeout=self.timeout_base * (self.power_base ** (retry_count - 1)),
                )
            except FacebookApiConnectTimeout as error:
                if retry_count >= self.max_retry_times:
                    error_report_service.report_error(error, data=data)
                retry_count += 1
            except Exception as error:
                error_report_service.report_error(error, data=data)
                break
            else:
                break

        message.ack()
