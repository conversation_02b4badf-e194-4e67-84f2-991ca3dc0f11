version: '3.7'

services:
  user_unify:
    &bq_writer
    container_name: user-unify
    image: ad_track_user_unify
    build:
      context: .
      dockerfile: user_unify/Dockerfile
    volumes:
      - ./user_unify:/app
      - ./gcloud/sa/event-user-unify-vm.json:/credentials/event-user-unify-vm.json
    environment:
      - GOOGLE_APPLICATION_CREDENTIALS=/credentials/event-user-unify-vm.json
    restart: always
    env_file: ./user_unify/.env
    command:
      - tail
      - -F
      - /dev/null
    networks:
      - user_unify

networks:
  user_unify:
    driver: bridge
