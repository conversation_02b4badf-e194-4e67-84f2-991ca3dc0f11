import time
from typing import Optional

from pydantic import BaseModel, Field, PositiveInt, validator

from app.utils import get_system_time

from .facebook import EventDataConfig, FacebookEventData
from .tagtoo import TagtooEventData


class EventData(BaseModel):
    token: str = Field(..., title='Auth Token')

    event_time: Optional[PositiveInt] = Field(
        None,
        title='Event Time',
        description=('Unix timestamp. The unit is in millisecond'),
    )
    conf: Optional[EventDataConfig] = Field(
        {},
        title='Configurations on event endpoint',
        description=('API-level specific configurations'),
    )

    # Channel Pixel Info
    facebook: Optional[FacebookEventData] = Field(
        {},
        title='Facebook Pixel Parameters',
        description=('A map that represent a server event for Facebook channel.'),
    )

    tagtoo: Optional[TagtooEventData] = Field(
        {},
        title='A map that represents an event for Tagtoo channel',
    )

    class Config:
        title = 'Event Data'

    @validator('event_time')
    def future_time_is_not_allowed(cls, v):
        if v is not None and v > get_system_time():
            raise ValueError('event_time should not be a future time')
        return v

    @property
    def event_api_scope_fields(self):
        return ['facebook', 'tagtoo']
