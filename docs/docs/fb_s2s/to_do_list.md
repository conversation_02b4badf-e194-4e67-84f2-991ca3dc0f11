## Optimization
- 關於 event api 的 FB data 驗證，為了比較方便統一處理，建議將來移到 S2S 這邊
- [S2S log 新增來源](https://github.com/Tagtoo/ad-track/issues/178)
- [LTA S2S 後端架構改善](https://github.com/Tagtoo/ad-track/issues/239)
- [LTA S2S 本機發送方式更動](https://github.com/Tagtoo/ad-track/issues/197)
- [Facebook S2S FacebookPixelDatumInvaild 錯誤訊息優化](https://github.com/Tagtoo/ad-track/issues/260)

## Future Problems
- 新增 Google Ads 渠道，先前的測試紀錄，可以參考 [Issue 175](https://github.com/Tagtoo/ad-track/issues/175)
- `facebook_capi_singleton` 目前是 FB_S2S 跟 LTA 共用，不確定將來是否可能會有問題，但目前也不方便區分錯誤資料是來自哪一邊
- LTA 的資料量偏大，不確定將來會不會更大，導致 subscriber 處理上遇到效能瓶頸，因為 subscriber 執行上是有 ack 的時間限制