"""
OptimizedIntegratedEventTransformer 單元測試

測試轉換器的核心功能，包括：
- 資料轉換正確性
- tracking_id 生成邏輯
- 事件過濾機制
- raw_json 最小化策略

版本: v2.1 (tracking_id 核心策略)
日期: 2025-08-18
"""

import unittest
import json
from datetime import datetime
from unittest.mock import patch, MagicMock

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from bq_writer.optimized_integrated_event_transformer import OptimizedIntegratedEventTransformer

# 測試常數
RAW_JSON_SIZE_LIMIT = 400  # bytes


class TestOptimizedIntegratedEventTransformer(unittest.TestCase):
    """OptimizedIntegratedEventTransformer 測試類別"""

    def setUp(self):
        """測試設置"""
        self.transformer = OptimizedIntegratedEventTransformer()
        self.sample_tagtoo_data = {
            "permanent": "test_permanent_123",
            "ec_id": 12345,
            "event_time": "2025-08-18T12:19:41Z",
            "link": "https://example.com/product/123",
            "session_id": "session_abc123def456",
            "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "referrer": "https://google.com/search?q=test",
            "ip_address": "*************",
            "language": "zh-TW",
            "event": {
                "name": "purchase",
                "value": 99.99,
                "currency": "USD",
                "custom_data": {
                    "order_id": "ORDER_123456"
                },
                "items": [
                    {"id": "item1", "name": "Product 1", "price": 49.99},
                    {"id": "item2", "name": "Product 2", "price": 50.00}
                ]
            },
            "user": {
                "em": "<EMAIL>",
                "ph": "+1234567890"
            },
            "location": {
                "country_code": "TW",
                "city": "Taipei"
            }
        }
        self.sample_message_id = "projects/test-project/subscriptions/test-sub/messages/123456789"

    def test_transform_basic_functionality(self):
        """測試基本轉換功能"""
        result = self.transformer.transform(self.sample_tagtoo_data, self.sample_message_id)

        # 檢查基本欄位
        self.assertEqual(result["permanent"], "test_permanent_123")
        self.assertEqual(result["ec_id"], 12345)
        self.assertEqual(result["partner_source"], "legacy-tagtoo-event")
        self.assertEqual(result["event_time"], "2025-08-18T12:19:41Z")
        self.assertEqual(result["link"], "https://example.com/product/123")

        # 檢查事件詳情
        self.assertEqual(result["event"], "purchase")
        self.assertEqual(result["value"], 99.99)
        self.assertEqual(result["currency"], "USD")
        self.assertEqual(result["order_id"], "ORDER_123456")
        self.assertEqual(len(result["items"]), 2)

        # 檢查用戶資訊
        self.assertIsNotNone(result["user"])
        self.assertEqual(result["user"]["em"], "<EMAIL>")
        self.assertEqual(result["user"]["ph"], "+1234567890")

        # 檢查位置資訊
        self.assertIsNotNone(result["location"])
        self.assertEqual(result["location"]["country_code"], "TW")

        # 檢查 create_time 存在且為 ISO 格式
        self.assertIsNotNone(result["create_time"])
        datetime.fromisoformat(result["create_time"].replace('Z', '+00:00'))

    def test_tracking_id_generation(self):
        """測試 tracking_id 生成邏輯"""
        result = self.transformer.transform(self.sample_tagtoo_data, self.sample_message_id)

        tracking_id = result["raw_json"]["tracking"]["id"]

        # 檢查 tracking_id 格式
        self.assertIsInstance(tracking_id, str)
        self.assertGreater(len(tracking_id), 0)
        self.assertLessEqual(len(tracking_id), 50)

        # 檢查包含預期的組件
        self.assertIn("12345", tracking_id)  # ec_id

        # 檢查唯一性
        result2 = self.transformer.transform(self.sample_tagtoo_data, self.sample_message_id + "_different")
        tracking_id2 = result2["raw_json"]["tracking"]["id"]
        self.assertNotEqual(tracking_id, tracking_id2)

    def test_raw_json_structure(self):
        """測試 raw_json 結構和大小"""
        result = self.transformer.transform(self.sample_tagtoo_data, self.sample_message_id)

        raw_json = result["raw_json"]

        # 檢查結構
        self.assertIn("tracking", raw_json)
        self.assertIn("debug", raw_json)

        # 檢查 tracking 欄位
        tracking = raw_json["tracking"]
        self.assertIn("id", tracking)
        self.assertIn("message_id", tracking)
        self.assertIn("source_ts", tracking)
        self.assertIn("process_ts", tracking)
        self.assertIn("version", tracking)
        self.assertEqual(tracking["version"], "v1.1")

        # 檢查 debug 欄位
        debug = raw_json["debug"]
        self.assertIn("session_hash", debug)
        self.assertIn("ua_signature", debug)
        self.assertIn("ref_domain", debug)
        self.assertIn("geo_code", debug)

        # 檢查大小 (應該 < 400 bytes，考慮到完整的 message_id)
        raw_json_size = len(json.dumps(raw_json))
        self.assertLess(raw_json_size, RAW_JSON_SIZE_LIMIT, f"raw_json 大小 {raw_json_size} bytes 超過預期")

        # 記錄實際大小以供參考
        print(f"實際 raw_json 大小: {raw_json_size} bytes")

    def test_event_filtering(self):
        """測試事件過濾機制"""
        # 測試允許的事件
        self.assertTrue(self.transformer.should_write_to_integrated("purchase"))
        self.assertTrue(self.transformer.should_write_to_integrated("view_item"))
        self.assertTrue(self.transformer.should_write_to_integrated("add_to_cart"))

        # 測試被排除的事件
        self.assertFalse(self.transformer.should_write_to_integrated("focus"))

    def test_ua_signature_extraction(self):
        """測試 User Agent 簽名提取"""
        # Chrome on Windows
        ua = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        signature = self.transformer._extract_ua_signature(ua)
        self.assertEqual(signature, "Chrome_Windows")

        # Firefox on Mac
        ua = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0"
        signature = self.transformer._extract_ua_signature(ua)
        self.assertEqual(signature, "Firefox_Mac")

        # 空值處理
        self.assertIsNone(self.transformer._extract_ua_signature(None))
        self.assertIsNone(self.transformer._extract_ua_signature(""))

    def test_domain_extraction(self):
        """測試網域提取"""
        # 正常 URL
        domain = self.transformer._extract_domain("https://www.google.com/search?q=test")
        self.assertEqual(domain, "www.google.com")

        # 長網域名稱截斷
        long_url = "https://very-long-domain-name-that-exceeds-twenty-characters.com/path"
        domain = self.transformer._extract_domain(long_url)
        self.assertEqual(len(domain), 20)

        # 空值處理
        self.assertIsNone(self.transformer._extract_domain(None))
        self.assertIsNone(self.transformer._extract_domain(""))

    def test_safe_conversions(self):
        """測試安全轉換函數"""
        # 字串轉換
        self.assertEqual(self.transformer._safe_str_convert(123), "123")
        self.assertEqual(self.transformer._safe_str_convert("test"), "test")
        self.assertIsNone(self.transformer._safe_str_convert(None))

        # 整數轉換
        self.assertEqual(self.transformer._safe_int_convert("123"), 123)
        self.assertEqual(self.transformer._safe_int_convert(456), 456)
        self.assertIsNone(self.transformer._safe_int_convert(None))
        self.assertIsNone(self.transformer._safe_int_convert(""))
        self.assertIsNone(self.transformer._safe_int_convert("invalid"))

    def test_hash_functions(self):
        """測試雜湊函數"""
        # 字串雜湊
        hash1 = self.transformer._hash_string("test", 8)
        self.assertEqual(len(hash1), 8)

        # 相同輸入產生相同雜湊
        hash2 = self.transformer._hash_string("test", 8)
        self.assertEqual(hash1, hash2)

        # 不同輸入產生不同雜湊
        hash3 = self.transformer._hash_string("different", 8)
        self.assertNotEqual(hash1, hash3)

        # 空值處理
        hash_empty = self.transformer._hash_string("", 8)
        self.assertEqual(hash_empty, "00000000")

    def test_missing_optional_fields(self):
        """測試缺少可選欄位的處理"""
        minimal_data = {
            "permanent": "test",
            "ec_id": 123,
            "event_time": "2025-08-18T12:19:41Z",
            "event": {"name": "test_event"}
        }

        result = self.transformer.transform(minimal_data, self.sample_message_id)

        # 檢查必填欄位存在
        self.assertEqual(result["permanent"], "test")
        self.assertEqual(result["ec_id"], 123)
        self.assertEqual(result["partner_source"], "legacy-tagtoo-event")

        # 檢查可選欄位為 None
        self.assertIsNone(result["user"])
        self.assertIsNone(result["location"])
        self.assertIsNone(result["value"])
        self.assertIsNone(result["currency"])
        self.assertIsNone(result["order_id"])

        # 檢查 raw_json 中的 debug 欄位處理 None 值
        debug = result["raw_json"]["debug"]
        self.assertIsNone(debug["session_hash"])
        self.assertIsNone(debug["ua_signature"])
        self.assertIsNone(debug["ref_domain"])
        self.assertIsNone(debug["geo_code"])

    def test_get_tracking_id_from_data(self):
        """測試從資料中提取 tracking_id"""
        result = self.transformer.transform(self.sample_tagtoo_data, self.sample_message_id)

        # 正常提取
        tracking_id = self.transformer.get_tracking_id_from_data(result)
        self.assertIsNotNone(tracking_id)
        self.assertEqual(tracking_id, result["raw_json"]["tracking"]["id"])

        # 異常資料處理
        invalid_data = {"raw_json": {}}
        self.assertIsNone(self.transformer.get_tracking_id_from_data(invalid_data))

        empty_data = {}
        self.assertIsNone(self.transformer.get_tracking_id_from_data(empty_data))


if __name__ == '__main__':
    # 設置日誌
    import logging
    logging.basicConfig(level=logging.DEBUG)

    unittest.main()
