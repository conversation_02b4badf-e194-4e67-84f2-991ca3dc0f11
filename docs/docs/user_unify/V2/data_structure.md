# 關鍵數據結構

User Unify 系統主要使用以下數據結構來儲存和處理用戶身份統一的相關資訊。

## 1. user_unify_groups 表

這是 User Unify 系統的核心表，儲存了所有的用戶群組信息。

### 主要字段

| 字段名 | 類型 | 描述 |
| --- | --- | --- |
| group_id | STRING | 群組的唯一標識符 |
| group_time | TIMESTAMP | 群組創建時間 |
| permanents | ARRAY<STRING> | 屬於該群組的所有 permanent ID 列表 |
| emails | ARRAY<STRING> | 該群組的所有電子郵件地址 |
| phones | ARRAY<STRING> | 該群組的所有電話號碼 |
| usernames | ARRAY<STRING> | 該群組的所有用戶名 |
| gids | ARRAY<STRING> | 該群組的所有 GID（全局標識符） |

### 用途

- 維護用戶群組的完整視圖
- 支援快速的用戶身份查詢
- 提供用戶屬性的集合視圖

### 示例數據

```json
{
  "group_id": "g12345",
  "group_time": "2023-01-01T00:00:00",
  "permanents": ["p111", "p222", "p333"],
  "emails": ["<EMAIL>"],
  "phones": ["+886912345678"],
  "usernames": ["user123"],
  "gids": ["gid123", "gid456"]
}
```

## 2. user_unify_group_permanent 表

這是一個扁平化的映射表，直接關聯 permanent ID 與其對應的 group_id。

### 主要字段

| 字段名 | 類型 | 描述 |
| --- | --- | --- |
| permanent | STRING | 用戶的永久標識符 |
| group_id | STRING | 該 permanent 所屬的群組 ID |

### 用途

- 提供 permanent ID 到 group_id 的高效查詢
- 支援其他系統快速查詢用戶所屬群組
- 避免解析 user_unify_groups 表中的複雜數組結構

### 示例數據

```json
[
  {"permanent": "p111", "group_id": "g12345"},
  {"permanent": "p222", "group_id": "g12345"},
  {"permanent": "p333", "group_id": "g12345"}
]
```

## 3. 臨時表和快照

### 每日分組結果表

- 表名格式：`tagtoo-tracking.event_grouping_result_prod.result-YYYY-MM-DD`
- 用途：儲存每日新產生的分組結果

### 中間預測表

- 表名：`_intermediate_predict`
- 用途：儲存 Union Find 算法處理後的中間結果

### user_unify_groups 快照表

- 表名格式：`tagtoo-tracking.event_grouping_result_prod.user_unify_groups_YYYY_MM_DD`
- 用途：在執行操作前創建的快照，用於錯誤情況下的回滾

## 數據流動圖解

以下圖表展示了 User Unify 系統中數據的流動過程：

```mermaid
flowchart TD
    A[Tagtoo_event 表] --> B[每日分組結果表]
    B --> C[user_unify_groups 表]
    A --> D[預測候選群組]
    D --> E[Union Find 處理]
    E --> F[中間預測表]
    F --> C
    C --> G[user_unify_group_permanent 表]
```

## 數據關係

- **永久標識符 (permanent ID)** 是系統中最基本的用戶標識單位
- 每個 **permanent ID** 都屬於且僅屬於一個 **group_id**
- 每個 **group_id** 可以包含多個 **permanent ID**
- **group_id** 也聚合了多種用戶屬性（電子郵件、電話等）
- 當系統發現兩個 **group_id** 應該合併時，會將其中一個指向另一個作為根節點
