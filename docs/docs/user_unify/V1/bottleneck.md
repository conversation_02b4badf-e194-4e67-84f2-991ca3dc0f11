## BigTable Performance

!!!info
    In general, Bigtable offers optimal latency when the CPU load for a cluster is under 70%.

For streaming operations, only 1 node and 20% utilization is needed. When performing a rewrite operation, it's
recommended to increase the node size from 1 to 2.

**Read latency** is an important metric for user unify, a reasonable latency is between 20ms ~ 200ms, which is affected by:

1. Server's response latency (Server CPU's capacity).
2. Client's latency to receive the response (Client CPU's capacity).

## Ack Latency

The lack latency of user unify operation is not static, in different circumstances it took different time to operate.
For example:

* Operation of Entity A took 10s, then operate it again only took 100ms, because the database stores the operation state to prevent rework.
* Operation of Entity J took 1s, but operation of Entity K took 100s, because <PERSON> involves in a small graph (1 node) and <PERSON> involves in a large graph (500 node). The more node in a graph, it took more time to traverse.

## Operation Bottleneck

Overall, user unify is not a latency-sensitive application (it's acceptable to take more times to calculate the result), 
but when setting in a streaming workflow, was eventually limited by the Pub/Sub subscription's maximum ack deadline - 600 seconds (10 minute).

```
V(Permanent counts in a group) * H(Read latency of a permanent, 20ms ~ 200ms) < 600s
```

When the data grows and to traverse a graph took more than 600s, the operation exceed the max ack deadline and break
the reliability of the application.
