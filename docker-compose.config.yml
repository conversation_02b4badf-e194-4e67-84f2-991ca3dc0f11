version: '3.7'

volumes:
  config_db_data: {}

services:
  config_api:
    container_name: config-api
    image: ad_track_config_api
    build:
      context: ./config_api
    command: ./scripts/start.sh
    entrypoint: ./scripts/entrypoint.sh
    restart: on-failure
    volumes:
      - ./config_api:/app
    depends_on:
      - config_db
    env_file:
      - ./config_api/.env
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/readiness"]
      interval: 5s
      start_period: 5s
    ports:
      - "10001:8000"
    networks:
      - config

  config_db:
    container_name: config-db
    image: postgres:13
    volumes:
      - config_db_data:/var/lib/postgresql/data
    env_file:
      - ./config_api/.env
    networks:
      - config

networks:
  config:
    name: config
    driver: bridge
