#!/bin/bash
#
# E2E test from Event API /event endpoint to Pub/Sub subscription
# Data field `event_time`, `token` is auto-patched
#
# Usage:
#   command topic_path subscription_path messages file

source ./functions.sh

ec_id=9999
token="test_token"
topic_path="$1"
subscription_path="$2"
messages="$3"
file="$4"

trap 'exit 1' HUP INT PIPE QUIT TERM
trap '[[ $? -eq 1 ]] && docker logs api' EXIT

echo -n " > Init config data in config database ... "

if init_config_data "$ec_id" "$token"; then success '[OK]'; else error '[ERROR]'; fi

echo -n " > Update config data from config API ..."

if update_api_db; then success '[OK]'; else error '[ERROR]'; fi

echo -n " > Setup subscription $subscription_path ... "

if setup_subscription "$topic_path" "$subscription_path"; then success '[OK]'; else error '[Error]'; fi

test -f "$file" || error "Invalid file $file."

echo -n " > Post test data from $file ... "

if post_test_data "$file" "$token" "v1"; then success '[OK]'; else error '[ERROR]'; fi

sleep 3

echo -n " > Check subscription "$subscription_path" has messages ... "

if check_subscription_message_count "$subscription_path" "$messages"; then success '[PASSED]'; else error '[ERROR]'; fi

echo -n " > Validate message from $subscription_path ... "

if validate_event_message "$subscription_path" "$ec_id" "v1"; then success '[PASSED]'; else error '[ERROR]'; fi
