<script src="https://unpkg.com/mermaid@8.13.3/dist/mermaid.min.js"></script>

## Docker Compose

```mermaid
flowchart TB
  subgraph docker bridge network
    subgraph network:bt_writer
        bt-writer-pubsub -- pull --> user-unify-subscriber
        user-unify-subscriber --> user-unify-bigtable[(user-unify-bigtable)]
    end
  end
```

1. Start the services
   ```shell
   docker-compose -f docker-compose.bt_writer.yml up
   ```
2. Turn down the services
   ```shell
   docker-compose -f docker-compose.bt_writer.yml down
   ```

!!! info
    The Pub/Sub local service is an emulator built from `messagebird/gcloud-pubsub-emulator`.

!!! info 
    The BigTable local service is an emulator instance built and defined at folder `bigtable_emulator`.

# Test

Use shell scripts to test across services. Start the services and run these commands at `tests/user_unify` folder:

```shell
./test_all.sh
```
