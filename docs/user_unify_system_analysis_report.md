# User Unify 系統完整技術文件

**文件日期：** 2025年9月10日
**系統版本：** V2.0
**維護團隊：** Tagtoo User Unify 開發團隊
**文件狀態：** 整合版本

---

## 📋 文件概述

本文件整合了 User Unify 系統的所有技術文件，包含系統概述、架構設計、實作細節、操作指南、測試驗證、改進建議等完整內容。適合技術團隊深入了解系統架構，也適合業務團隊理解系統價值和應用場景。

### 📚 文件結構
- **第一部分**：系統概述與架構
- **第二部分**：技術實作細節
- **第三部分**：操作與維護指南
- **第四部分**：測試與驗證方法
- **第五部分**：改進建議與未來發展

---

## 🎯 系統概述

User Unify 是 Tagtoo 追蹤系統中負責**使用者身份統一和識別**的核心組件。系統透過分析使用者的多種標識符（email、手機號碼、permanent ID、GID 等），識別並關聯屬於同一使用者的不同身份，建立完整的使用者畫像。

### 主要功能
- **身份統一**：識別和關聯同一用戶的多個標識符
- **用戶畫像**：建立完整的用戶身份視圖
- **查詢優化**：提供高效的用戶群組查詢
- **跨裝置追蹤**：透過共同標識符連結不同裝置的使用者行為

### 關鍵特性
- ✅ **高效能大數據處理**：基於 BigQuery 的分散式運算
- ✅ **智慧身份統一**：使用 Union Find 演算法處理複雜關聯
- ✅ **強健的錯誤恢復**：完整的快照和回滾機制
- ✅ **可擴展架構**：支援大規模使用者資料處理
- ✅ **每日批次處理**：透過 Kubernetes cron job 自動執行

---

## 🏗️ 系統架構分析

### 1. 整體架構設計

```mermaid
graph TD
    A[Tagtoo Event 表] --> B[每日分組結果生成]
    B --> C[新實體合併]
    C --> D[候選群組預測]
    D --> E[Union Find 演算法]
    E --> F[中間結果表]
    F --> G[解析候選群組合併]
    G --> H[無用群組清理]
    H --> I[user_unify_groups 表]
    I --> J[user_unify_group_permanent 表]

    K[快照機制] --> I
    L[回滾機制] --> I
```

### 2. 核心模組架構

#### 2.1 主要 Python 模組

<augment_code_snippet path="user_unify/user_unify/process.py" mode="EXCERPT">
```python
def determine_groups(
    event_date: date,
    client: bigquery.Client,
    dataset: str,
    grouping_result_dataset: str,
    recalc_daily_grouping_result: bool = False,
):
    '''The main process of candidate groups determination

    Steps:
    * Calculate the daily grouping result from event data
    * Merge new entities into existed grouping result table
    * Predict the final group for groups with multiple candidates (Union Find)
    * Write the result into an intermediate table
    * Merge the above result into user_unify_groups
    * Delete the useless rows
    '''
```
</augment_code_snippet>

#### 2.2 設定管理

<augment_code_snippet path="user_unify/user_unify/settings.py" mode="EXCERPT">
```python
GCP_PROJECT = env.str('GCP_PROJECT', 'tagtoo-tracking')
BQ_DATASET = env.str('BQ_DATASET')
BQ_GROUPING_RESULT_DATASET = env.str('BQ_GROUPING_RESULT_DATASET')
BQ_SNAPSHOT_DATASET = env.str('BQ_SNAPSHOT_DATASET')
BQ_TABLE_USER_UNIFY_GROUPS = env.str('BQ_TABLE_USER_UNIFY_GROUPS')
```
</augment_code_snippet>

### 3. 詳細工作流程

#### 3.1 每日處理流程圖

```mermaid
flowchart TD
    A([Daily cron job triggered]) --> B[Check process date range]
    B --> C[Create Daily Grouping Result Snapshot]
    C --> D[Determine groups]
    D --> E{Execute} -- Success --> F[Update Grouping Result]
    E -- Error --> G[RollBack] --> Z([End])
    F --> H{Execute} -- Success --> I{Have other days?}
    H -- Error --> G
    I -- Yes --> C
    I -- No --> Z
```

#### 3.2 主要處理步驟

1. **創建快照**：執行操作前創建 user_unify_groups 表的快照
2. **獲取每日分組結果**：從 tagtoo_event 表中提取當天的用戶數據並分組
3. **合併新實體**：將每日分組結果合併到 user_unify_groups 表中
4. **預測候選群組**：找出每個 permanent 可能屬於的 group_id 群組
5. **解析多候選群組**：使用 Union Find 算法處理一個 permanent 屬於多個 group_id 的情況
6. **結果寫入**：將處理結果寫入相關表
7. **錯誤處理**：提供錯誤處理和回滾機制

#### 3.3 詳細處理流程

1. **執行 merge_new_entities.sql**
   - 使用 `get_grouping_result_by_date()` 從 Tagtoo event 撈出當天的使用者資訊
   - 將有相同特徵的使用者聚集成一個群組，產出 Daily Group Result
   - 將該結果 Merge 進持續維護的 Group Result

2. **執行 predict_candidate.sql**
   - 使用 `get_entity_by_date()` 從 Tagtoo event 撈出當天的使用者資訊作為 Daily Entity Data
   - 與上一步更新的 Group Result 做運算，找出每個 permanent 可以隸屬於哪些 group_id 群組

3. **Python Union Find 處理**
   - 處理一個 permanent 隸屬多個 group_id 群組的情況
   - 使用 SQL 語法處理會有困難，因此選擇使用 Python 來處理

4. **執行 merge_resolved_candidates.sql 和 delete_useless_group.sql**
   - 將 Python 計算後的結果存入暫存 Table
   - 完成更新 Group Result

#### 3.2 Union Find 演算法實作

<augment_code_snippet path="user_unify/user_unify/utils.py" mode="EXCERPT">
```python
def resolve_multiple_candidates(data: List[dict]) -> dict:
    root = {}

    def find(node):
        root.setdefault(node, node)
        if root[node] != node:
            root[node] = find(root[node])
        return root[node]

    def union(x, y):
        parent_x = find(x)
        parent_y = find(y)
        if parent_x < parent_y:
            root[parent_y] = parent_x
        elif parent_x > parent_y:
            root[parent_x] = parent_y
```
</augment_code_snippet>

---

## 🗄️ 資料庫架構分析

### 1. 核心資料表結構

#### 1.1 user_unify_groups 表

| 欄位名 | 類型 | 描述 |
|--------|------|------|
| group_id | STRING | 群組唯一標識符 |
| group_time | TIMESTAMP | 群組創建時間 |
| permanents | ARRAY<STRING> | 屬於該群組的所有 permanent ID |
| emails | ARRAY<STRING> | 該群組的所有電子郵件地址 |
| phones | ARRAY<STRING> | 該群組的所有電話號碼 |
| usernames | ARRAY<STRING> | 該群組的所有使用者名稱 |
| gids | ARRAY<STRING> | 該群組的所有 GID |

#### 1.2 資料表關聯

```mermaid
erDiagram
    tagtoo_event ||--o{ daily_grouping_result : generates
    daily_grouping_result ||--o{ user_unify_groups : merges_into
    user_unify_groups ||--o{ user_unify_group_permanent : creates
    user_unify_groups ||--o{ snapshot_tables : backs_up
```

### 2. BigQuery SQL 查詢邏輯

#### 2.1 新實體合併查詢

<augment_code_snippet path="user_unify/user_unify/bigquery/sql/merge_new_entities.sql" mode="EXCERPT">
```sql
MERGE `$groups_table` T
USING (
    WITH contact_res AS (
        SELECT * FROM `$grouping_result_table`
    ),
    DISTINST_res AS (
        SELECT
            group_id,
            group_time,
            ARRAY(SELECT DISTINCT p FROM contact_res.permanents p) permanents,
            ARRAY(SELECT DISTINCT em FROM contact_res.emails em) emails,
            ARRAY(SELECT DISTINCT ph FROM contact_res.phones ph) phones,
            ARRAY(SELECT DISTINCT un FROM contact_res.usernames un) usernames,
            ARRAY(SELECT DISTINCT gid FROM contact_res.gids gid) gids
        FROM contact_res
    )
```
</augment_code_snippet>

#### 2.2 解析候選群組合併

<augment_code_snippet path="user_unify/user_unify/bigquery/sql/merge_resolved_candidates.sql" mode="EXCERPT">
```sql
MERGE `$groups_table` T
USING (
    WITH Attr_contact AS (
        SELECT predict_result, T.*
        FROM `$intermediate_table` I
        JOIN `$groups_table` T ON I.group_id = T.group_id
    ),
    Distinct_res AS (
        SELECT
            predict_result group_id,
            ARRAY(SELECT DISTINCT p FROM Attr_contact.permanents p) permanents,
            ARRAY(SELECT DISTINCT em FROM Attr_contact.emails em) emails
        FROM Attr_contact
    )
```
</augment_code_snippet>

---

## 🔧 技術實作細節

### 1. 使用的技術棧

#### 1.1 核心技術
- **Python 3.8**：主要程式語言
- **Google BigQuery**：大數據處理和儲存
- **Docker**：容器化部署
- **Google Cloud Platform**：雲端基礎設施

#### 1.2 第三方套件
- `google-cloud-bigquery`：BigQuery 客戶端
- `environs`：環境變數管理
- `pytz`：時區處理

#### 1.3 Docker 配置

<augment_code_snippet path="user_unify/Dockerfile" mode="EXCERPT">
```dockerfile
FROM --platform=linux/amd64 python:3.8-slim

ENV PYTHONFAULTHANDLER=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONHASHSEED=random \
    PIP_NO_CACHE_DIR=off \
    PIP_DISABLE_PIP_VERSION_CHECK=on

WORKDIR /app
COPY user_unify/requirements.txt .
RUN pip install --upgrade pip && \
    pip install -r requirements.txt
```
</augment_code_snippet>

### 2. 錯誤處理和日誌記錄

#### 2.1 錯誤報告服務

<augment_code_snippet path="user_unify/user_unify/service.py" mode="EXCERPT">
```python
class UserUnifyErrorReportingService(ErrorReportingService):
    project = settings.GCP_PROJECT
    credentials = None  # Use VM service account
    service = 'event-user-unify'

error_reporting_service = UserUnifyErrorReportingService('v1')
```
</augment_code_snippet>

#### 2.2 快照和回滾機制

<augment_code_snippet path="user_unify/user_unify/process.py" mode="EXCERPT">
```python
def rollback_user_unify_groups_table(
    event_date: date,
    client: bigquery.Client,
    dataset: str,
    snapshot_dataset: str,
):
    rollback_filepath = os.path.join(SQL_PATH, 'rollback_user_unify_groups.sql')
    snapshot_table = (
        f'{snapshot_dataset}.user_unify_groups_{event_date.strftime("%Y_%m_%d")}'
    )
```
</augment_code_snippet>

### 3. 效能最佳化策略

#### 3.1 BigQuery 查詢最佳化
- **分區表設計**：按日期分區提升查詢效能
- **陣列欄位去重**：使用 `ARRAY(SELECT DISTINCT)` 減少資料重複
- **批次處理**：每日批次處理而非即時處理

#### 3.2 記憶體最佳化
- **分段處理**：大型資料集分段載入和處理
- **中間表機制**：使用臨時表避免記憶體溢出

---

## 🧮 核心演算法詳解

### 1. Union Find 演算法

#### 1.1 演算法簡介

Union Find 演算法主要用來處理一些不相交集合的合併及查詢，簡單來說就是對節點做分群，並提供查詢每個節點隸屬哪個群組。此演算法會將節點以樹的形式來表示，子節點會指向其父節點，而整棵樹的根節點就是該群組代表節點。

#### 1.2 主要操作

**Find(x) 操作**：
- 找出 x 隸屬的群組（根節點）
- 每次在做 Find(x) 時，對走訪的路徑進行壓縮
- 這樣下次路徑上的節點有被查詢時，查詢速度就會變快

**Union(x, y) 操作**：
- 將 x 跟 y 所在的群組做合併
- 找出 x 跟 y 所屬樹的根節點後，依據自定義的規則，將一邊的根節點指向另一方

#### 1.3 User Unify 實際應用

在 User Unify 系統中，從 BigQuery 得到的結果類似：

```python
predict_result = [
    {"permanent":"P1","group_ids":["G1","G2"]},
    {"permanent":"P2","group_ids":["G1","G3", "G30"]},
    {"permanent":"P3","group_ids":["G3","G4"]},
    {"permanent":"P5","group_ids":["G5","G7"]},
    {"permanent":"P6","group_ids":["G6","G7"]},
]
```

處理邏輯：
- `permanent` 作為導致群組合併的樞紐
- 每個 `group_ids` 視為一顆樹，`group_ids[0]` 預設為根節點
- 其餘 `group_ids[1:]` 都視為子節點，每個子節點指向根節點時，同時做 Union()
- Union() 合併的規則為字典序較小者當新的根節點

### 2. 每日分組結果生成演算法

#### 2.1 實體鄰居查找

使用 `get_entity_neighbors_by_date()` 函數：
- 基於相同屬性（email、phone、username、gid）找出具有關聯的 permanent
- 使用 self join 找出具有相同屬性的 permanent，定為 neighbors

#### 2.2 群組結果生成

使用 `get_grouping_result_by_date()` 函數：
- 先將鄰居資料整理，以 entity_time 作為排序基礎
- 第二次聚合，將相同 group id 的聚合在一起
- 以時間最早的 permanent 作為 group id

---

## 🗄️ BigQuery SQL 實作詳解

### 1. 核心 SQL 檔案

#### 1.1 merge_new_entities.sql

**功能**：將每日分組結果合併到持續維護的 Group Result

**主要邏輯**：
- 抓出指定日期的 Grouping result 作為 next_day_result
- 將 next_day_result 跟 groups_table 相同 group id 的欄位合併
- 使用 IFNULL 處理 ARRAY_CONCAT() 遇到 NULL 的情況
- 對合併結果做 DISTINCT，保留唯一值

**關鍵 SQL 片段**：
```sql
MERGE `$groups_table` T USING (
    WITH next_day_result AS (
        SELECT * FROM `$grouping_result_table`
    ),
    contact_res AS (
        SELECT
            cnds.group_id,
            IFNULL(temp.group_time, cnds.group_time) group_time,
            IFNULL((temp.permanents || cnds.permanents), cnds.permanents) permanents,
            IFNULL((temp.emails || cnds.emails), cnds.emails) emails
        FROM next_day_result cnds
        LEFT JOIN `$groups_table` temp ON temp.group_id = cnds.group_id
    )
```

#### 1.2 predict_candidate.sql

**功能**：找出每個 permanent 可能隸屬的候選群組

**主要邏輯**：
- 將 groups_table 的各個屬性展開（flatten）
- 使用每日的 entity_data 去跟各個屬性做比對
- 聚合找出每個 permanent 可以讓多少個群組合併
- 只提取候選 group_id 超過一個以上的 permanent

**關鍵特點**：
- 每個屬性需要獨自取出分別比較，避免多屬性同時比對的邏輯錯誤
- 使用 ARRAY_AGG(DISTINCT group_id IGNORE NULLS) 聚合候選群組
- 只處理 ARRAY_LENGTH(group_ids) > 1 的情況

#### 1.3 merge_resolved_candidates.sql

**功能**：合併 Union Find 算法處理後的結果

**主要邏輯**：
- 取出 Union Find 的結果（group_id、predict_result）
- 將相同 predict_result 的群組做聚合
- 對聚合後的各個屬性做 DISTINCT
- 只針對 predict_result 做更新處理

#### 1.4 delete_useless_group.sql

**功能**：刪除合併後不再需要的群組

**主要邏輯**：
```sql
DELETE `$groups_table`
WHERE group_id IN (
    SELECT group_id FROM `$intermediate_table`
    WHERE group_id != predict_result
);
```

### 2. Table Functions

#### 2.1 get_entity_by_date()

**功能**：從 tagtoo_event 表產出 Daily Entity Data

**參數**：
- start_date：STRING
- end_date：STRING

**主要特點**：
- 將 entity_time 設為最早的訪問時間
- 排除不合規格的 gid（使用正規表達式驗證）
- 目前已移除 fbp 欄位（基於效能考量）

#### 2.2 get_entity_neighbors_by_date()

**功能**：找出具有相同屬性的 permanent neighbors

**主要邏輯**：
- 基於 get_entity_by_date() 的結果
- 使用 self join 找出具有相同屬性的 permanent
- 支援 email、phone、username、gid 的匹配

#### 2.3 get_grouping_result_by_date()

**功能**：產出最終的 Daily Group Result

**主要邏輯**：
- 基於 get_entity_neighbors_by_date() 的結果
- 以時間最早的 permanent 作為 group id
- 進行兩次聚合：第一次按 permanent，第二次按 group_id

---

## 💼 業務邏輯理解

### 1. User Unify 核心功能

#### 1.1 使用者身份統一
- **多重標識符關聯**：email、phone、username、permanent ID、GID
- **跨裝置識別**：透過共同標識符連結不同裝置的使用者行為
- **歷史資料整合**：持續累積和更新使用者身份資訊

#### 1.2 群組管理機制
- **動態群組更新**：每日增量更新群組資訊
- **衝突解決**：使用 Union Find 演算法處理複雜的群組關聯
- **資料一致性**：確保群組資料的完整性和準確性

### 2. 與追蹤系統的關聯

#### 2.1 資料來源
- **主要來源**：`tagtoo_event` 表中的使用者行為資料
- **標識符提取**：從事件資料中提取各種使用者標識符
- **即時更新**：每日處理新的使用者資料

#### 2.2 下游應用
- **廣告歸因**：提供準確的使用者身份用於廣告效果分析
- **個人化推薦**：支援跨裝置的個人化內容推薦
- **使用者分析**：提供完整的使用者行為分析基礎

---

## 🔄 系統工作流程

### 1. 每日處理流程

<augment_code_snippet path="user_unify/update_grouping_result.py" mode="EXCERPT">
```python
for date_ in date_range:
    process.generate_group_result_snapshot(
        event_date=date_,
        client=client,
        dataset=dataset,
        snapshot_dataset=snapshot_dataset,
    )
    try:
        result = process.determine_groups(
            event_date=date_,
            client=client,
            dataset=dataset,
            grouping_result_dataset=grouping_result_dataset,
            recalc_daily_grouping_result=args.recalc_daily_grouping_result,
        )
    except Exception as e:
        process.rollback_user_unify_groups_table(
            event_date=date_,
            client=client,
            dataset=dataset,
            snapshot_dataset=snapshot_dataset,
        )
```
</augment_code_snippet>

### 2. 命令列介面

#### 2.1 主要參數
- `-s, --start-date`：處理開始日期
- `-e, --end-date`：處理結束日期
- `-r, --recalc-daily-grouping-result`：強制重新計算每日分組結果

#### 2.2 執行範例
```bash
python update_grouping_result.py -s 2025-09-01 -e 2025-09-10 -r
```

---

## 📊 系統監控和維護

### 1. 日誌記錄機制

```python
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s %(levelname)s] %(message)s',
)
logger = logging.getLogger(__file__)
```

### 2. 錯誤處理策略

#### 2.1 自動回滾
- 處理失敗時自動回滾到快照狀態
- 保證資料一致性和系統穩定性

#### 2.2 錯誤報告
- 整合 Google Cloud Error Reporting
- 自動收集和分析錯誤資訊

### 3. 效能監控指標

| 指標類型 | 監控項目 | 正常範圍 |
|---------|---------|---------|
| 處理時間 | 每日處理完成時間 | < 4 小時 |
| 資料量 | 每日處理記錄數 | 依業務量而定 |
| 錯誤率 | 處理失敗率 | < 1% |
| 資源使用 | BigQuery 查詢成本 | 監控預算 |

---

## 🎯 系統優勢與限制

### 優勢

1. **高可擴展性**
   - 基於 BigQuery 的雲端架構
   - 支援 PB 級資料處理

2. **高可靠性**
   - 完整的快照和回滾機制
   - 自動錯誤恢復

3. **高準確性**
   - Union Find 演算法確保群組關聯正確性
   - 多重標識符提升識別準確度

### 限制

1. **處理延遲**
   - 每日批次處理，非即時更新
   - 新使用者識別有 24 小時延遲

2. **成本考量**
   - BigQuery 查詢成本隨資料量增長
   - 需要持續監控和最佳化

3. **複雜性**
   - 系統邏輯複雜，維護成本較高
   - 需要專業技術人員維護

---

## �️ 操作與維護指南

### 1. 系統部署

#### 1.1 Docker 環境

User Unify V2 使用 Kubernetes cron job 來處理，相關設定可參考 `user_unify.tf`。

**本機手動執行流程**：

1. 啟動 User Unify 環境
```bash
docker compose -f docker-compose.user_unify.yml up
```

2. 連上該 container
```bash
docker exec -it user-unify /bin/bash
```

3. 執行 update_grouping_result
```bash
python update_grouping_result.py -s start_date -e end_date
```

4. 關閉 container
```bash
docker compose -f docker-compose.user_unify.yml down
```

#### 1.2 命令列參數

**update_grouping_result.py 參數**：

| 參數 | 描述 | 預設值 |
|------|------|--------|
| -s, --start-date | 處理開始日期 | 昨天 |
| -e, --end-date | 處理結束日期 | 昨天 |
| -r, --recalc-daily-grouping-result | 強制重新計算每日分組結果 | False |

**執行範例**：
```bash
python update_grouping_result.py -s 2025-09-01 -e 2025-09-10 -r
```

### 2. 快照和回滾

#### 2.1 快照機制

**快照創建**：
- 每次處理前自動創建 user_unify_groups 表的快照
- 快照表名格式：`user_unify_groups_YYYY_MM_DD`
- 快照保留期限：30 天

**快照用途**：
- 錯誤情況下的資料恢復
- 歷史資料比較和分析
- 系統測試和驗證

#### 2.2 回滾操作

**自動回滾**：
```sql
CREATE OR REPLACE TABLE `$groups_table`
CLONE `$snapshot_table`;
```

**手動回滾**：
1. 確認快照表存在
2. 執行回滾 SQL
3. 驗證資料完整性
4. 重新執行處理流程

---

## 🧪 測試與驗證方法

### 1. 建立 Ground Truth 資料集

#### 1.1 手動標記樣本

**實施步驟**：
- 從現有資料中隨機抽取 100-200 個群組
- 由業務專家手動審核這些群組
- 記錄審核結果，建立高質量的 ground truth 資料集

**實現方式**：
- 開發簡單的標記工具
- 整合審核結果，建立基準資料集

#### 1.2 使用已知關聯的用戶資料

**資料來源**：
- 用戶登錄後關聯的多個設備或會話
- CRM 系統中已確認的用戶身份關聯資料

**實現方式**：
- 建立資料導入流程
- 設計驗證腳本，比較系統結果與確定性資料

#### 1.3 合成測試資料

**測試場景**：
- 同一用戶使用不同設備
- 用戶更換電子郵件
- 跨平台使用者行為

**實現方式**：
- 開發資料生成腳本
- 建立多樣化的測試場景

### 2. 驗證方法

#### 2.1 A/B 測試框架

**目的**：比較不同算法版本的效果

**評估指標**：
- 群組數量變化（合併效率）
- 群組大小分布變化
- 群組屬性豐富度變化

#### 2.2 歷史資料回測

**實施步驟**：
- 使用改進後的算法重新處理歷史資料
- 比較新舊結果的差異
- 關注被合併的群組數量和特徵

#### 2.3 人工抽樣審核

**實施流程**：
- 定期從系統結果中抽取樣本
- 由業務專家審核這些樣本
- 記錄審核結果，計算準確率指標

### 3. 測試指標

#### 3.1 群組一致性指標

**定義**：同一群組內用戶屬性的一致性程度

**計算方法**：
```text
一致性得分 = 共享屬性數量 / 總屬性數量
```

#### 3.2 群組穩定性指標

**定義**：群組結構在連續處理中的穩定程度

**計算方法**：
```text
穩定性得分 = 保持不變的群組數量 / 總群組數量
```

#### 3.3 合併效率指標

**定義**：系統合併相關用戶的效率

**計算方法**：
```text
合併效率 = 合併後的群組數量 / 合併前的群組數量
```

---

## �🔮 未來發展建議

### 1. 短期改進（1-3 個月）

#### 1.1 信心分數系統

**目標**：為不同匹配類型賦予權重

**實施方案**：
- 電子郵件匹配權重：1.0
- 手機號碼匹配權重：1.0
- 用戶名匹配權重：0.7
- GID 匹配權重：0.5
- 多個識別碼同時匹配的權重遠高於單一識別碼匹配

#### 1.2 屬性驗證增強

**改進項目**：
- 增強對電子郵件、手機號碼和用戶名的驗證和標準化
- 過濾臨時郵箱域名和無效格式的資料
- 排除不合規格的 GID

#### 1.3 SQL 查詢最佳化

**最佳化方向**：
- 重構關鍵 SQL 查詢，提高效率
- 減少不必要的資料掃描
- 使用分區表設計提升查詢效能

### 2. 中期改進（3-6 個月）

#### 2.1 智能 Union-Find 代表選擇

**改進策略**：
- 考慮群組創建時間（group_time）
- 群組豐富度（包含更多 permanent ID 或更多種類識別碼的群組）
- 群組活躍度（如果能追蹤最近活躍時間）

#### 2.2 模糊匹配與識別碼生命週期

**技術實現**：
- 對 email、username 引入字串相似度算法（如 Levenshtein 距離）
- 追蹤識別碼的首次出現時間和最後活躍時間
- 評估識別碼的有效性

#### 2.3 建立 Ground Truth 資料集

**實施計畫**：
- 開發標記工具，支持人工審核
- 建立測試環境，支持不同算法版本的比較
- 實施 A/B 測試框架

### 3. 長期發展（6-12 個月）

#### 3.1 機器學習增強

**發展方向**：
- 使用機器學習模型識別相同用戶的不同標識符
- 實現用戶行為相似性分析
- 應用圖神經網路 (GNNs) 學習節點表示

#### 3.2 實時處理能力

**技術路線**：
- 從批處理模式轉向實時或近實時處理
- 使用流處理技術（如 Dataflow）
- 提供實時用戶身份統一 API

#### 3.3 隱私合規增強

**合規要求**：
- 實現資料匿名化機制
- 支持資料刪除和遺忘權
- 增強資料安全性和訪問控制

### 4. 未來問題與挑戰

#### 4.1 擴展性挑戰

**潛在問題**：
- 新增其他廣告渠道的追蹤 ID
- 資料量過大時的效能問題
- permanent 可能需要退場機制

**解決方案**：
- 實施資料分片處理
- 建立資料生命週期管理
- 最佳化 BigQuery 查詢效能

#### 4.2 測試與驗證

**待實作項目**：
- 建立測試用的 BigQuery dataset
- 開發 Python Script 驗證 SQL 結果
- 實施自動化測試流程

### 5. 改進優先級建議

| 改進項目 | 優先級 | 預期效果 | 實施難度 | 時間框架 |
|---------|--------|---------|---------|---------|
| 信心分數系統 | 高 | 提升匹配準確度 20% | 中 | 1 個月 |
| SQL 查詢最佳化 | 高 | 減少處理時間 30% | 低 | 2 週 |
| 屬性驗證增強 | 中 | 提升資料品質 15% | 低 | 3 週 |
| 智能代表選擇 | 中 | 提升群組穩定性 | 中 | 2 個月 |
| 模糊匹配 | 低 | 提升匹配率 10% | 高 | 3 個月 |
| 機器學習增強 | 低 | 長期競爭優勢 | 高 | 6 個月 |

---

**報告完成日期：** 2025年9月10日
**下次複查建議：** 2025年12月10日
**負責團隊：** User Unify 開發團隊
**批准狀態：** 待審核
