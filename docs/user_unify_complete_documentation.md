# User Unify 系統完整文件

> **適合 Notion 或其他文件平台的完整版本**

**文件日期：** 2025年9月10日  
**系統版本：** V2.0  
**維護團隊：** Tagtoo User Unify 開發團隊  
**文件狀態：** 整合完整版

---

## 📖 目錄

1. [系統概述](#系統概述)
2. [核心功能](#核心功能)
3. [技術架構](#技術架構)
4. [核心演算法](#核心演算法)
5. [資料結構](#資料結構)
6. [BigQuery SQL 實作](#bigquery-sql-實作)
7. [操作指南](#操作指南)
8. [測試驗證](#測試驗證)
9. [監控維護](#監控維護)
10. [改進建議](#改進建議)

---

## 🎯 系統概述

### 什麼是 User Unify？

User Unify 是 Tagtoo 追蹤系統中負責**使用者身份統一和識別**的核心組件。系統透過分析使用者的多種標識符（email、手機號碼、permanent ID、GID 等），識別並關聯屬於同一使用者的不同身份，建立完整的使用者畫像。

### 為什麼需要 User Unify？

在現代數位行銷環境中，使用者會在多個裝置、平台和時間點與品牌互動：

- **多裝置使用**：手機、平板、電腦
- **多平台互動**：網站、APP、社群媒體
- **身份變化**：更換 email、手機號碼
- **隱私保護**：Safari ITP、廣告攔截器

User Unify 解決了這些挑戰，提供統一的使用者視圖。

### 系統特性

✅ **高效能大數據處理**：基於 BigQuery 的分散式運算  
✅ **智慧身份統一**：使用 Union Find 演算法處理複雜關聯  
✅ **強健的錯誤恢復**：完整的快照和回滾機制  
✅ **可擴展架構**：支援大規模使用者資料處理  
✅ **每日批次處理**：透過 Kubernetes cron job 自動執行

---

## 🔧 核心功能

### 1. 身份統一

**功能描述**：識別和關聯同一用戶的多個標識符

**支援的標識符**：
- **Email**：電子郵件地址
- **Phone**：手機號碼
- **Username**：使用者名稱
- **GID**：Google Analytics ID
- **Permanent ID**：基於 FingerPrintJS 的持久識別符

**統一邏輯**：
- 相同 email 的使用者 → 同一群組
- 相同 phone 的使用者 → 同一群組
- 相同 username 的使用者 → 同一群組
- 相同 GID 的使用者 → 同一群組

### 2. 用戶畫像建立

**功能描述**：建立完整的用戶身份視圖

**畫像內容**：
- 所有關聯的 permanent ID
- 所有使用過的 email 地址
- 所有使用過的手機號碼
- 所有使用過的使用者名稱
- 所有關聯的 GID

### 3. 查詢優化

**功能描述**：提供高效的用戶群組查詢

**查詢能力**：
- 根據任一標識符快速找到完整群組
- 支援批次查詢和單一查詢
- 提供群組統計和分析功能

### 4. 跨裝置追蹤

**功能描述**：透過共同標識符連結不同裝置的使用者行為

**追蹤機制**：
- 登入狀態下的 email 關聯
- 手機號碼驗證的關聯
- 跨裝置的 permanent ID 關聯

---

## 🏗️ 技術架構

### 系統架構圖

```
[Tagtoo Event 表] 
    ↓
[每日分組結果生成]
    ↓
[新實體合併]
    ↓
[候選群組預測]
    ↓
[Union Find 演算法]
    ↓
[中間結果表]
    ↓
[解析候選群組合併]
    ↓
[無用群組清理]
    ↓
[user_unify_groups 表]
    ↓
[user_unify_group_permanent 表]
```

### 技術棧

**核心技術**：
- **Python 3.8**：主要程式語言
- **Google BigQuery**：大數據處理和儲存
- **Docker**：容器化部署
- **Kubernetes**：容器編排和 cron job
- **Google Cloud Platform**：雲端基礎設施

**第三方套件**：
- `google-cloud-bigquery`：BigQuery 客戶端
- `environs`：環境變數管理
- `pytz`：時區處理

### 資料流程

**每日處理流程**：

1. **創建快照**：執行操作前創建 user_unify_groups 表的快照
2. **獲取每日分組結果**：從 tagtoo_event 表中提取當天的用戶數據並分組
3. **合併新實體**：將每日分組結果合併到 user_unify_groups 表中
4. **預測候選群組**：找出每個 permanent 可能屬於的 group_id 群組
5. **解析多候選群組**：使用 Union Find 算法處理一個 permanent 屬於多個 group_id 的情況
6. **結果寫入**：將處理結果寫入相關表
7. **錯誤處理**：提供錯誤處理和回滾機制

---

## 🧮 核心演算法

### Union Find 演算法

#### 演算法簡介

Union Find 演算法主要用來處理一些不相交集合的合併及查詢，簡單來說就是對節點做分群，並提供查詢每個節點隸屬哪個群組。

#### 主要操作

**Find(x) 操作**：
- 找出 x 隸屬的群組（根節點）
- 每次在做 Find(x) 時，對走訪的路徑進行壓縮
- 這樣下次路徑上的節點有被查詢時，查詢速度就會變快

**Union(x, y) 操作**：
- 將 x 跟 y 所在的群組做合併
- 找出 x 跟 y 所屬樹的根節點後，依據自定義的規則，將一邊的根節點指向另一方

#### User Unify 實際應用

在 User Unify 系統中，從 BigQuery 得到的結果類似：

```python
predict_result = [
    {"permanent":"P1","group_ids":["G1","G2"]},
    {"permanent":"P2","group_ids":["G1","G3", "G30"]},
    {"permanent":"P3","group_ids":["G3","G4"]},
    {"permanent":"P5","group_ids":["G5","G7"]},
    {"permanent":"P6","group_ids":["G6","G7"]},
]
```

**處理邏輯**：
- `permanent` 作為導致群組合併的樞紐
- 每個 `group_ids` 視為一顆樹，`group_ids[0]` 預設為根節點
- 其餘 `group_ids[1:]` 都視為子節點，每個子節點指向根節點時，同時做 Union()
- Union() 合併的規則為字典序較小者當新的根節點

### 每日分組結果生成演算法

#### 實體鄰居查找

使用 `get_entity_neighbors_by_date()` 函數：
- 基於相同屬性（email、phone、username、gid）找出具有關聯的 permanent
- 使用 self join 找出具有相同屬性的 permanent，定為 neighbors

#### 群組結果生成

使用 `get_grouping_result_by_date()` 函數：
- 先將鄰居資料整理，以 entity_time 作為排序基礎
- 第二次聚合，將相同 group id 的聚合在一起
- 以時間最早的 permanent 作為 group id

---

## 🗄️ 資料結構

### 核心資料表

#### user_unify_groups 表

| 欄位名 | 類型 | 描述 |
|--------|------|------|
| group_id | STRING | 群組唯一標識符 |
| group_time | TIMESTAMP | 群組創建時間 |
| permanents | ARRAY<STRING> | 屬於該群組的所有 permanent ID |
| emails | ARRAY<STRING> | 該群組的所有電子郵件地址 |
| phones | ARRAY<STRING> | 該群組的所有電話號碼 |
| usernames | ARRAY<STRING> | 該群組的所有使用者名稱 |
| gids | ARRAY<STRING> | 該群組的所有 GID |

#### user_unify_group_permanent 表

| 欄位名 | 類型 | 描述 |
|--------|------|------|
| permanent | STRING | Permanent ID |
| group_id | STRING | 所屬群組 ID |

### 資料表關聯

```
tagtoo_event 
    ↓ (generates)
daily_grouping_result 
    ↓ (merges_into)
user_unify_groups 
    ↓ (creates)
user_unify_group_permanent
    ↓ (backs_up)
snapshot_tables
```

---

## 💾 BigQuery SQL 實作

### 核心 SQL 檔案

#### 1. merge_new_entities.sql

**功能**：將每日分組結果合併到持續維護的 Group Result

**主要邏輯**：
- 抓出指定日期的 Grouping result 作為 next_day_result
- 將 next_day_result 跟 groups_table 相同 group id 的欄位合併
- 使用 IFNULL 處理 ARRAY_CONCAT() 遇到 NULL 的情況
- 對合併結果做 DISTINCT，保留唯一值

#### 2. predict_candidate.sql

**功能**：找出每個 permanent 可能隸屬的候選群組

**主要邏輯**：
- 將 groups_table 的各個屬性展開（flatten）
- 使用每日的 entity_data 去跟各個屬性做比對
- 聚合找出每個 permanent 可以讓多少個群組合併
- 只提取候選 group_id 超過一個以上的 permanent

#### 3. merge_resolved_candidates.sql

**功能**：合併 Union Find 算法處理後的結果

**主要邏輯**：
- 取出 Union Find 的結果（group_id、predict_result）
- 將相同 predict_result 的群組做聚合
- 對聚合後的各個屬性做 DISTINCT
- 只針對 predict_result 做更新處理

#### 4. delete_useless_group.sql

**功能**：刪除合併後不再需要的群組

**主要邏輯**：
```sql
DELETE `$groups_table`
WHERE group_id IN (
    SELECT group_id FROM `$intermediate_table` 
    WHERE group_id != predict_result
);
```

### Table Functions

#### get_entity_by_date()

**功能**：從 tagtoo_event 表產出 Daily Entity Data

**參數**：
- start_date：STRING
- end_date：STRING

**主要特點**：
- 將 entity_time 設為最早的訪問時間
- 排除不合規格的 gid（使用正規表達式驗證）
- 目前已移除 fbp 欄位（基於效能考量）

#### get_entity_neighbors_by_date()

**功能**：找出具有相同屬性的 permanent neighbors

**主要邏輯**：
- 基於 get_entity_by_date() 的結果
- 使用 self join 找出具有相同屬性的 permanent
- 支援 email、phone、username、gid 的匹配

#### get_grouping_result_by_date()

**功能**：產出最終的 Daily Group Result

**主要邏輯**：
- 基於 get_entity_neighbors_by_date() 的結果
- 以時間最早的 permanent 作為 group id
- 進行兩次聚合：第一次按 permanent，第二次按 group_id

---

## 🛠️ 操作指南

### 系統部署

#### Docker 環境

User Unify V2 使用 Kubernetes cron job 來處理，相關設定可參考 `user_unify.tf`。

**本機手動執行流程**：

1. **啟動 User Unify 環境**
```bash
docker compose -f docker-compose.user_unify.yml up
```

2. **連上該 container**
```bash
docker exec -it user-unify /bin/bash
```

3. **執行 update_grouping_result**
```bash
python update_grouping_result.py -s start_date -e end_date
```

4. **關閉 container**
```bash
docker compose -f docker-compose.user_unify.yml down
```

#### 命令列參數

**update_grouping_result.py 參數**：

| 參數 | 描述 | 預設值 |
|------|------|--------|
| -s, --start-date | 處理開始日期 | 昨天 |
| -e, --end-date | 處理結束日期 | 昨天 |
| -r, --recalc-daily-grouping-result | 強制重新計算每日分組結果 | False |

**執行範例**：
```bash
python update_grouping_result.py -s 2025-09-01 -e 2025-09-10 -r
```

### 快照和回滾

#### 快照機制

**快照創建**：
- 每次處理前自動創建 user_unify_groups 表的快照
- 快照表名格式：`user_unify_groups_YYYY_MM_DD`
- 快照保留期限：30 天

**快照用途**：
- 錯誤情況下的資料恢復
- 歷史資料比較和分析
- 系統測試和驗證

#### 回滾操作

**自動回滾**：
```sql
CREATE OR REPLACE TABLE `$groups_table`
CLONE `$snapshot_table`;
```

**手動回滾**：
1. 確認快照表存在
2. 執行回滾 SQL
3. 驗證資料完整性
4. 重新執行處理流程

---

## 🧪 測試驗證

### 建立 Ground Truth 資料集

#### 手動標記樣本

**實施步驟**：
- 從現有資料中隨機抽取 100-200 個群組
- 由業務專家手動審核這些群組
- 記錄審核結果，建立高質量的 ground truth 資料集

**實現方式**：
- 開發簡單的標記工具
- 整合審核結果，建立基準資料集

#### 使用已知關聯的用戶資料

**資料來源**：
- 用戶登錄後關聯的多個設備或會話
- CRM 系統中已確認的用戶身份關聯資料

**實現方式**：
- 建立資料導入流程
- 設計驗證腳本，比較系統結果與確定性資料

### 驗證方法

#### A/B 測試框架

**目的**：比較不同算法版本的效果

**評估指標**：
- 群組數量變化（合併效率）
- 群組大小分布變化
- 群組屬性豐富度變化

#### 歷史資料回測

**實施步驟**：
- 使用改進後的算法重新處理歷史資料
- 比較新舊結果的差異
- 關注被合併的群組數量和特徵

### 測試指標

#### 群組一致性指標

**定義**：同一群組內用戶屬性的一致性程度

**計算方法**：
```
一致性得分 = 共享屬性數量 / 總屬性數量
```

#### 群組穩定性指標

**定義**：群組結構在連續處理中的穩定程度

**計算方法**：
```
穩定性得分 = 保持不變的群組數量 / 總群組數量
```

#### 合併效率指標

**定義**：系統合併相關用戶的效率

**計算方法**：
```
合併效率 = 合併後的群組數量 / 合併前的群組數量
```

---

## 📊 監控維護

### 關鍵監控指標

| 指標類型 | 監控項目 | 正常範圍 | 告警條件 |
|---------|---------|---------|---------|
| 處理時間 | 每日處理完成時間 | < 4 小時 | > 6 小時 |
| 資料量 | 每日處理記錄數 | 依業務量而定 | 異常波動 >50% |
| 錯誤率 | 處理失敗率 | < 1% | > 5% |
| 資源使用 | BigQuery 查詢成本 | 監控預算 | 超出預算 20% |

### 錯誤處理機制

**自動回滾**：
- 處理失敗時自動回滾到快照狀態
- 保證資料一致性和系統穩定性

**錯誤報告**：
- 整合 Google Cloud Error Reporting
- 自動收集和分析錯誤資訊

### 日誌記錄

**日誌級別**：
- DEBUG：詳細的調試資訊
- INFO：一般處理資訊
- WARNING：警告訊息
- ERROR：錯誤訊息
- CRITICAL：嚴重錯誤

**日誌格式**：
```
[時間戳記 級別] 訊息內容
```

---

## 🚀 改進建議

### 短期改進（1-3 個月）

#### 信心分數系統

**目標**：為不同匹配類型賦予權重

**實施方案**：
- 電子郵件匹配權重：1.0
- 手機號碼匹配權重：1.0  
- 用戶名匹配權重：0.7
- GID 匹配權重：0.5
- 多個識別碼同時匹配的權重遠高於單一識別碼匹配

#### 屬性驗證增強

**改進項目**：
- 增強對電子郵件、手機號碼和用戶名的驗證和標準化
- 過濾臨時郵箱域名和無效格式的資料
- 排除不合規格的 GID

#### SQL 查詢最佳化

**最佳化方向**：
- 重構關鍵 SQL 查詢，提高效率
- 減少不必要的資料掃描
- 使用分區表設計提升查詢效能

### 中期改進（3-6 個月）

#### 智能 Union-Find 代表選擇

**改進策略**：
- 考慮群組創建時間（group_time）
- 群組豐富度（包含更多 permanent ID 或更多種類識別碼的群組）
- 群組活躍度（如果能追蹤最近活躍時間）

#### 模糊匹配與識別碼生命週期

**技術實現**：
- 對 email、username 引入字串相似度算法（如 Levenshtein 距離）
- 追蹤識別碼的首次出現時間和最後活躍時間
- 評估識別碼的有效性

### 長期發展（6-12 個月）

#### 機器學習增強

**發展方向**：
- 使用機器學習模型識別相同用戶的不同標識符
- 實現用戶行為相似性分析
- 應用圖神經網路 (GNNs) 學習節點表示

#### 實時處理能力

**技術路線**：
- 從批處理模式轉向實時或近實時處理
- 使用流處理技術（如 Dataflow）
- 提供實時用戶身份統一 API

#### 隱私合規增強

**合規要求**：
- 實現資料匿名化機制
- 支持資料刪除和遺忘權
- 增強資料安全性和訪問控制

### 改進優先級建議

| 改進項目 | 優先級 | 預期效果 | 實施難度 | 時間框架 |
|---------|--------|---------|---------|---------|
| 信心分數系統 | 高 | 提升匹配準確度 20% | 中 | 1 個月 |
| SQL 查詢最佳化 | 高 | 減少處理時間 30% | 低 | 2 週 |
| 屬性驗證增強 | 中 | 提升資料品質 15% | 低 | 3 週 |
| 智能代表選擇 | 中 | 提升群組穩定性 | 中 | 2 個月 |
| 模糊匹配 | 低 | 提升匹配率 10% | 高 | 3 個月 |
| 機器學習增強 | 低 | 長期競爭優勢 | 高 | 6 個月 |

---

## 📝 結論

User Unify V2 是一個成熟且強健的使用者身份統一系統，具備以下優勢：

✅ **技術架構完善**：基於 BigQuery 的高效能大數據處理  
✅ **演算法設計優秀**：Union Find 演算法確保群組關聯正確性  
✅ **錯誤處理完整**：快照回滾機制保證資料一致性  
✅ **可擴展性強**：支援 PB 級資料處理  

同時也存在改進空間：

⚠️ **處理延遲**：每日批次處理，非即時更新  
⚠️ **成本考量**：BigQuery 查詢成本隨資料量增長  
⚠️ **複雜性**：系統邏輯複雜，維護成本較高  

建議優先實施**信心分數系統**和**SQL 查詢最佳化**，這將顯著提升系統效能和準確性，為後續的機器學習增強和實時處理能力奠定基礎。

---

**文件完成日期：** 2025年9月10日  
**下次複查建議：** 2025年12月10日  
**負責團隊：** User Unify 開發團隊  
**批准狀態：** 待審核
