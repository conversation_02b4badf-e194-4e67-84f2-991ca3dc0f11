<script src="https://unpkg.com/mermaid@8.13.3/dist/mermaid.min.js"></script>

Since the raw event data is stores in BigQuery, the user unify operations are rewrite-able. The rewrite subscription
is separated from streaming subscription in order to apply different subscribe strategies for them.

```mermaid
flowchart LR
    A[Subsctiption] --> B([Dataflow])
    B --> C[Deduplicated Entity]
    C --> D([Streaming Subscriber])
    D --> E[(BigTable)]
    
    F[(BigQuery)] --query/dump--> G[Rewrite Topic]
    G --> H([Rewrite Subscriber])
    H --> E
```

!!!warning
    When rewrite is processing, make sure the BigTable instance have enough utilization for both streaming and rewrite.
    If not, latency of the read/write could increase and cause side effects.

    Choose a reasonable subscriber replicas count or scale out the BigTable instances to buy more throughputs.

### Rewrite Entire Graph

When the schema has a major change or the operation logic is totally modified, we might want to rewrite the whole graph/database.

1. Stop the deduplication Dataflow.
2. Wait until the messages in deduplicated subscription are all consumed.
3. Stop streaming subscribers by scale in to zero instance.
4. Backup the graph.
5. Flush the graph.

    ```shell
    cbt deleteallrows entity && \
        cbt deleteallrows permanent && \
        cbt deleteallrows email && \
        cbt deleteallrows mobile && \
        cbt deleteallrows username && \
        cbt deleteallrows gid && \
        cbt deleteallrows fbp
    ```

6. Update subscriber Deployments with new code.
7. Query the rewrite data from BigQuery and save the result to a temp table.

    ```sql
    SELECT 
        DISTINCT permanent,
        ec_id,
        user.em as email,
        user.ph as mobile,
        user.un as username,
        user.fbp as fbp,
        user.gid as gid 
    FROM `tagtoo-tracking.event_prod.tagtoo_event`
    ```

8. Transfer the temp table to a GCS folder under `tagtoo-event-bigquery-export/user-unify` in wildcard `*.csv` format.
9. Load CSV at local machine, transfer into Pub/Sub message and publish to the rewrite topic.

    ```python
    from dask import dataframe
    df = dataframe.read_csv('gs://tagtoo-event-bigquery-export/user_unify/<SubFolder>/*.csv', dtype=str).compute(scheduler="processes")
    ```

10. Flush the backlogs in Subscription which fan-out from Tagtoo event topic.
11. Start streaming: Start the deduplication Dataflow and streaming subscribers.
12. Start the rewrite subscribers.
13. Stop the rewrite subscribers when rewrite is done. 

### Partial Rewrite

!!! warning
    In order to recover from the backup, the database need to be backed-up every 30 days since there's an [Automatic expiration](https://cloud.google.com/bigtable/docs/backups#key-features).

When a BAD version of code is deployed and write wrong data to graph, we need rollback the graph to previous status, 
and rewrite the data after that version.

1. Stop the deduplication Dataflow.
2. Stop streaming subscribers by scale in to zero instance.
3. Flush the backlogs in deduplicated subscription.
4. Flush and restore the graph from backup.
5. Update subscriber Deployments with new code.
6. Prepare the rewrite data and publish them to rewrite topic.
7. Flush the backlogs in Subscription which fan-out from Tagtoo event topic.
8. Start streaming: Start the deduplication Dataflow and streaming subscribers.
9. Start the rewrite subscribers.
10. Stop the rewrite subscribers when rewrite is done.