from environs import Env

env = Env()


GCP_PROJECT = env.str('GCP_PROJECT', 'tagtoo-tracking')

BQ_DATASET = env.str('BQ_DATASET')

BQ_GROUPING_RESULT_DATASET = env.str('BQ_GROUPING_RESULT_DATASET')

BQ_SNAPSHOT_DATASET = env.str('BQ_SNAPSHOT_DATASET')

BQ_TABLE_USER_UNIFY_GROUPS = env.str('BQ_TABLE_USER_UNIFY_GROUPS')

BQ_TABLE_USER_UNIFY_GROUP_PERMANENT = env.str('BQ_TABLE_USER_UNIFY_GROUP_PERMANENT')

BQ_TABLE_PREDICT_INTERMEDIATE = env.str('BQ_TABLE_PREDICT_INTERMEDIATE')

BQ_TABLE_FUNCTION_GET_ENTITY = env.str('BQ_TABLE_FUNCTION_GET_ENTITY')

BQ_TABLE_FUNCTION_GET_GROUPING_RESULT = env.str('BQ_TABLE_FUNCTION_GET_GROUPING_RESULT')
