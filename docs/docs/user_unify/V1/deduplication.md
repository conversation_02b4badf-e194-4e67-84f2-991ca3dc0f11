In user unify, we only care about the user entity data of events. 

The entity data is streamed from the event-base dataflow, thus an entity data need to be extracted from an event data and de-duplicated 
by a specific window - which is currently set to 5 minute.

If window size is small, it means there are more duplicated entities across different windows and need more subscriber to
consume. On the other hand, if extending the window size, please make sure there is enough memory space in Dataflow for a window 
to fixed in.

This window size is also concerned with the consuming capacity of the streaming subscriber, please make sure there's no backlogs
in the streaming subscription.

??? Reference "dataflow/fb_capi_compressor.py"
    ```python hl_lines="108 110 112"
    --8<-- "../dataflow/user_deduplicator.py"
    ```