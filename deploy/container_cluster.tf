# https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/container_cluster
resource "google_container_cluster" "event" {
  name       = "event-${var.environment}"
  location   = "asia-east1-a"
  network    = google_compute_network.event.name
  subnetwork = google_compute_subnetwork.asia_east1.name
  # We can't create a cluster with no node pool defined, but we want to only use
  # separately managed node pools. So we create the smallest possible default
  # node pool and immediately delete it.
  remove_default_node_pool = true
  initial_node_count       = 1
  vertical_pod_autoscaling {
    enabled = true
  }
  resource_labels = {
    application = "event"
    environment = var.environment
  }

  datapath_provider        = "ADVANCED_DATAPATH"
  enable_l4_ilb_subsetting = true

  # Adding this block enables IP aliasing
  ip_allocation_policy {
    cluster_ipv4_cidr_block  = "***********/20"
    services_ipv4_cidr_block = "************/20"
    stack_type               = "IPV4_IPV6"
  }

  private_cluster_config {
    enable_private_nodes    = true
    enable_private_endpoint = false
    master_ipv4_cidr_block  = "**********/28"
  }

  master_authorized_networks_config {
    cidr_blocks {
      display_name = "all"
      cidr_block   = "0.0.0.0/0"
    }
  }

  master_auth {
    client_certificate_config {
      issue_client_certificate = false
    }
  }
}

resource "google_container_node_pool" "event_kubernetes_pool" {
  name     = "kubernetes-${var.environment}"
  location = "asia-east1-a"
  cluster  = google_container_cluster.event.name

  node_config {
    machine_type = "e2-highcpu-2"
    preemptible  = var.container_cluster_event.kubernetes_pool.preemptible
    metadata = {
      disable-legacy-endpoints = "true"
    }
    disk_size_gb    = 20
    service_account = var.container_cluster_event.kubernetes_pool.service_account
    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
    labels = {
      application = "event"
      name        = "kubernetes"
      environment = var.environment
    }
  }
  autoscaling {
    min_node_count  = 1
    max_node_count  = 5
    location_policy = "BALANCED"
  }
}

resource "google_container_node_pool" "event_api_pool" {
  name              = "api-${var.environment}"
  location          = "asia-east1-a"
  cluster           = google_container_cluster.event.name
  max_pods_per_node = 20

  node_config {
    machine_type = var.container_cluster_event.api_pool.machine_type
    preemptible  = var.container_cluster_event.api_pool.preemptible
    metadata = {
      disable-legacy-endpoints = "true"
    }
    disk_size_gb = 20
    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
    service_account = var.container_cluster_event.api_pool.service_account
    labels = {
      application = "event"
      name        = "api"
      environment = var.environment
    }
    taint {
      key    = "name"
      value  = "api"
      effect = "NO_SCHEDULE"
    }

  }
  autoscaling {
    min_node_count  = 3
    max_node_count  = 10
    location_policy = "BALANCED"
  }
}

resource "google_container_node_pool" "event_s2s_system_pool" {
  name              = "s2s-system-${var.environment}"
  location          = "asia-east1-a"
  cluster           = google_container_cluster.event.name
  max_pods_per_node = 20

  node_config {
    machine_type = var.container_cluster_event.s2s_system_pool.machine_type
    preemptible  = var.container_cluster_event.s2s_system_pool.preemptible
    metadata = {
      disable-legacy-endpoints = "true"
    }
    disk_size_gb = 20
    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
    service_account = var.container_cluster_event.s2s_system_pool.service_account
    labels = {
      application = "event"
      name        = "s2s-system"
      environment = var.environment
    }
    taint {
      key    = "name"
      value  = "s2s-system"
      effect = "NO_SCHEDULE"
    }

  }
  autoscaling {
    min_node_count  = 1
    max_node_count  = 3
    location_policy = "BALANCED"
  }
}

resource "google_container_node_pool" "event_s2s_lta_subscriber_pool" {
  name              = "s2s-lta-subscriber-${var.environment}"
  location          = "asia-east1-a"
  cluster           = google_container_cluster.event.name
  max_pods_per_node = 20

  node_config {
    machine_type = var.container_cluster_event.s2s_lta_subscriber_pool.machine_type
    preemptible  = var.container_cluster_event.s2s_lta_subscriber_pool.preemptible
    metadata = {
      disable-legacy-endpoints = "true"
    }
    disk_size_gb = 20
    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
    service_account = var.container_cluster_event.s2s_lta_subscriber_pool.service_account
    labels = {
      application = "event"
      name        = "s2s-lta-subscriber"
      environment = var.environment
    }
    taint {
      key    = "name"
      value  = "s2s-lta-subscriber"
      effect = "NO_SCHEDULE"
    }
  }
  autoscaling {
    min_node_count  = 1
    max_node_count  = 8
    location_policy = "BALANCED"
  }
}
resource "google_container_node_pool" "event_s2s_subscriber_pool" {
  name              = "s2s-subscriber-${var.environment}"
  location          = "asia-east1-a"
  cluster           = google_container_cluster.event.name
  max_pods_per_node = 20

  node_config {
    machine_type = var.container_cluster_event.s2s_subscriber_pool.machine_type
    preemptible  = var.container_cluster_event.s2s_subscriber_pool.preemptible
    metadata = {
      disable-legacy-endpoints = "true"
    }
    disk_size_gb = 20
    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
    service_account = var.container_cluster_event.s2s_subscriber_pool.service_account
    labels = {
      application = "event"
      name        = "s2s-subscriber"
      environment = var.environment
    }
    taint {
      key    = "name"
      value  = "s2s-subscriber"
      effect = "NO_SCHEDULE"
    }

  }
  autoscaling {
    min_node_count  = 1
    max_node_count  = 8
    location_policy = "BALANCED"
  }
}

resource "google_container_node_pool" "event_config_api_pool" {
  name     = "config-api-${var.environment}"
  location = "asia-east1-a"
  cluster  = google_container_cluster.event.name

  node_config {
    machine_type = var.container_cluster_event.config_api_pool.machine_type
    preemptible  = var.container_cluster_event.config_api_pool.preemptible
    metadata = {
      disable-legacy-endpoints = "true"
    }
    disk_size_gb = 20
    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
    service_account = var.container_cluster_event.config_api_pool.service_account
    labels = {
      application = "event"
      name        = "config-api"
      environment = var.environment
    }
    taint {
      key    = "name"
      value  = "config-api"
      effect = "NO_SCHEDULE"
    }
  }
  autoscaling {
    min_node_count  = 1
    max_node_count  = 8
    location_policy = "BALANCED"
  }
}

resource "google_container_node_pool" "bq_writer_subscriber_pool" {
  name              = "bq-writer-${var.environment}"
  location          = "asia-east1-a"
  cluster           = google_container_cluster.event.name
  max_pods_per_node = 10

  node_config {
    machine_type = var.container_cluster_event.bq_writer_subscriber_pool.machine_type
    preemptible  = var.container_cluster_event.bq_writer_subscriber_pool.preemptible
    metadata = {
      disable-legacy-endpoints = "true"
    }
    disk_size_gb = 30
    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
    service_account = var.container_cluster_event.bq_writer_subscriber_pool.service_account
    labels = {
      application = "event"
      name        = "bq-writer-subscriber"
      environment = var.environment
    }
    taint {
      key    = "name"
      value  = "bq-writer-subscriber"
      effect = "NO_SCHEDULE"
    }

  }
  autoscaling {
    min_node_count  = 1
    max_node_count  = 40
    location_policy = "BALANCED"
  }
}

resource "google_container_node_pool" "user_unify_pool" {
  name     = "user-unify-${var.environment}"
  location = "asia-east1-a"
  cluster  = google_container_cluster.event.name
  # max_pods_per_node = 16

  node_config {
    machine_type = var.container_cluster_event.user_unify_pool.machine_type
    preemptible  = var.container_cluster_event.user_unify_pool.preemptible
    metadata = {
      disable-legacy-endpoints = "true"
    }
    disk_size_gb = 20
    oauth_scopes = [
      "https://www.googleapis.com/auth/cloud-platform"
    ]
    service_account = var.container_cluster_event.user_unify_pool.service_account
    labels = {
      application = "event"
      name        = "user-unify-job"
      environment = var.environment
    }
    taint {
      key    = "name"
      value  = "user-unify-job"
      effect = "NO_SCHEDULE"
    }
  }
  autoscaling {
    min_node_count  = 1
    max_node_count  = 3
    location_policy = "BALANCED"
  }
}
