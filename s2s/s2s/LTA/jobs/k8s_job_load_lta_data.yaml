apiVersion: batch/v1
kind: Job
metadata:
  name: "NONE"
  labels:
    application: "event"
    job-type: "load-lta-data"
spec:
  template:
    metadata:
      labels:
        job-type: "load-lta-data"
    spec:
      nodeSelector:
        application: "event"
        name: "s2s-lta-subscriber"
      tolerations:
      - effect: NoSchedule
        key: name
        operator: Equal
        value: s2s-lta-subscriber
      # 添加 Pod 反親和性，鼓勵將 job 分散到不同節點
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: job-type
                  operator: In
                  values:
                  - load-lta-data
              topologyKey: "kubernetes.io/hostname"
      # 添加拓撲分佈約束，確保 job 均勻分佈
      # 添加優先級
      priorityClassName: lta-job-priority
      topologySpreadConstraints:
      - maxSkew: 1
        topologyKey: kubernetes.io/hostname
        whenUnsatisfiable: ScheduleAnyway
        labelSelector:
          matchLabels:
            job-type: load-lta-data
      containers:
      - name: loader
        image: none
        args: []
        resources:
          requests:
            memory: 0.5Gi
            cpu: 600m  # 從 1000m 降低到 600m
          limits:
            memory: 1Gi
            cpu: "1"  # 從 1.5 降低到 1
        envFrom:
          - configMapRef:
              name: s2s-env
      restartPolicy: Never
  backoffLimit: 5
  ttlSecondsAfterFinished: 86400
