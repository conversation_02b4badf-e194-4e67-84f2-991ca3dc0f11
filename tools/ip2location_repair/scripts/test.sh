#!/bin/bash

# 檢查 GCP 憑證
if [ -z "$GOOGLE_APPLICATION_CREDENTIALS" ]; then
  echo "警告: GOOGLE_APPLICATION_CREDENTIALS 環境變數未設定"
  echo "請設定這個環境變數指向你的 GCP 憑證檔案:"
  echo "export GOOGLE_APPLICATION_CREDENTIALS=/路徑/到/你的/憑證.json"
  exit 1
fi

if [ ! -f "$GOOGLE_APPLICATION_CREDENTIALS" ]; then
  echo "錯誤: GCP 憑證檔案 $GOOGLE_APPLICATION_CREDENTIALS 不存在"
  exit 1
fi

# 檢查工作目錄
if [ ! -d "./mysql-conf" ] || [ ! -f "./test_connection.py" ]; then
  echo "錯誤: 找不到必要的文件"
  echo "請確保在 tools/ip2location_repair 目錄下執行此腳本"
  exit 1
fi

echo "使用 GCP 憑證: $GOOGLE_APPLICATION_CREDENTIALS"

# 停止舊的容器（如果存在）
docker-compose down || true

# 構建和啟動測試環境
echo "正在啟動測試環境..."
docker-compose up --build ip2location_test

# 檢查測試結果
TEST_EXIT_CODE=$?
if [ $TEST_EXIT_CODE -eq 0 ]; then
  echo "測試成功! 環境已經準備好可以執行完整的修復程序。"
  echo "執行以下命令開始修復:"
  echo "docker-compose up ip2location_fix"
else
  echo "測試失敗! 請檢查上面的錯誤訊息，解決問題後再繼續。"
fi
