#!/usr/bin/env bash

set -o errexit
set -o pipefail

cmd="$@"

function redis_ready() {
python << END
import sys
import asyncio
import traceback
from app.config import settings
from app.utils import update_event_configs, get_db

async def check_redis_ready():
  try:
    r = await get_db()
    await r.ping()
    await r.close()
  except Exception as exc:
    traceback.print_exception(*sys.exc_info())
    print('[Readiness] Failed to establish redis connection')
    sys.exit(-1)

  try:
    await update_event_configs()
  except Exception as exc:
    traceback.print_exception(*sys.exc_info())
    print('[Readiness] Failed to update event configs')
    sys.exit(-1)

  sys.exit(0)

asyncio.run(check_redis_ready())
END
}

function pubsub_emulator_ready() {
  curl "$PUBSUB_EMULATOR_HOST" || exit 1
}

until redis_ready; do
  >&2 echo "Redis is unavailable - sleeping"
  sleep 1
done

>&2 echo "Redis is up - continuing..."

if [[ -n "$PUBSUB_EMULATOR_HOST" ]]; then

  echo "Run app against the Pub/Sub emulator"

  until pubsub_emulator_ready; do
    >&2 echo "Pub/Sub emulator is unavailable - sleeping"
    sleep 1
  done

  >&2 echo "Pub/Sub emulator is up - continuing..."

fi

exec $cmd