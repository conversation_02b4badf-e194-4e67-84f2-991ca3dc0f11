-- Merge result of candidate > 1
MERGE `$groups_table` T USING (
    WITH Group_attrs AS (
        SELECT
            *
        FROM
            `$intermediate_table` PRE
            LEFT OUTER JOIN `$groups_table` GR ON PRE.group_id = GR.group_id
    ),
    Attr_contact AS (
        SELECT
            predict_result,
            ARRAY_CONCAT_AGG(permanents) permanents,
            ARRAY_CONCAT_AGG(emails) emails,
            ARRAY_CONCAT_AGG(phones) phones,
            ARRAY_CONCAT_AGG(usernames) usernames,
            -- ARRAY_CONCAT_AGG(fbps) fbps,
            ARRAY_CONCAT_AGG(gids) gids
        FROM
            Group_attrs
        GROUP BY
            1
    ),
    Distinct_res AS (
        SELECT
            predict_result group_id,
            ARRAY(
                SELECT
                    DISTINCT p
                FROM
                    Attr_contact.permanents p
            ) permanents,
            ARRAY(
                SELECT
                    DISTINCT em
                FROM
                    Attr_contact.emails em
            ) emails,
            ARRAY(
                SELECT
                    DISTINCT ph
                FROM
                    Attr_contact.phones ph
            ) phones,
            ARRAY(
                SELECT
                    DISTINCT un
                FROM
                    Attr_contact.usernames un
            ) usernames,
            -- <PERSON><PERSON><PERSON>(
            --     SELECT
            --         DISTINCT fbp
            --     FROM
            --         Attr_contact.fbps fbp
            -- ) fbps,
            ARRAY(
                SELECT
                    DISTINCT gid
                FROM
                    Attr_contact.gids gid
            ) gids,
        FROM
            Attr_contact
    )
    SELECT
        *
    FROM
        Distinct_res
) Q ON T.group_id = Q.group_id
WHEN MATCHED THEN
UPDATE
SET
    permanents = Q.permanents,
    emails = Q.emails,
    phones = Q.phones,
    usernames = Q.usernames,
    -- fbps = Q.fbps,
    gids = Q.gids;