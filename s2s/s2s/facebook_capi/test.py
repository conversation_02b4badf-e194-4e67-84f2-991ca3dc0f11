import json
import time
from unittest import TestCase
from .utils import post_facebook_capi
from .exceptions import FacebookRequestError


class CAPITestCase(TestCase):
    TEST_PIXEL_ID = '2169275333130765'
    TEST_PIXEL_TOKEN = 'EAAF2w0Wwk0YBABniYMgVXqRMh291bhK6hScmnfMvZAie1FcqdGoQFhOy0f8KnzWzW9UiFzzO8VjL3j7vNQDC9z2PZCKHXOej6jCjHrLwL7nJrHbOVubzKZAEDRI4sjvhDQTePXzko4uMmFmdaC5MSiGwliEkZCyqtSTInKaE2vFIPihuFNCd'

    def post(self, data):
        return post_facebook_capi(
            data=[data], pixel_id=self.TEST_PIXEL_ID, access_token=self.TEST_PIXEL_TOKEN
        )

    def test_success(self):
        """Post a real event."""
        data_str = """
            {
                "event_name":"PageView",
                "user_data":{
                  "external_id":[
                    "413e8821adeca47afe1eef292ad0db3a"
                  ],
                  "client_user_agent":"Mozilla/5.0 (iPad; CPU OS 14_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 [FBAN/FBIOS;FBAV/300.**********;FBBV/262375406;FBDV/iPad13,2;FBMD/iPad;FBSN/iOS;FBSV/14.2;FBSS/2;FBID/tablet;FBLC/en_GB;FBOP/5;FBRV/266463419]",
                  "fbp":"fb.0.1610114519920.958814089"
                },
                "event_source_url":"https://www.obdesign.com.tw/inpage.aspx?no=6991",
                "event_id":"1610114519951",
                "action_source":"website"
            }
        """

        data = json.loads(data_str)
        data.update({'event_time': int(time.time())})
        response = self.post(data)
        self.assertEqual(response.status_code, 200)

    def test_exception(self):
        """Test exception with invalid event_time."""
        data_str = """
            {
                "event_name":"PageView",
                "user_data":{
                  "external_id":[
                    "413e8821adeca47afe1eef292ad0db3a"
                  ],
                  "client_user_agent":"Mozilla/5.0 (iPad; CPU OS 14_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 [FBAN/FBIOS;FBAV/300.**********;FBBV/262375406;FBDV/iPad13,2;FBMD/iPad;FBSN/iOS;FBSV/14.2;FBSS/2;FBID/tablet;FBLC/en_GB;FBOP/5;FBRV/266463419]",
                  "fbp":"fb.0.1610114519920.958814089"
                },
                "event_source_url":"https://www.obdesign.com.tw/inpage.aspx?no=6991",
                "event_id":"1610114519951",
                "action_source":"website",
                "event_time":1610114519952
            }
        """

        data = json.loads(data_str)

        try:
            self.post(data)
        except FacebookRequestError as exc:
            self.assertEqual(exc.message, "Invalid parameter")
            self.assertEqual(exc.type, "OAuthException")
            self.assertEqual(exc.code, 100)
            self.assertEqual(exc.error_subcode, 2804004)
