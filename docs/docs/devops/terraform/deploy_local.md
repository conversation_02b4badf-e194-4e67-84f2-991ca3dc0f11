# Terraform 本地部署

## Installation and Setup

You don't need to install Terraform in your local machine, the environment is packed and pre-built using container image. Run this command to run the containers:

```shell
docker compose -f docker-compose.deploy.yml up -d
```

或者使用 Makefile 命令：

```shell
make deploy-up
```

然後，有兩種方式可以執行 Terraform 命令：

### 方式一：直接附加到容器

附加到容器：

```shell
make manage
```

然後，在容器中執行以下命令安裝依賴（首次登入時）：

```shell
terraform init
```

切換到 `prod` 工作空間：

```shell
terraform workspace select prod
```

最後，應用變更：

```shell
terraform apply -var-file=prod.tfvars
```

### 方式二：使用 Makefile 命令

我們提供了一系列 Makefile 命令，可以直接從主機執行 Terraform 操作：

```shell
# 初始化 Terraform
make terraform-init

# 查看計劃（使用 prod.tfvars）
make terraform-plan

# 應用變更（使用 prod.tfvars）
make terraform-apply

# 針對特定資源查看計劃
make terraform-plan-target TARGET=google_compute_disk.event_s2s_redis

# 針對特定資源應用變更
make terraform-apply-target TARGET=google_compute_disk.event_s2s_redis
```

無論使用哪種方式，在應用變更時，檢查所有變更然後輸入 `yes`，部署過程就會開始。

After the completion of deployment, remember to commit the code that updating image tags. The
header of commit message can be with the format as follows:

```shell
deploy(srv1, [srv2, ...]): Update image
```

!!! info
    Please note that the provider requirements for Terraform are persisted by a local docker volume, which means you don't
    need to run `terraform init` at every container start, which also means, if you need to delete the local volume,
    you must specify `-v` argument when brings down the containers.

    ```shell
    docker compose -f docker-compose.deploy.yml down -v
    ```

### Note

A ConnectionRefused error may occur when one modifies resources. This is because there are some resource
**already created** in the **Kubernetes Cluster** which need extra context file to be able to access. In this case,
simply run a make command to generate the context for cluster:

```shell
make config-cluster CLUSTER=<cluster-name>
```

## 停止容器

當您完成工作後，可以使用以下命令停止 deploy 容器：

```shell
docker compose -f docker-compose.deploy.yml down
```

或者使用 Makefile 命令：

```shell
make deploy-down
```

如果您想同時刪除本地 Docker 卷（這將移除 Terraform 的本地狀態和提供者緩存），請使用：

```shell
docker compose -f docker-compose.deploy.yml down -v
```
