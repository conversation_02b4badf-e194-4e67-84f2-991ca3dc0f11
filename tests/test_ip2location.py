#!/usr/bin/env python3
"""
測試 IP2Location 模組中的 is_internal_ip 函數。
比較使用 ipaddress 模組和原始實現的結果。
"""

import unittest
import ipaddress

# 原始實現
def is_internal_ip_original(ip: str) -> bool:
    """檢查 IP 是否為內部 IP 地址（原始實現）"""
    # 檢查 IPv4 私有地址範圍
    if ip.startswith(('10.', '172.16.', '172.17.', '172.18.', '172.19.', '172.20.', '172.21.', '172.22.',
                      '172.23.', '172.24.', '172.25.', '172.26.', '172.27.', '172.28.', '172.29.',
                      '172.30.', '172.31.', '192.168.')):
        return True

    # 檢查 IPv6 本地地址
    if ip.startswith(('fc', 'fd', 'fe80:', '::1')):
        return True

    # 檢查回環地址
    if ip == '127.0.0.1' or ip == '::1':
        return True

    return False

# 新實現
def is_internal_ip_new(ip: str) -> bool:
    """檢查 IP 是否為內部 IP 地址（使用 ipaddress 模組）"""
    try:
        ip_obj = ipaddress.ip_address(ip)
        # 檢查 IP 是否為私有地址或回環地址
        return ip_obj.is_private or ip_obj.is_loopback
    except ValueError:
        # 無效的 IP 地址
        return False

class TestIP2Location(unittest.TestCase):
    def test_internal_ipv4(self):
        """測試內部 IPv4 地址"""
        internal_ips = [
            '********',
            '**********',
            '**********',
            '**********',
            '**********',
            '**********',
            '**********',
            '**********',
            '**********',
            '**********',
            '**********',
            '**********',
            '**********',
            '**********',
            '**********',
            '**********',
            '**********',
            '***********',
            '127.0.0.1',  # 回環地址
        ]

        for ip in internal_ips:
            with self.subTest(ip=ip):
                self.assertTrue(is_internal_ip_original(ip), f"原始實現應該將 {ip} 識別為內部 IP")
                self.assertTrue(is_internal_ip_new(ip), f"新實現應該將 {ip} 識別為內部 IP")

    def test_internal_ipv6(self):
        """測試內部 IPv6 地址"""
        internal_ips = [
            'fc00::1',
            'fd00::1',
            'fe80::1',
            '::1',  # 回環地址
        ]

        for ip in internal_ips:
            with self.subTest(ip=ip):
                self.assertTrue(is_internal_ip_original(ip), f"原始實現應該將 {ip} 識別為內部 IP")
                self.assertTrue(is_internal_ip_new(ip), f"新實現應該將 {ip} 識別為內部 IP")

    def test_external_ipv4(self):
        """測試外部 IPv4 地址"""
        external_ips = [
            '*******',  # Google DNS
            '*******',  # Cloudflare DNS
            '**************',  # Cloudflare
            '**************',  # Google
            '************',  # Microsoft
        ]

        for ip in external_ips:
            with self.subTest(ip=ip):
                self.assertFalse(is_internal_ip_original(ip), f"原始實現應該將 {ip} 識別為外部 IP")
                self.assertFalse(is_internal_ip_new(ip), f"新實現應該將 {ip} 識別為外部 IP")

    def test_external_ipv6(self):
        """測試外部 IPv6 地址"""
        external_ips = [
            '2001:4860:4860::8888',  # Google DNS
            '2606:4700:4700::1111',  # Cloudflare DNS
            '2620:1ec:c11::200',  # Microsoft
        ]

        for ip in external_ips:
            with self.subTest(ip=ip):
                self.assertFalse(is_internal_ip_original(ip), f"原始實現應該將 {ip} 識別為外部 IP")
                self.assertFalse(is_internal_ip_new(ip), f"新實現應該將 {ip} 識別為外部 IP")

    def test_invalid_ip(self):
        """測試無效的 IP 地址"""
        invalid_ips = [
            'not an ip',
            '999.999.999.999',
            '2001:xyz::1',
        ]

        for ip in invalid_ips:
            with self.subTest(ip=ip):
                # 原始實現可能會將某些無效 IP 誤判為內部 IP
                # 新實現應該將所有無效 IP 識別為非內部 IP
                self.assertFalse(is_internal_ip_new(ip), f"新實現應該將無效 IP {ip} 識別為非內部 IP")

if __name__ == '__main__':
    unittest.main()
