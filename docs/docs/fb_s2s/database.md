## Data Source

Facebook S2S application is depends on Event Config API, which provides:

* Ecommerce / Target Pixels mapping data

## Storage Backend

Multiple subscribers shares same Redis instance as the config data backend.

## Data Types

### CAPI Pixels

Determine which Pixel to send to for each Ecommerce.

The mapping are stored with this format:

|Key|Value|
|---|---|
|Ecommerce ID|List of tuples (Pixel ID, Pixel Token) dumps into bytes|

## Refresh Frequency

The config data refresh process is executed by Kubernetes Cronjob.

|Data|Frequency|
|---|---|
|CAPI Pixel Data|every 15 minutes|