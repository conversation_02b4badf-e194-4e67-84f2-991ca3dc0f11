from django.db import models
from django.utils import timezone


class AccessTokenQuerySet(models.query.QuerySet):
    def delete(self, soft=True):
        if soft:
            return super().update(revoked=timezone.now())
        else:
            return super().delete()


class AccessTokenManager(models.Manager):
    def get_queryset(self):
        return AccessTokenQuerySet(self.model, using=self._db)
