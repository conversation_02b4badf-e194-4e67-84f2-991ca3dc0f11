from typing import List, Optional, Union

from pydantic import BaseModel, Field, HttpUrl, root_validator


class EventDataConfig(BaseModel):
    pass


class EventFacebookUserData(BaseModel):
    em: Optional[str] = Field(None, title='Email (Hashing needed)')
    ph: Optional[str] = Field(None, title='Phone (Hashing needed)')
    ge: Optional[str] = Field(None, title='Gender (Hashing needed)')
    db: Optional[str] = Field(None, title='Date of Birth (Hashing needed)')
    ln: Optional[str] = Field(None, title='Last Name (Hashing needed)')
    fn: Optional[str] = Field(None, title='First Name (Hashing needed)')
    ct: Optional[str] = Field(None, title='City (Hashing needed)')
    st: Optional[str] = Field(None, title='State (Hashing needed)')
    zp: Optional[str] = Field(None, title='Zip (Hashing needed)')
    country: Optional[str] = Field(None, title='country (Hashing needed)')
    external_id: Optional[Union[str, List[str]]] = Field(
        None, title='External ID (Hashing recommended)'
    )
    client_ip_address: Optional[str] = Field(None, title='Client IP address')
    client_user_agent: Optional[str] = Field(None, title='Client User agent')
    fbc: Optional[str] = Field(None, title='Click ID')
    fbp: Optional[str] = Field(None, title='Browser ID')
    subscription_id: Optional[str] = Field(None, title='Subscription ID')
    fb_login_id: Optional[int] = Field(None, title='Facebook Login ID')
    lead_id: Optional[int] = Field(None, title='Lead ID')

    class Config:
        title = 'Facebbok User Data'

    @root_validator(pre=True)
    def check_at_least_one_param(cls, values):
        if not any(values.values()):
            raise ValueError(
                'At least one parameter should be provided within user data'
            )
        return values


class EventFacebookCustomData(BaseModel):
    value: Optional[float] = Field(None, title='Value')
    currency: Optional[str] = Field(None, title='Currency')
    content_name: Optional[str] = Field(None, title='Content Name')
    content_category: Optional[str] = Field(None, title='Content Category')
    content_ids: Optional[List[str]] = Field(None, title='Content IDs')
    contents: Optional[List[dict]] = Field(None, title='Contents')
    content_type: Optional[str] = Field(None, title='Content Type')
    order_id: Optional[str] = Field(None, title='Order ID')
    predicted_ltv: Optional[float] = Field(None, title='Predicted Lifetime Value')
    num_items: Optional[str] = Field(None, title='Event Content Type')
    search_string: Optional[str] = Field(None, title='Search String')
    status: Optional[str] = Field(None, title='Status')
    delivery_category: Optional[str] = Field(None, title='Delivery Category')
    custom_properties: Optional[dict] = Field(None, title='Custom Properties')

    class Config:
        title = 'Facebbok Custom Data'


class FacebookEventData(BaseModel):
    # Required Fields
    # pixels: List[str] = Field(..., title='Facebook pixels')
    event_name: str = Field(..., title='Facebook Event Name')

    # Optional Fields
    user_data: Optional[EventFacebookUserData] = Field({}, title='User Data')
    custom_data: Optional[EventFacebookCustomData] = Field(None, title='Custom Data')
    event_source_url: Optional[HttpUrl] = Field(None, title='Event Source URL')
    event_id: Optional[str] = Field(None, title='Event ID')
    action_source: Optional[str] = Field(None, title='Action Source')
    data_processing_options: Optional[List[str]] = Field(
        None, title='Data Processing Options'
    )
    data_processing_options_country: Optional[int] = Field(
        None, title='Data Processing Options Country'
    )
    data_processing_options_state: Optional[int] = Field(
        None, title='Data Processing Options Country'
    )

    class Config:
        title = 'Facebook Params'
