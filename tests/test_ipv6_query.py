#!/usr/bin/env python3
"""
測試 IPv6 查詢問題。
比較直接使用 INET6_ATON 和使用更複雜的查詢方式。
"""

import unittest
from unittest.mock import MagicMock, patch
from dataclasses import dataclass

@dataclass
class Location:
    country_code: str
    region_name: str
    city_name: str
    latitude: float
    longitude: float
    zip_code: str

# 模擬數據庫連接
def get_mock_cursor(query_results=None):
    """獲取模擬的數據庫游標"""
    cursor = MagicMock()

    # 設置 fetchone 方法返回的結果
    if query_results is None:
        # 默認返回一些測試數據
        query_results = {
            # 簡單查詢結果
            'INET6_ATON': {
                '2001:4860:4860::8888': ('US', 'California', 'Mountain View', 37.4056, -122.0775, '94043'),
                '2606:4700:4700::1111': ('US', 'California', 'San Francisco', 37.7697, -122.3933, '94107'),
                '2620:1ec:c11::200': ('US', 'Washington', 'Redmond', 47.6801, -122.1206, '98052'),
                '2a00:1450:4001:801::2004': ('IE', 'Dublin', 'Dublin', 53.3331, -6.2489, 'D02'),
            },
            # 複雜查詢結果
            'COMPLEX': {
                '2001:4860:4860::8888': ('US', 'California', 'Mountain View', 37.4056, -122.0775, '94043'),
                '2606:4700:4700::1111': ('US', 'California', 'San Francisco', 37.7697, -122.3933, '94107'),
                '2620:1ec:c11::200': ('US', 'Washington', 'Redmond', 47.6801, -122.1206, '98052'),
                '2a00:1450:4001:801::2004': ('IE', 'Dublin', 'Dublin', 53.3331, -6.2489, 'D02'),
                # 這個 IP 只有複雜查詢能解析
                '2001:200:0:8002:203:47ff:fea5:3085': ('JP', 'Tokyo', 'Tokyo', 35.6895, 139.6917, '100-0001'),
            }
        }

    # 模擬 execute 方法，根據查詢類型和 IP 返回不同的結果
    def mock_execute(query, *args, **kwargs):
        ip = None
        query_type = None

        # 從查詢中提取 IP 地址
        if 'INET6_ATON' in query and 'BETWEEN ip_from AND ip_to' in query:
            # 簡單查詢
            start = query.find('INET6_ATON("') + 12
            end = query.find('")', start)
            ip = query[start:end]
            query_type = 'INET6_ATON'
        elif 'SELECT INET6_ATON' in query and 'CAST(CONV' in query:
            # 複雜查詢
            start = query.find('INET6_ATON("') + 12
            end = query.find('")', start)
            ip = query[start:end]
            query_type = 'COMPLEX'

        # 設置 fetchone 返回值
        if ip and query_type:
            result = query_results.get(query_type, {}).get(ip)
            cursor.fetchone.return_value = result
        else:
            cursor.fetchone.return_value = None

    cursor.execute = mock_execute
    return cursor

def query_ipv6_simple(ip: str, conn):
    """使用簡單的 INET6_ATON 查詢 IPv6 地址"""
    cursor = conn.cursor()
    try:
        cursor.execute(
            f"""
            SELECT
                `country_code`,
                `region_name`,
                `city_name`,
                `latitude`,
                `longitude`,
                `zip_code`
            FROM `ip2location_database` WHERE INET6_ATON("{ip}") BETWEEN ip_from AND ip_to LIMIT 1;
            """
        )
        result = cursor.fetchone()
        if result and result[0] != '-':
            return Location(
                *map(lambda x: x if x != '-' and x != 0.0 else None, result)
            )
        return None
    finally:
        cursor.close()

def query_ipv6_complex(ip: str, conn):
    """使用更複雜的查詢方式處理 IPv6 地址"""
    cursor = conn.cursor()
    try:
        cursor.execute(
            f"""
            SELECT
                `country_code`,
                `region_name`,
                `city_name`,
                `latitude`,
                `longitude`,
                `zip_code`
            FROM (
                SELECT INET6_ATON("{ip}") AS ip_bin
            ) AS ip,
            ip2location_database AS ip_info
            WHERE (
                CAST(CONV(SUBSTRING(HEX(ip.ip_bin), 1, 16), 16, 10) AS DECIMAL(39, 0)) * POW(2, 64) +
                CAST(CONV(SUBSTRING(HEX(ip.ip_bin), 17, 16), 16, 10) AS DECIMAL(39, 0))
            ) BETWEEN ip_info.ip_from AND ip_info.ip_to
            LIMIT 1;
            """
        )
        result = cursor.fetchone()
        if result and result[0] != '-':
            return Location(
                *map(lambda x: x if x != '-' and x != 0.0 else None, result)
            )
        return None
    finally:
        cursor.close()

class TestIPv6Query(unittest.TestCase):
    def setUp(self):
        """設置測試環境"""
        # 創建模擬連接
        self.conn = MagicMock()
        self.conn.cursor.return_value = get_mock_cursor()

    def test_ipv6_queries(self):
        """測試不同的 IPv6 查詢方法"""
        # 測試一些 IPv6 地址
        ipv6_addresses = [
            '2001:200:0:8002:203:47ff:fea5:3085',  # 日本 WIDE Project (只有複雜查詢能解析)
            '2001:4860:4860::8888',  # Google DNS (兩種方法都能解析)
            '2606:4700:4700::1111',  # Cloudflare DNS (兩種方法都能解析)
            '2620:1ec:c11::200',  # Microsoft (兩種方法都能解析)
            '2a00:1450:4001:801::2004',  # Google (兩種方法都能解析)
        ]

        for ip in ipv6_addresses:
            with self.subTest(ip=ip):
                # 使用簡單查詢
                simple_result = query_ipv6_simple(ip, self.conn)
                print(f"簡單查詢 {ip}: {simple_result}")

                # 使用複雜查詢
                complex_result = query_ipv6_complex(ip, self.conn)
                print(f"複雜查詢 {ip}: {complex_result}")

                # 如果兩種方法都返回結果，比較它們是否一致
                if simple_result and complex_result:
                    self.assertEqual(simple_result.country_code, complex_result.country_code)
                    self.assertEqual(simple_result.region_name, complex_result.region_name)
                    self.assertEqual(simple_result.city_name, complex_result.city_name)

                # 特殊情況：2001:200:0:8002:203:47ff:fea5:3085 只有複雜查詢能解析
                if ip == '2001:200:0:8002:203:47ff:fea5:3085':
                    self.assertIsNone(simple_result, f"簡單查詢不應該能解析 {ip}")
                    self.assertIsNotNone(complex_result, f"複雜查詢應該能解析 {ip}")
                else:
                    # 其他 IP 兩種方法都應該能解析
                    self.assertIsNotNone(simple_result, f"簡單查詢應該能解析 {ip}")
                    self.assertIsNotNone(complex_result, f"複雜查詢應該能解析 {ip}")

    def test_mariadb_128bit_limitation(self):
        """測試 MariaDB 128 位元限制"""
        # 創建一個特殊的模擬游標，模擬 MariaDB 不支援 128 位元的情況
        cursor = MagicMock()

        # 模擬 execute 方法，當使用簡單查詢時拋出異常
        def mock_execute(query, *args, **kwargs):
            if 'INET6_ATON' in query and 'BETWEEN ip_from AND ip_to' in query and not 'CAST(CONV' in query:
                # 簡單查詢，拋出異常
                raise Exception("MariaDB Error: INET6_ATON result is too large for comparison")
            else:
                # 複雜查詢，返回結果
                cursor.fetchone.return_value = ('JP', 'Tokyo', 'Tokyo', 35.6895, 139.6917, '100-0001')

        cursor.execute = mock_execute

        # 替換連接的游標
        conn = MagicMock()
        conn.cursor.return_value = cursor

        # 測試 IPv6 地址
        ip = '2001:200:0:8002:203:47ff:fea5:3085'

        # 簡單查詢應該失敗
        with self.assertRaises(Exception):
            query_ipv6_simple(ip, conn)

        # 複雜查詢應該成功
        result = query_ipv6_complex(ip, conn)
        self.assertIsNotNone(result)
        self.assertEqual(result.country_code, 'JP')

if __name__ == '__main__':
    unittest.main()
