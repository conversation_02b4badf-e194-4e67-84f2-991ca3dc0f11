site_name: Tagtoo Event
nav:
  - Overview: index.md
  - Architecture:
      - Overview: architecture.md
      - Pub/Sub 死信佇列: architecture/pubsub_deadletter.md
      - Kubernetes 自定義指標: architecture/custom_metrics.md
  - API:
      - Endpoints: api/endpoints.md
      - Objectives: api/objectives.md
      - Enums: api/enums.md
      - Events: api/events.md
      - Installation & Test: api/install_and_test.md
  - Facebook S2S:
      - Commands: fb_s2s/commands.md
      - Workflow: fb_s2s/workflow.md
      - Database: fb_s2s/database.md
      - Configurations: fb_s2s/configurations.md
      - Installation & Test: fb_s2s/install_and_test.md
      - Build: fb_s2s/build.md
      - To do list: fb_s2s/to_do_list.md
  - LTA (Label Target Audience):
      - Overview: lta/overview.md
      - Optimization: lta/optimization.md
      - Troubleshooting: lta/troubleshooting.md
      - 作業處理系統: s2s/lta-jobs.md
  - BigQuery Writer:
      - Commands: bq_writer/commands.md
      - IP2Location: bq_writer/ip2location.md
      - Configurations: bq_writer/configurations.md
      - Installation & Test: bq_writer/install_and_test.md
  - User Unify:
      - V1 (Offlined):
          - Announcement: user_unify/V1/announcement.md
          - Commands: user_unify/V1/commands.md
          - Schema: user_unify/V1/schema.md
          - Transactions: user_unify/V1/transactions.md
          - Examples: user_unify/V1/examples.md
          - Entity Deduplication: user_unify/V1/deduplication.md
          - Operation Bottleneck: user_unify/V1/bottleneck.md
          - Entity Rewrite: user_unify/V1/rewrite.md
          - Export: user_unify/V1/export.md
          - Configurations: user_unify/V1/configurations.md
          - Installation & Test: user_unify/V1/install_and_test.md
      - V2:
          - Overview: user_unify/V2/overview.md
          - Workflow: user_unify/V2/workflow.md
          - Data Structure: user_unify/V2/data_structure.md
          - Algorithm: user_unify/V2/algorithm.md
          - Improvement & Roadmap: user_unify/V2/improvement.md
          - Validation & Testing: user_unify/V2/validation.md
          - Commands: user_unify/V2/commands.md
          - Installation & Test: user_unify/V2/install_and_test.md
          - To do list: user_unify/V2/to_do_list.md
          - BigQuery SQL:
              - Table Functions: user_unify/V2/BigQuery_SQL/table_functions.md
              - User Unify SQL: user_unify/V2/BigQuery_SQL/UU_SQL.md
              - Rollback SQL: user_unify/V2/BigQuery_SQL/rollback_SQL.md
  - Dev/Ops:
      - Terraform:
          - Configurations: devops/terraform/configurations.md
          - Deployment (Local): devops/terraform/deploy_local.md
          - Deployment (GCE VM): devops/terraform/deploy_vm.md
      - Code Review:
          - 複雜架構變更指南: devops/CODE_REVIEW_GUIDE.md
      - Cost Management:
          - 成本分析最佳實務: devops/COST_ANALYSIS_BEST_PRACTICES.md
          - 成本監控策略: devops/COST_MONITORING_STRATEGY.md
      - Dataflow: devops/dataflow.md
      - Error Reporting: devops/error-reporting.md
      - MkDocs: devops/mkdocs.md
      - GitHub Actions Testing: devops/github-actions-testing.md
theme:
  name: "material"
  palette:
    # Palette toggle for automatic mode
    - media: "(prefers-color-scheme)"
      toggle:
        icon: material/brightness-auto
        name: Switch to light mode

    # Palette toggle for light mode
    - media: "(prefers-color-scheme: light)"
      scheme: default

      toggle:
        icon: material/brightness-7
        name: Switch to dark mode

    # Palette toggle for dark mode
    - media: "(prefers-color-scheme: dark)"
      scheme: slate
      toggle:
        icon: material/brightness-4
        name: Switch to system preference
markdown_extensions:
  - admonition
  - pymdownx.highlight
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:pymdownx.superfences.fence_div_format
  - pymdownx.snippets
  - pymdownx.details
  - toc:
      permalink: true
plugins:
  - search
  - mkdocstrings
