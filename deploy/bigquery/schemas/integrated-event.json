[{"description": "A long-lived user ID.", "name": "permanent", "type": "STRING", "mode": "NULLABLE"}, {"description": "The Tagtoo ecommerce ID.", "name": "ec_id", "type": "INTEGER", "mode": "NULLABLE"}, {"description": "Partner source identifier.", "name": "partner_source", "type": "STRING", "mode": "REQUIRED"}, {"description": "The time an event is triggered. In UTC+0 format.", "name": "event_time", "type": "TIMESTAMP", "mode": "NULLABLE"}, {"description": "The time the record was created. In UTC+0 format.", "name": "create_time", "type": "TIMESTAMP", "mode": "REQUIRED"}, {"description": "The current page link.", "name": "link", "type": "STRING", "mode": "NULLABLE"}, {"description": "Event name.", "name": "event", "type": "STRING", "mode": "NULLABLE"}, {"description": "The total price of an event.", "name": "value", "type": "FLOAT", "mode": "NULLABLE"}, {"description": "Sale currency.", "name": "currency", "type": "STRING", "mode": "NULLABLE"}, {"description": "Order ID from custom data.", "name": "order_id", "type": "STRING", "mode": "NULLABLE"}, {"description": "Product item details.", "name": "items", "type": "RECORD", "mode": "REPEATED", "fields": [{"description": "Product ID.", "name": "id", "type": "STRING", "mode": "REQUIRED"}, {"description": "Product name.", "name": "name", "type": "STRING", "mode": "REQUIRED"}, {"description": "Sale price.", "name": "price", "type": "FLOAT", "mode": "NULLABLE"}, {"description": "Product quantity.", "name": "quantity", "type": "FLOAT", "mode": "NULLABLE"}, {"description": "Product availability.", "name": "availability", "type": "STRING", "mode": "NULLABLE"}]}, {"description": "User information", "name": "user", "type": "RECORD", "mode": "NULLABLE", "fields": [{"description": "A hashed email.", "name": "em", "type": "STRING", "mode": "NULLABLE"}, {"description": "A hashed phone number.", "name": "ph", "type": "STRING", "mode": "NULLABLE"}]}, {"description": "Partner ID.", "name": "partner_id", "type": "STRING", "mode": "NULLABLE"}, {"description": "Page information.", "name": "page", "type": "STRING", "mode": "NULLABLE"}, {"description": "GEO location information resolved from client IP address.", "name": "location", "type": "RECORD", "mode": "NULLABLE", "fields": [{"description": "Two-character country code based on ISO 3166.", "name": "country_code", "type": "STRING", "mode": "REQUIRED"}, {"description": "Region or state name.", "name": "region_name", "type": "STRING", "mode": "NULLABLE"}, {"description": "City name.", "name": "city_name", "type": "STRING", "mode": "NULLABLE"}, {"description": "City latitude.", "name": "latitude", "type": "FLOAT", "mode": "NULLABLE"}, {"description": "City longitude.", "name": "longitude", "type": "FLOAT", "mode": "NULLABLE"}, {"description": "Zip code.", "name": "zip_code", "type": "STRING", "mode": "NULLABLE"}]}, {"description": "Optimized raw JSON data with tracking information (v2.1 tracking_id strategy).", "name": "raw_json", "type": "JSON", "mode": "NULLABLE"}]