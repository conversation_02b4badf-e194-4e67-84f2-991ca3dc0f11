## Installation and Setup

Currently, the images built by Mac M1 system cannot be run properly on the production machines.
Therefore, we establish a GCE VM with the same environment as the production machines to workaround
this issue. The VM is at the same project, named `ad-track-deploy`.

Since turning on the VM for a long periods can lead to unnecessary expenses, please remember to 
shut down the VM after finishing deployment, which also means that one should turn on the VM each 
time when deploying.

After connecting to this VM, one should log in as root user and move to the source code directory:

```shell
sudo su
cd /root/ad-track
```

You may find the source codes are under the `ad-track` directory. Then make sure if the code meets the latest commit on `master` branch:

```shell
git checkout master
git pull origin master
```

### Build Image

According to the latest commits, you should know which of the services are actually affected by
those changes. Trigger the make commands specified in `Makefile`, and the corresponding image will
be built and pushed onto GCR when finished:

```shell
make build-event-api
make build-event-s2s
make build-event-config-api
make build-event-bq-writer
make build-ip2location
make build-event-user-unify-job
```

Once the complete message has been displayed, you may see the image tag is shown.
Remember to update the image tag of related configurations in `prod.tfvars`.

### Deployment

Now you are ready for deployment. Firstly, start the `deploy` container by triggering following
command:

```shell
docker compose -f docker-compose.deploy.yml up -d
```

Note that since multiple shell environment are still not available at this moment, we specify a
`-d` here to make it run at background.

Then, attach the container by make command:

```shell
make manage
```

Then, run this command in the container to install the requirements at your first-time log-in:

```shell
terraform init
```

Switch to workspace `prod`:

```shell
terraform workspace select prod
```

Finally, apply the changes via command:

```shell
terraform apply -var-file=prod.tfvars
```

Check all of the changes then type `yes`, and the deployment process should start.

After the completion of deployment, remember to commit the code that updating image tags. The 
header of commit message can be with the format as follows:

```shell
deploy(srv1, [srv2, ...]): Update image
```

### Note

A ConnectionRefused error may occur when one modifies resources. This is because there are some resource
**already created** in the **Kubernetes Cluster** which need extra context file to be able to access. In this case,
simply run a make command to generate the context for cluster:

```shell
make config-cluster CLUSTER=<cluster-name>
```
