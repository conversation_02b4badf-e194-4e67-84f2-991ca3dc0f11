# 創建 Custom Metrics Server Resources ClusterRole
resource "kubernetes_cluster_role" "custom_metrics_server_resources" {
  metadata {
    name = "custom-metrics-server-resources"
  }

  rule {
    api_groups = ["custom.metrics.k8s.io"]
    resources  = ["*"]
    verbs      = ["*"]
  }

  rule {
    api_groups = ["external.metrics.k8s.io"]
    resources  = ["*"]
    verbs      = ["*"]
  }

  rule {
    api_groups = [""]
    resources  = ["configmaps", "namespaces", "pods", "services"]
    verbs      = ["get", "list", "watch"]
  }

  rule {
    api_groups     = [""]
    resources      = ["configmaps"]
    resource_names = ["extension-apiserver-authentication"]
    verbs          = ["get", "list", "watch"]
  }
}

# 創建 Custom Metrics Resource Reader ClusterRole
resource "kubernetes_cluster_role" "custom_metrics_resource_reader" {
  metadata {
    name = "custom-metrics-resource-reader"
  }

  rule {
    api_groups = ["authorization.k8s.io"]
    resources  = ["subjectaccessreviews"]
    verbs      = ["create"]
  }
}

# 創建 Custom Metrics Resource Reader ClusterRoleBinding
resource "kubernetes_cluster_role_binding" "custom_metrics_resource_reader" {
  metadata {
    name = "custom-metrics-resource-reader"
  }

  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "ClusterRole"
    name      = kubernetes_cluster_role.custom_metrics_resource_reader.metadata[0].name
  }

  subject {
    kind      = "ServiceAccount"
    name      = kubernetes_service_account.custom_metrics_stackdriver_adapter.metadata[0].name
    namespace = kubernetes_namespace.custom_metrics.metadata[0].name
  }
}
