<script src="https://unpkg.com/mermaid@8.13.3/dist/mermaid.min.js"></script>

## Docker Compose

S2S microservices is depends on Event Config database. Please compose these services locally in a specific order.

```mermaid
flowchart TB
  subgraph docker bridge network
    subgraph network:config
        config-api --- config-db[(config-db)]
    end
    subgraph network:s2s
        s2s-pubsub -- pull --> capi-sub-batch
        s2s-pubsub -- pull --> capi-sub-singleton
        s2s-pubsub -- pull --> lta-sub
        capi-sub-batch -- query --> s2s-db[(s2s-db)]
        capi-sub-singleton -- query --> s2s-db
        capi-sub-batch -- publish --> s2s-broker
        capi-sub-singleton -- publish --> s2s-broker
        lta-sub -- query --> fake-GCS
        lta-sub -- publish --> capi-sub-batch
        s2s-broker -- subscribe --> capi-failed-batch-handler
    end
    config-db -. sync .-> s2s-db
  end
  config-api o-. 10001:8000 .-o Host
```

1. Start the event config services
    ```shell
    docker-compose -f docker-compose.config.yml up
    ```
2. Start the s2s services
    ```shell
    docker-compose -f docker-compose.s2s.yml up
    ```
3. Turn down the services
    ```shell
    docker-compose -f docker-compose.config.yml down
    docker-compose -f docker-compose.s2s.yml down
    ```

!!! info
    The Pub/Sub local service is an emulator built from `messagebird/gcloud-pubsub-emulator`.

## Test

Use shell scripts to test across services. Start the services and run these commands at `tests/s2s` folder:

* Test batch channel
    ```shell
    ./test_batch.sh
    ```
* Test singleton channel
    ```shell
    ./test_failed_batch.sh
    ```
* Test lta s2s
    ```
    ./test_lta.sh
    ```