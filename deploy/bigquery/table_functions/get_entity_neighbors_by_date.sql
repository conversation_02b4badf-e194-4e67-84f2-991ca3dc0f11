WITH temp_ed AS (
    SELECT * FROM `tagtoo-tracking.event_prod.get_entity_by_date`(start_date, end_date)
  ),

  em_match AS (
  SELECT ed.permanent AS base_permanent, ed2.permanent AS neighbors, ed2.entity_time, CAST(ed.em AS STRING) AS em, '' AS ph, '' AS un, '' AS gid
  FROM temp_ed ed
  JOIN temp_ed ed2 ON ed2.em = ed.em
),
ph_match AS (
  SELECT ed.permanent AS base_permanent, ed2.permanent AS neighbors, ed2.entity_time, '' AS em, CAST(ed.ph AS STRING) AS ph, '' AS un, '' AS gid
  FROM temp_ed ed
  JOIN temp_ed ed2 ON ed2.ph = ed.ph
),
un_match AS (
  SELECT ed.permanent AS base_permanent, ed2.permanent AS neighbors, ed2.entity_time, '' AS em, '' AS ph, CAST(ed.un AS STRING) AS un, '' AS gid
  FROM temp_ed ed
  JOIN temp_ed ed2 ON ed2.un = ed.un
),
gid_match AS (
  SELECT ed.permanent AS base_permanent, ed2.permanent AS neighbors, ed2.entity_time, '' AS em, '' AS ph, '' AS un, CAST(ed.gid AS STRING) AS gid
  FROM temp_ed ed
  JOIN temp_ed ed2 ON ed2.gid = ed.gid
)

SELECT base_permanent, neighbors, entity_time, em, ph, un, gid
FROM (
  SELECT * FROM em_match
  UNION ALL
  SELECT * FROM ph_match
  UNION ALL
  SELECT * FROM un_match
  UNION ALL
  SELECT * FROM gid_match
)
ORDER BY base_permanent