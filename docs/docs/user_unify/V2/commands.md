### update_grouping_result

Update Grouping Result for specific date range

```
python update_grouping_result.py [-h] 
                    [-s --start-date {yesterday}]
                    [-e --end-date {yesterday}]
                    [-r --recalc-daily-grouping-result]
```

**Arguments**

|Name|Description|
|---|---|
| start-date | Start date of input event data, default is yesterday. |
| end-date | End date of input event data, default is yesterday. |
| recalc-daily-grouping-result | Recalculate daily grouping result even though it is existed. |
