SELECT
    DISTINCT permanent,
    MIN(event_time) OVER (PARTITION BY permanent) entity_time,
    user.em,
    user.ph,
    user.un,
    -- user.fbp,
    IF(
        REGEXP_CONTAINS(TRIM(user.gid), r'^GA\d\.\d\.\d+\.\d+'),
        TRIM(user.gid),
        NULL
    ) gid
FROM
    `tagtoo-tracking.event_prod.tagtoo_event`
WHERE
    DATE(event_time, 'Asia/Taipei') BETWEEN DATE(start_date)
    AND DATE(end_date)