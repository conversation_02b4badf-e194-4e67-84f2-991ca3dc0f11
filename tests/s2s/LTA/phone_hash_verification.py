#!/usr/bin/env python3
"""
驗證電話號碼 hash 處理邏輯一致性
確認修改後的 _safe_encode_phone 方法與原本邏輯完全一致
"""

import re
import sys
import os
from hashlib import sha256

# 添加路徑以便導入 LTA loader
sys.path.append('s2s')
from s2s.LTA.loader import DataLoader


def original_phone_hash(phone: str) -> str:
    """原本的電話號碼 hash 邏輯（模擬）"""
    # 根據程式碼，原本應該是：
    # 1. 直接使用 re.sub(r'[^\d]', '', phone) 去除所有非數字字符
    # 2. 然後進行 SHA256 hash
    normalized = re.sub(r'[^\d]', '', phone)
    if not normalized:
        return ''
    
    sha_256 = sha256(normalized.encode('utf8'))
    return sha_256.hexdigest()


def test_phone_hash_consistency():
    """測試電話號碼 hash 處理的一致性"""
    
    # 建立 DataLoader 實例來測試新方法
    loader = DataLoader()
    
    # 測試用的電話號碼案例
    test_phones = [
        '+886-912-345-678',  # 國際格式
        '886-912-345-678',   # 無 + 號
        '0912-345-678',      # 本地格式
        '09123456789',       # 純數字
        '******-123-4567',   # 美國號碼
        '(02)2345-6789',     # 市話格式
        '02 2345 6789',      # 空格分隔
        '+886 912 345 678',  # 空格分隔國際格式
        '',                  # 空字串
        'abc123',           # 包含字母
        '+++---',           # 純符號
        '123abc456',        # 混合字母數字
        # 已 hash 的電話號碼
        'a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456',  # 64字符十六進位
        'ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890',  # 大寫
    ]
    
    print("📞 電話號碼 Hash 處理一致性驗證")
    print("=" * 80)
    
    all_consistent = True
    
    for phone in test_phones:
        # 原本邏輯的結果
        original_result = original_phone_hash(phone)
        
        # 新邏輯的結果 (對於需要 hash 的電話號碼)
        new_result = loader._safe_encode_phone(phone)
        
        # 如果是已經 hash 的值，應該直接返回（小寫）
        if len(phone) == 64 and all(c in '0123456789abcdefABCDEF' for c in phone):
            expected_result = phone.lower()
        else:
            expected_result = original_result
        
        is_consistent = (new_result == expected_result)
        status = "✅ 一致" if is_consistent else "❌ 不一致"
        
        print(f"輸入: {phone!r:<25}")
        print(f"  原本結果: {original_result!r}")
        print(f"  新的結果: {new_result!r}")
        print(f"  預期結果: {expected_result!r}")
        print(f"  狀態: {status}")
        print("-" * 80)
        
        if not is_consistent:
            all_consistent = False
    
    print(f"\n🎯 總體驗證結果: {'✅ 完全一致' if all_consistent else '❌ 發現不一致'}")
    
    # 額外測試：確認 + 號確實被移除
    print("\n📋 + 號移除驗證:")
    plus_sign_tests = [
        '+886912345678',
        '+1555123456',
        '886+912345678',  # 異常位置的 +
    ]
    
    for phone in plus_sign_tests:
        normalized = re.sub(r'[^\d]', '', phone)
        has_plus = '+' in normalized
        print(f"  {phone!r} -> {normalized!r} (包含+號: {has_plus})")
    
    return all_consistent


if __name__ == '__main__':
    success = test_phone_hash_consistency()
    sys.exit(0 if success else 1)