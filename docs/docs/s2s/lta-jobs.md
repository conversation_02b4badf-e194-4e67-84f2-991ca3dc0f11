# LTA 作業處理系統

本文檔詳細說明了 LTA（Lookalike Targeting Audience）作業處理系統的架構、命名規則和監控機制。

## 概述

LTA 作業處理系統負責從 Google Cloud Storage 讀取 AVRO 文件，處理其中的用戶數據，並將其發送到 Facebook Conversion API。這個系統由以下組件組成：

1. **s2s-lta-subscriber**：監聽 Pub/Sub 主題 `lta-prod`，接收處理請求
2. **load-lta-data 作業**：由 s2s-lta-subscriber 創建的 Kubernetes 作業，負責實際的數據處理

## 作業命名規則

### 新的命名格式

LTA 作業使用以下格式命名：

```text
load-lta-data-{YYYYMMDD}-{HHMMSS}-{UUID}
```

其中：

- `YYYYMMDD`：作業創建的日期（年月日）
- `HHMMSS`：作業創建的時間（時分秒）
- `UUID`：8 位隨機 UUID，用於確保名稱唯一性

例如：`load-lta-data-20250516-043822-c9ba747e`

### 命名格式的優點

1. **人類可讀性**：可以直接看出作業創建的日期和時間
2. **唯一性**：通過添加 UUID 短碼確保名稱不會衝突
3. **排序友好**：按照時間順序排序時，作業會按照創建時間排列
4. **簡潔**：格式緊湊但仍然包含所有必要信息

### 舊的命名格式（已棄用）

舊的命名格式使用 Unix 時間戳：

```text
load-lta-data-{timestamp}
```

例如：`load-lta-data-1747352141.4484668`

這種格式不易於人類閱讀，因為時間戳需要轉換才能理解作業創建的時間。

## 日誌級別和監控

### 日誌級別

LTA 作業處理系統使用以下日誌級別：

1. **DEBUG**：詳細的調試信息，用於開發和故障排除
2. **INFO**：一般信息，如作業開始、批次處理完成等
3. **WARNING**：警告信息，如重試發布消息等
4. **ERROR**：錯誤信息，如無法連接到 GCS 等
5. **CRITICAL**：嚴重錯誤，需要立即關注，如：
   - 無法從 GCS 讀取文件
   - 處理像素數據時發生錯誤
   - 沒有成功發布任何消息

### CRITICAL 日誌的用途

CRITICAL 日誌用於標記需要立即關注的嚴重問題。這些問題通常表示系統無法正常運行，可能導致數據丟失或處理失敗。

以下情況會記錄 CRITICAL 日誌：

1. **無法從 GCS 讀取文件**：

   ```text
   CRITICAL: Failed to read file {file_name} from GCS. This is a serious issue that requires immediate attention.
   ```

2. **處理像素數據時發生錯誤**：

   ```text
   CRITICAL: Failed to compose pixel data for file {file_name}. This is a serious issue that requires immediate attention.
   ```

3. **沒有成功發布任何消息**：

   ```text
   CRITICAL: No messages were published from {file_name}. This is a serious issue that requires immediate attention.
   ```

### 監控和警報

我們使用 Google Cloud Logging 來監控 LTA 作業處理系統。特別是，我們設置了警報來檢測 CRITICAL 日誌，以便在出現嚴重問題時立即通知團隊。

#### 警報設置

我們在 Google Cloud Logging 中設置了以下警報：

1. **LTA Critical Errors**：
   - 查詢：`resource.type="k8s_container" resource.labels.namespace_name="default" resource.labels.container_name="loader" severity=CRITICAL`
   - 通知頻率：5 分鐘
   - 通知渠道：Email ([<EMAIL>](mailto:<EMAIL>))

##### 使用 gcloud 命令設置警報

您可以使用以下 gcloud 命令來設置 LTA Critical Errors 警報：

步驟 1: 創建日誌指標：

```bash
gcloud logging metrics create lta-critical-errors \
  --description="Detect CRITICAL level logs in LTA processing" \
  --log-filter="resource.type=\"k8s_container\" resource.labels.namespace_name=\"default\" resource.labels.container_name=\"loader\" severity=CRITICAL"
```

步驟 2: 創建警報策略（使用 JSON 文件）：

首先，創建一個 JSON 文件 `alert-policy.json`：

```json
{
  "displayName": "LTA Critical Errors Alert",
  "documentation": {
    "content": "Alert when CRITICAL level logs are detected in LTA processing. This indicates a serious issue that requires immediate attention.",
    "mimeType": "text/markdown"
  },
  "conditions": [
    {
      "displayName": "Log match condition",
      "conditionThreshold": {
        "filter": "metric.type=\"logging.googleapis.com/user/lta-critical-errors\" resource.type=\"k8s_container\"",
        "comparison": "COMPARISON_GT",
        "thresholdValue": 0,
        "duration": "0s",
        "trigger": {
          "count": 1
        },
        "aggregations": [
          {
            "alignmentPeriod": "300s",
            "perSeriesAligner": "ALIGN_COUNT",
            "crossSeriesReducer": "REDUCE_SUM"
          }
        ]
      }
    }
  ],
  "combiner": "OR",
  "notificationChannels": [
    "projects/tagtoo-tracking/notificationChannels/YOUR_CHANNEL_ID"
  ],
  "alertStrategy": {
    "autoClose": "604800s"
  }
}
```

然後，使用以下命令創建警報策略：

```bash
gcloud alpha monitoring policies create --policy-from-file=alert-policy.json
```

注意：您需要將 `YOUR_CHANNEL_ID` 替換為實際的通知渠道 ID。您可以使用以下命令列出可用的通知渠道：

```bash
gcloud alpha monitoring channels list
```

#### 故障排除

當收到 CRITICAL 日誌警報時，請按照以下步驟進行故障排除：

1. **檢查日誌詳情**：
   - 查看完整的日誌消息，了解具體的錯誤原因
   - 注意文件名、處理階段和錯誤類型

2. **檢查 GCS 文件**：
   - 確認 GCS 文件是否存在
   - 檢查文件權限和格式

3. **檢查 Pub/Sub 消息**：
   - 查看觸發作業的 Pub/Sub 消息
   - 確認消息屬性是否正確

4. **檢查 Kubernetes 作業**：
   - 查看作業的狀態和日誌
   - 檢查作業的資源使用情況

## 作業處理流程

1. **接收 Pub/Sub 消息**：
   - s2s-lta-subscriber 接收包含 `ec_id`、`file_name` 和 `version` 的 Pub/Sub 消息

2. **創建 Kubernetes 作業**：
   - s2s-lta-subscriber 使用 `create_loader_job` 函數創建 Kubernetes 作業
   - 作業名稱使用新的命名格式：`load-lta-data-{YYYYMMDD}-{HHMMSS}-{UUID}`

3. **處理數據**：
   - 作業從 GCS 讀取 AVRO 文件
   - 處理用戶數據，生成像素數據
   - 將像素數據發布到 Facebook Conversion API

4. **記錄結果**：
   - 作業記錄處理結果，包括發布的消息數量
   - 如果沒有發布任何消息，記錄 CRITICAL 日誌

## 結論

LTA 作業處理系統是 Tagtoo 追蹤系統的重要組成部分，負責將用戶數據發送到 Facebook Conversion API。通過使用人類可讀的作業名稱和 CRITICAL 日誌級別，我們可以更容易地監控和維護這個系統，確保它能夠可靠地運行。
