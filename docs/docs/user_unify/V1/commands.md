## Pub/Sub Subscriber

Run a Pub/Sub subscriber by given name. 

```
python subscribe.py [-h] 
                    [--version {v1}] 
                    [--max-messages MAX_MESSAGES]
                    [--timeout TIMEOUT] {user_unify_streaming,user_unify_rewrite}
```

**Instances**

|Subscriber Name|Type|Description|
|---|---|---|
|user_unify_streaming|asynchronous pull|Streaming deduplicated user entities into user unify graph in BigTable.|
|user_unify_rewrite|asynchronous pull|Rewrite user entities into user unify graph in BigTable.|
