# In order to use the shared local packages, please build from the root context

# Stage1
FROM --platform=linux/amd64 python:3.8 as build-deps

COPY shared /shared

RUN python /shared/setup_pubsub.py bdist_wheel && \
    python /shared/setup_error_report.py bdist_wheel && \
    python /shared/setup_cloud_storage.py bdist_wheel

# Stage2
FROM --platform=linux/amd64 python:3.8

LABEL author="<PERSON> (<EMAIL>)"
LABEL modifier="<PERSON> (<EMAIL>)"
LABEL modification="Fixed platform architecture issue"

# python envs
ENV PYTHONFAULTHANDLER=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONHASHSEED=random \
    PIP_NO_CACHE_DIR=off \
    PIP_DISABLE_PIP_VERSION_CHECK=on \
    PIP_DEFAULT_TIMEOUT=100

USER root

COPY --from=build-deps /dist /dist

WORKDIR /app

COPY s2s/requirements.txt .

RUN pip install --upgrade pip && \
    pip install /dist/* && \
    pip install -r requirements.txt

COPY s2s/. .
