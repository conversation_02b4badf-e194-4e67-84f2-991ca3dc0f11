# FBP、FBC 值格式詳解及 Phone + Segment_ID 可行性分析

## FBP 和 FBC 值的詳細格式

### FBP (Facebook Browser ID) 格式

**格式**: `fb.{subdomainIndex}.{creationTime}.{randomNumber}`

#### 各部分說明：
1. **version**: 固定前綴 `fb`
2. **subdomainIndex**: Cookie 定義的域名層級
   - `com` = 0
   - `example.com` = 1  
   - `www.example.com` = 2
   - 如果在伺服器端生成且未儲存 `_fbp` cookie，使用值 1
3. **creationTime**: UNIX 時間戳（毫秒），`_fbp` cookie 建立的時間
4. **randomNumber**: Meta Pixel SDK 生成的隨機數字，確保每個 `_fbp` cookie 的唯一性

#### 範例：
```
fb.1.1596403881668.1116446470
```

### FBC (Facebook Click ID) 格式

**格式**: `fb.{subdomainIndex}.{creationTime}.{fbclid}`

#### 各部分說明：
1. **version**: 固定前綴 `fb`
2. **subdomainIndex**: Cookie 定義的域名層級（同 FBP）
   - 如果在伺服器端生成且未儲存 `_fbc` cookie，使用值 1
3. **creationTime**: UNIX 時間戳（毫秒），`_fbc` cookie 儲存的時間
   - 如果未儲存 cookie，使用第一次觀察或接收到 `fbclid` 值的時間戳
4. **fbclid**: 來自頁面 URL 中 `fbclid` 查詢參數的值

#### 範例：
```
fb.1.1554763741205.IwAR2F4-dbP0l7Mn1IawQQGCINEz7PYXQvwjNwB_qa2ofrHyiLjcbCRxTDMgk
```

### 值的來源和取得方式

#### FBP 來源：
1. **Meta Pixel 自動生成**: 當 Meta Pixel 安裝在網站上時，會自動在 `_fbp` cookie 中儲存唯一識別符
2. **手動生成**: 在伺服器端手動建立，遵循格式規範

#### FBC 來源：
1. **URL 查詢參數**: 從 `fbclid` URL 參數取得
   ```
   https://example.com/?fbclid=IwAR2F4-dbP0l7Mn1IawQQGCINEz7PYXQvwjNwB_qa2ofrHyiLjcbCRxTDMgk
   ```
2. **_fbc Cookie**: 當 Meta Pixel 存在時，會自動儲存在 `_fbc` cookie 中
3. **伺服器端儲存**: 手動從 URL 參數擷取並格式化儲存

## Phone + Segment_ID 的 API 可行性分析

### ✅ 完全可行 - Meta API 觀點

根據 Meta Conversion API 官方文檔：

#### Meta API 要求
- **只需要至少一個用戶識別參數**
- `phone` (ph) 是有效的用戶識別參數
- `segment_id` 屬於 custom_data，用於受眾定向

#### 有效的最小配置
```json
{
  "event_name": "LTA",
  "event_time": 1640995200,
  "action_source": "other",
  "user_data": {
    "ph": ["hashed_phone_number"]
  },
  "custom_data": {
    "segment_id": "your_segment_value"
  }
}
```

### ✅ 系統實作支援

根據程式碼分析（`s2s/s2s/LTA/loader.py`）：

#### AVRO 檔案結構支援
```json
{
  "phones": ["1234567890"],
  "segment_id": ["targeting_segment_1"],
  "fb_info": [
    {
      "fbp_fbc_ip": ["", "", "***********"]
    }
  ]
}
```

#### 處理邏輯
1. **電話號碼自動 hash**: 系統會自動對 `phones` 進行 SHA256 hash
2. **Segment_ID 組合**: 系統會將 `segment_id` 陣列組合成字串格式
3. **必要結構**: `fb_info` 陣列仍需存在，但可以包含空值

### 實際測試場景

#### 情境 1: 僅 Phone + Segment_ID + IP

**AVRO 輸入**:
```json
{
  "phones": ["+886912345678"],
  "segment_id": ["mobile_users"],
  "fb_info": [
    {
      "fbp_fbc_ip": ["", "", "************"]
    }
  ]
}
```

**Meta API 輸出**:
```json
{
  "event_name": "LTA",
  "event_time": 1640995200,
  "action_source": "other", 
  "user_data": {
    "em": [],
    "ph": ["hashed_phone_sha256"],
    "ge": [],
    "db": [],
    "zp": [],
    "fbp": "",
    "fbc": "",
    "client_ip_address": "************"
  },
  "custom_data": {
    "segment_id": "_mobile_users_"
  }
}
```

**結果**: ✅ **完全可行且有效**

#### 情境 2: 多個電話號碼

**AVRO 輸入**:
```json
{
  "phones": ["+886912345678", "+886987654321"],
  "segment_id": ["high_value_customers", "mobile_users"],
  "fb_info": [
    {
      "fbp_fbc_ip": ["", "", "************"]
    }
  ]
}
```

**結果**: ✅ **支援多個電話號碼和多個分群標籤**

### 電話號碼格式要求

根據 Meta 官方文檔，電話號碼的格式化要求：

#### 正規化規則
1. **移除符號和字母**: 只保留數字
2. **移除前導零**: 移除任何前導零
3. **包含國碼**: 必須包含國碼才能用於匹配
4. **美國號碼**: 數字 1 必須在電話號碼前面
5. **全球適用**: 即使所有數據來自同一國家，也要包含國碼

#### 範例
- **輸入**: `(886) 912-345-678`
- **正規化**: `886912345678`
- **Hash**: SHA256 後的結果

### 效能和匹配率考量

#### 優點
1. **良好的匹配率**: 電話號碼是有效的用戶識別符
2. **全球適用**: 適用於各種市場和地區
3. **隱私友善**: 經過 hash 處理，保護用戶隱私
4. **系統支援**: 現有系統完全支援此配置

#### 注意事項
1. **格式正確性**: 確保電話號碼包含正確的國碼
2. **Hash 處理**: 系統會自動進行 SHA256 hash
3. **IP 地址**: 建議同時提供 IP 地址以提高匹配準確度

## 最佳實踐建議

### 推薦配置 1: Phone + Segment + IP
```json
{
  "phones": ["+886912345678"],
  "segment_id": ["target_audience"],
  "fb_info": [
    {
      "fbp_fbc_ip": ["", "", "user_ip_address"]
    }
  ]
}
```

### 推薦配置 2: Phone + Email + Segment (最佳)
```json
{
  "phones": ["+886912345678"],
  "emails": ["<EMAIL>"],
  "segment_id": ["target_audience"],
  "fb_info": [
    {
      "fbp_fbc_ip": ["", "", "user_ip_address"]
    }
  ]
}
```

## 結論

### FBP/FBC 值格式
- **FBP**: `fb.{subdomain}.{timestamp_ms}.{random}`
- **FBC**: `fb.{subdomain}.{timestamp_ms}.{fbclid}`
- 兩者都是 Meta 自動生成或根據規範手動建立

### Phone + Segment_ID 可行性
- ✅ **Meta API 完全支援**
- ✅ **系統實作完全支援**
- ✅ **實際應用中有效且常用**
- ✅ **符合隱私保護要求**

**Phone + Segment_ID 是一個完全可行且有效的配置**，特別適合：
1. 行動應用場景
2. 沒有 web cookie 的環境
3. 跨平台用戶追蹤
4. 隱私導向的實作方式