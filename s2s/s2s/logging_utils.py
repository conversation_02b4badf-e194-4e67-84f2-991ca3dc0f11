"""
Google Cloud Logging 整合工具

這個模組提供了與 Google Cloud Logging 整合的工具，
確保 Python logging 級別正確映射到 Google Cloud Logging 級別。
"""

import logging
from google.cloud import logging as cloud_logging
from google.cloud.logging.handlers import CloudLoggingHandler, StructuredLogHandler
from google.cloud.logging.handlers.transports import BackgroundThreadTransport


class EnhancedCloudLoggingHandler(CloudLoggingHandler):
    """
    增強的 Google Cloud Logging 處理器，確保日誌級別正確映射。

    這個處理器擴展了標準的 CloudLoggingHandler，
    確保 INFO 級別的日誌在 Google Cloud Logging 中也顯示為 INFO 級別。
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def emit(self, record):
        """
        發送日誌記錄到 Google Cloud Logging。

        這個方法覆蓋了父類的 emit 方法，
        確保日誌級別正確映射到 Google Cloud Logging 級別。

        Args:
            record: 日誌記錄對象
        """
        # 確保 record 的 levelname 屬性與實際級別一致
        record.levelname = logging.getLevelName(record.levelno)

        # 直接設置 Cloud Logging 的 severity 字段
        if not hasattr(record, 'severity'):
            # 將 Python logging 級別映射到 Google Cloud Logging 級別
            if record.levelno >= logging.ERROR:
                record.severity = 'ERROR'
            elif record.levelno >= logging.WARNING:
                record.severity = 'WARNING'
            elif record.levelno >= logging.INFO:
                record.severity = 'INFO'
            elif record.levelno >= logging.DEBUG:
                record.severity = 'DEBUG'
            else:
                record.severity = 'DEFAULT'

        # 調用父類的 emit 方法
        super().emit(record)


class CustomStructuredLogHandler(StructuredLogHandler):
    """
    自定義的 StructuredLogHandler，確保日誌級別正確映射。

    這個處理器擴展了標準的 StructuredLogHandler，
    確保 INFO 級別的日誌在 Google Cloud Logging 中也顯示為 INFO 級別。
    """

    def emit(self, record):
        """
        發送日誌記錄到 Google Cloud Logging。

        這個方法覆蓋了父類的 emit 方法，
        確保日誌級別正確映射到 Google Cloud Logging 級別。

        Args:
            record: 日誌記錄對象
        """
        # 確保 record 的 levelname 屬性與實際級別一致
        record.levelname = logging.getLevelName(record.levelno)

        # 直接設置 Cloud Logging 的 severity 字段
        if not hasattr(record, 'severity'):
            # 將 Python logging 級別映射到 Google Cloud Logging 級別
            if record.levelno >= logging.ERROR:
                record.severity = 'ERROR'
            elif record.levelno >= logging.WARNING:
                record.severity = 'WARNING'
            elif record.levelno >= logging.INFO:
                record.severity = 'INFO'
            elif record.levelno >= logging.DEBUG:
                record.severity = 'DEBUG'
            else:
                record.severity = 'DEFAULT'

        # 調用父類的 emit 方法
        super().emit(record)


def setup_cloud_logging(logger_name, project_id=None, use_structured=True):
    """
    設置 Google Cloud Logging 處理器。

    Args:
        logger_name: 日誌記錄器名稱
        project_id: Google Cloud 項目 ID，如果為 None 則使用默認值
        use_structured: 是否使用 StructuredLogHandler，默認為 True

    Returns:
        配置好的日誌記錄器
    """
    # 獲取日誌記錄器
    logger = logging.getLogger(logger_name)

    if use_structured:
        # 創建自定義的 StructuredLogHandler
        handler = CustomStructuredLogHandler(project_id=project_id)
    else:
        # 初始化 Google Cloud Logging 客戶端
        client = cloud_logging.Client(project=project_id)

        # 創建增強的 Cloud Logging 處理器
        handler = EnhancedCloudLoggingHandler(
            client,
            name=logger_name
        )

    # 將處理器添加到日誌記錄器
    logger.addHandler(handler)

    return logger
