#!/bin/sh

set -e
set -o pipefail

cmd="$@"

ip2location_db_ready() {
python << END
import sys
from bq_writer.ip2location import ip2location_connection
try:
  with ip2location_connection() as conn:
    cursor = conn.cursor()
    cursor.execute("SELECT 1;")
    if cursor.fetchone():
      sys.exit(0)
except Exception as exc:
  sys.exit(-1)
END
}

until ip2location_db_ready; do
  echo "IP2Location database is unavailable - sleeping" 1>&2
  sleep 10
done

echo "IP2Location is up - continuing..." 1>&2

exec $cmd