#!/usr/bin/env python3
"""
從備份文件中恢復 Pub/Sub 消息。

用法：
    python restore_messages.py --backup-file pubsub_backups/pubsub_backup_20250508_123456.json --topic tagtoo-event-prod
    
    # 乾跑模式（不執行實際的發布操作）
    python restore_messages.py --backup-file pubsub_backups/pubsub_backup_20250508_123456.json --topic tagtoo-event-prod --dry-run
"""

import argparse
import json
import os
from google.cloud import pubsub_v1

def parse_args():
    parser = argparse.ArgumentParser(description='Restore Pub/Sub messages from backup')
    parser.add_argument('--backup-file', required=True, help='Backup file path')
    parser.add_argument('--topic', required=True, help='Topic ID to publish to')
    parser.add_argument('--project', default='tagtoo-tracking', help='GCP project ID')
    parser.add_argument('--dry-run', action='store_true', help='Dry run mode (do not publish)')
    parser.add_argument('--limit', type=int, default=None, help='Limit number of messages to restore')
    return parser.parse_args()

def load_backup(backup_file):
    """從備份文件中加載消息"""
    if not os.path.exists(backup_file):
        raise FileNotFoundError(f"備份文件不存在：{backup_file}")
    
    with open(backup_file, 'r') as f:
        messages = json.load(f)
    
    print(f"從備份文件 {backup_file} 中加載了 {len(messages)} 條消息")
    return messages

def restore_messages(project, topic, messages, dry_run=False, limit=None):
    """將消息恢復到 Pub/Sub 主題"""
    if dry_run:
        print("乾跑模式，不會實際發布消息")
        for i, message in enumerate(messages[:5]):
            print(f"消息 {i+1}：ID={message['message_id']}, 屬性={message['attributes']}")
        if len(messages) > 5:
            print(f"... 還有 {len(messages) - 5} 條消息")
        return
    
    publisher = pubsub_v1.PublisherClient()
    topic_path = publisher.topic_path(project, topic)
    
    # 限制消息數量
    if limit is not None and limit < len(messages):
        messages = messages[:limit]
        print(f"限制恢復 {limit} 條消息")
    
    futures = []
    for message in messages:
        # 準備數據
        if isinstance(message['data'], dict):
            data = json.dumps(message['data']).encode('utf-8')
        elif isinstance(message['data'], str):
            data = message['data'].encode('utf-8')
        else:
            print(f"警告：無法處理的數據類型：{type(message['data'])}")
            continue
        
        # 發布消息
        future = publisher.publish(
            topic_path,
            data=data,
            **message['attributes']
        )
        futures.append(future)
    
    # 等待所有消息發布完成
    for i, future in enumerate(futures):
        try:
            message_id = future.result()
            if i % 100 == 0 or i == len(futures) - 1:
                print(f"已發布 {i+1}/{len(futures)} 條消息，最新消息 ID：{message_id}")
        except Exception as e:
            print(f"發布消息時出錯：{e}")
    
    print(f"成功發布 {len(futures)} 條消息到主題 {topic}")

def main():
    args = parse_args()
    
    # 加載備份
    messages = load_backup(args.backup_file)
    
    # 恢復消息
    restore_messages(args.project, args.topic, messages, args.dry_run, args.limit)

if __name__ == '__main__':
    main()
