from enum import Enum, IntEnum
from typing import NamedTuple


class ChannelType(IntEnum):
    FRONTEND = 0
    BACKEND = 1


class EventAPIScope(str, Enum):
    FACEBOOK_CAPI = 'auth/event.facebook_capi'
    TAGTOO_EVENT = 'auth/event.tagtoo'
    PERMANENT = 'auth/permanent'


class EventDataScopeInfo(NamedTuple):
    name: str
    data_key: str


class EventDataScope(EventDataScopeInfo, Enum):
    FACEBOOK_CAPI = EventDataScopeInfo(EventAPIScope.FACEBOOK_CAPI.value, 'facebook')
    TAGTOO_EVENT = EventDataScopeInfo(EventAPIScope.TAGTOO_EVENT.value, 'tagtoo')
