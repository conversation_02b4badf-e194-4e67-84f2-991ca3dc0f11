#!/bin/bash
# verify_rollback.sh - BigQuery 雙寫入架構回滾驗證腳本
# 版本: v1.0
# 最後更新: 2025-09-03

set -e

# 配置變數
NAMESPACE="event-prod"
DEPLOYMENT="bq-writer-tagtoo-event-subscriber"
PROJECT_ID="tagtoo-tracking"
DATASET_ID="event_prod"
TABLE_ID="tagtoo_event"

# 顏色定義
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 檢查結果函數
check_result() {
    local test_name="$1"
    local result="$2"
    local expected="$3"
    
    if [ "$result" = "$expected" ]; then
        log_success "✅ $test_name: 通過"
        return 0
    else
        log_error "❌ $test_name: 失敗 (預期: $expected, 實際: $result)"
        return 1
    fi
}

# 功能驗證
verify_functionality() {
    log_info "🔍 開始功能驗證..."
    local errors=0
    
    # 檢查主要寫入功能
    log_info "檢查主要寫入功能..."
    
    # 檢查 Pod 狀態
    log_info "檢查 Pod 狀態..."
    local ready_pods=$(kubectl get pods -n $NAMESPACE -l app=$DEPLOYMENT --no-headers | grep "Running" | grep "1/1" | wc -l)
    local total_pods=$(kubectl get pods -n $NAMESPACE -l app=$DEPLOYMENT --no-headers | wc -l)
    
    if [ "$ready_pods" -eq "$total_pods" ] && [ "$total_pods" -gt 0 ]; then
        log_success "✅ Pod 狀態正常 ($ready_pods/$total_pods 運行中)"
    else
        log_error "❌ Pod 狀態異常 ($ready_pods/$total_pods 運行中)"
        ((errors++))
    fi
    
    # 檢查環境變數
    log_info "檢查環境變數..."
    local integrated_write_enabled=$(kubectl get deployment $DEPLOYMENT -n $NAMESPACE \
        -o jsonpath='{.spec.template.spec.containers[0].env[?(@.name=="INTEGRATED_WRITE_ENABLED")].value}' 2>/dev/null || echo "not_found")
    
    if [ "$integrated_write_enabled" = "false" ] || [ "$integrated_write_enabled" = "not_found" ]; then
        log_success "✅ 雙寫入功能已停用 (INTEGRATED_WRITE_ENABLED: $integrated_write_enabled)"
    else
        log_error "❌ 雙寫入功能未正確停用 (INTEGRATED_WRITE_ENABLED: $integrated_write_enabled)"
        ((errors++))
    fi
    
    # 檢查 Pub/Sub 訊息處理
    log_info "檢查 Pub/Sub 訊息處理..."
    local recent_logs=$(kubectl logs deployment/$DEPLOYMENT -n $NAMESPACE --tail=50 --since=5m 2>/dev/null || echo "")
    
    if echo "$recent_logs" | grep -q "Successfully processed\|Message processed\|ACK"; then
        log_success "✅ Pub/Sub 訊息處理正常"
    else
        log_warn "⚠️ 無法確認 Pub/Sub 訊息處理狀態（可能是正常的低流量期間）"
    fi
    
    # 檢查錯誤率
    log_info "檢查錯誤率..."
    local error_logs=$(kubectl logs deployment/$DEPLOYMENT -n $NAMESPACE --tail=100 --since=10m 2>/dev/null | grep -i "error\|exception\|failed" | wc -l)
    
    if [ "$error_logs" -lt 5 ]; then
        log_success "✅ 錯誤率正常 (最近 10 分鐘內 $error_logs 個錯誤)"
    else
        log_warn "⚠️ 錯誤率偏高 (最近 10 分鐘內 $error_logs 個錯誤)"
        ((errors++))
    fi
    
    return $errors
}

# 資源驗證
verify_resources() {
    log_info "🔧 開始資源驗證..."
    local errors=0
    
    # 檢查 CPU 和記憶體使用
    log_info "檢查資源使用..."
    
    # 獲取資源請求配置
    local cpu_request=$(kubectl get deployment $DEPLOYMENT -n $NAMESPACE \
        -o jsonpath='{.spec.template.spec.containers[0].resources.requests.cpu}' 2>/dev/null || echo "unknown")
    local memory_request=$(kubectl get deployment $DEPLOYMENT -n $NAMESPACE \
        -o jsonpath='{.spec.template.spec.containers[0].resources.requests.memory}' 2>/dev/null || echo "unknown")
    
    log_info "CPU 請求: $cpu_request"
    log_info "記憶體請求: $memory_request"
    
    # 檢查實際資源使用（如果 metrics-server 可用）
    if kubectl top pods -n $NAMESPACE -l app=$DEPLOYMENT &>/dev/null; then
        log_info "當前資源使用情況:"
        kubectl top pods -n $NAMESPACE -l app=$DEPLOYMENT
        log_success "✅ 資源使用情況已顯示"
    else
        log_warn "⚠️ 無法獲取資源使用情況（metrics-server 可能不可用）"
    fi
    
    # 檢查 Pod 數量
    local current_replicas=$(kubectl get deployment $DEPLOYMENT -n $NAMESPACE \
        -o jsonpath='{.status.readyReplicas}' 2>/dev/null || echo "0")
    local desired_replicas=$(kubectl get deployment $DEPLOYMENT -n $NAMESPACE \
        -o jsonpath='{.spec.replicas}' 2>/dev/null || echo "0")
    
    if [ "$current_replicas" -eq "$desired_replicas" ] && [ "$current_replicas" -gt 0 ]; then
        log_success "✅ Pod 數量正常 ($current_replicas/$desired_replicas)"
    else
        log_error "❌ Pod 數量異常 ($current_replicas/$desired_replicas)"
        ((errors++))
    fi
    
    return $errors
}

# 監控驗證
verify_monitoring() {
    log_info "📊 開始監控驗證..."
    local errors=0
    
    # 檢查最近日誌
    log_info "檢查最近日誌..."
    local recent_logs=$(kubectl logs deployment/$DEPLOYMENT -n $NAMESPACE --tail=20 --since=5m 2>/dev/null || echo "")
    
    if [ -n "$recent_logs" ]; then
        log_success "✅ 日誌正常產生"
        
        # 檢查是否有嚴重錯誤
        if echo "$recent_logs" | grep -qi "fatal\|critical\|panic"; then
            log_error "❌ 發現嚴重錯誤日誌"
            echo "$recent_logs" | grep -i "fatal\|critical\|panic"
            ((errors++))
        else
            log_success "✅ 無嚴重錯誤日誌"
        fi
    else
        log_warn "⚠️ 最近 5 分鐘內無日誌產生（可能是正常的低活動期間）"
    fi
    
    # 檢查部署狀態
    log_info "檢查部署狀態..."
    local deployment_status=$(kubectl get deployment $DEPLOYMENT -n $NAMESPACE \
        -o jsonpath='{.status.conditions[?(@.type=="Available")].status}' 2>/dev/null || echo "Unknown")
    
    if [ "$deployment_status" = "True" ]; then
        log_success "✅ 部署狀態正常"
    else
        log_error "❌ 部署狀態異常: $deployment_status"
        ((errors++))
    fi
    
    return $errors
}

# 資料完整性驗證
verify_data_integrity() {
    log_info "🗄️ 開始資料完整性驗證..."
    local errors=0
    
    # 檢查 BigQuery 表格狀態
    log_info "檢查 BigQuery tagtoo_event 表格..."
    
    if bq show $PROJECT_ID:$DATASET_ID.$TABLE_ID &>/dev/null; then
        log_success "✅ tagtoo_event 表格存在且可存取"
        
        # 檢查最近的資料寫入
        local recent_count=$(bq query --use_legacy_sql=false --format=csv --max_rows=1 \
            "SELECT COUNT(*) as count FROM \`$PROJECT_ID.$DATASET_ID.$TABLE_ID\` 
             WHERE event_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 HOUR)" 2>/dev/null | tail -n 1 || echo "0")
        
        if [ "$recent_count" -gt 0 ]; then
            log_success "✅ 最近 1 小時內有 $recent_count 筆資料寫入"
        else
            log_warn "⚠️ 最近 1 小時內無資料寫入（可能是正常的低流量期間）"
        fi
    else
        log_error "❌ 無法存取 tagtoo_event 表格"
        ((errors++))
    fi
    
    # 檢查 integrated_event 表格狀態（如果存在）
    log_info "檢查 integrated_event 表格狀態..."
    if bq show $PROJECT_ID:$DATASET_ID.integrated_event &>/dev/null; then
        log_info "integrated_event 表格仍然存在"
        
        # 檢查是否還有新的寫入
        local integrated_recent_count=$(bq query --use_legacy_sql=false --format=csv --max_rows=1 \
            "SELECT COUNT(*) as count FROM \`$PROJECT_ID.$DATASET_ID.integrated_event\` 
             WHERE event_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 10 MINUTE)" 2>/dev/null | tail -n 1 || echo "0")
        
        if [ "$integrated_recent_count" -eq 0 ]; then
            log_success "✅ integrated_event 表格無新寫入（雙寫入已停用）"
        else
            log_warn "⚠️ integrated_event 表格仍有新寫入 ($integrated_recent_count 筆)，雙寫入可能未完全停用"
            ((errors++))
        fi
    else
        log_info "integrated_event 表格不存在（已刪除或從未建立）"
    fi
    
    return $errors
}

# 生成驗證報告
generate_report() {
    local total_errors=$1
    
    echo
    echo "=========================================="
    echo "🔍 BigQuery 雙寫入架構回滾驗證報告"
    echo "=========================================="
    echo "驗證時間: $(date)"
    echo "命名空間: $NAMESPACE"
    echo "部署名稱: $DEPLOYMENT"
    echo
    
    if [ $total_errors -eq 0 ]; then
        log_success "🎉 所有驗證項目通過！回滾成功完成。"
        echo
        echo "✅ 功能驗證: 通過"
        echo "✅ 資源驗證: 通過"
        echo "✅ 監控驗證: 通過"
        echo "✅ 資料完整性驗證: 通過"
        echo
        echo "🚀 系統已恢復到回滾前的穩定狀態。"
    else
        log_error "❌ 發現 $total_errors 個問題，需要進一步檢查。"
        echo
        echo "建議採取的行動:"
        echo "1. 檢查上述錯誤訊息"
        echo "2. 查看詳細的 Pod 日誌"
        echo "3. 確認網路和資源配置"
        echo "4. 如有需要，聯絡技術支援團隊"
    fi
    
    echo
    echo "=========================================="
}

# 主函數
main() {
    echo "🔍 BigQuery 雙寫入架構回滾驗證腳本"
    echo "版本: v1.0"
    echo "開始時間: $(date)"
    echo

    # 檢查必要工具
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl 未安裝或不在 PATH 中"
        exit 1
    fi
    
    if ! command -v bq &> /dev/null; then
        log_error "bq CLI 未安裝或不在 PATH 中"
        exit 1
    fi

    # 檢查 kubectl 連線
    if ! kubectl cluster-info &> /dev/null; then
        log_error "無法連接到 Kubernetes 叢集"
        exit 1
    fi

    local total_errors=0

    # 執行各項驗證
    verify_functionality
    total_errors=$((total_errors + $?))

    verify_resources
    total_errors=$((total_errors + $?))

    verify_monitoring
    total_errors=$((total_errors + $?))

    verify_data_integrity
    total_errors=$((total_errors + $?))

    # 生成報告
    generate_report $total_errors

    # 返回適當的退出碼
    if [ $total_errors -eq 0 ]; then
        exit 0
    else
        exit 1
    fi
}

# 執行主函數
main "$@"
