#!/bin/bash

source ./functions.sh

ec_id=9999
batch_subscription_path="projects/tagtoo-tracking/subscriptions/batch"
capi_compressed_topic_path="projects/tagtoo-tracking/topics/capi-compressed"
test_data_path=${1:-"./capi-compressed-v1.json"}
test_timeout=${2:-10}

echo -n " > Init config data in config database ... "

if init_config_data "$ec_id"; then success '[OK]'; else error '[ERROR]'; fi

echo -n " > Update config data from config API ..."

if update_s2s_db; then success '[OK]'; else error '[ERROR]'; fi

echo -n " > Copy test data into s2s container ... "

cp "$test_data_path" ../../s2s/test_data.$$

function test_data_exists(){
  docker exec -i s2s sh -c "[ -f /app/test_data.$$ ] && exit 0 || exit 1"
}

if test_data_exists; then success '[OK]'; else error '[ERROR]'; fi

trap "exit 1" HUP INT PIPE QUIT TERM
trap "rm ../../s2s/test_data.$$" EXIT

echo -n " > Purge subscription $batch_subscription_path ... "

if purge_subscription "$batch_subscription_path"; then success '[OK]'; else error '[ERROR]'; fi

echo -n " > Publish test data into topic $capi_compressed_topic_path ... "

if publish_test_data "/app/test_data.$$" "$capi_compressed_topic_path" "v1" "$ec_id"; then success '[OK]'; else error '[ERROR]'; fi

echo -n " > Check subscription $batch_subscription_path is empty ... "

sleep "$test_timeout"

if check_subscription_message_count "$batch_subscription_path" 0; then success '[PASSED]'; else error '[ERROR]'; fi
