# 測試 MkDocs 構建流程
name: Test MkDocs Build

on:
  pull_request:
    branches:
      - master
    paths:
      - '.github/workflows/mkdocs-gh_deploy.yml'
      - '.github/workflows/test-mkdocs-build.yml'
      - 'docs/**'

jobs:
  test-mkdocs-build:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: docs/
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: 3.9
      - uses: actions/cache@v4
        with:
          key: ${{ github.ref }}
          path: .cache
      - run: pip install pdm
      - run: pdm install --no-self
      - name: Test MkDocs Build
        run: pdm run mkdocs build --strict
