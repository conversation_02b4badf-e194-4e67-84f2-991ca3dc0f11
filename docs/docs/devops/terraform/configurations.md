Cloud resources in this project are defined and manage by [Terraform](https://www.terraform.io/).

## State

First important thing to know is [Configuration State](https://www.terraform.io/docs/language/state/index.html). In
this project, the state file was store in a GCS bucket defines at:

!!! Reference "deploy/backends.tf"
    ```hcl hl_lines="3"
    --8<-- "../deploy/backends.tf"
    ```

This bucket should be protected carefully.

## Var Files

In our configuration templates, if a resource setting is customizable, we use pre-defined input variable.

For instance, if we want to run a Dataflow instance with a customizable machine type:

```hcl
variable "my_dataflow_machine_type" {
  type = string
}

resource "google_dataflow_job" "my_dataflow" {
  ...
  machine_type = var.my_dataflow_machine_type
  ...
}
```

## Split Environments by Workspaces

A workspace is much like a namespace. In Terraform, it means separated resource configuration by point to different State.

For example, if we create resource in two different workspaces with exact same configuration, to Terraform, it means two
different resource that managed my two state. In this case, if they are created in the same GCP project, the creation
of the secondary resource might fail due to the name conflict.

In order to prevent naming conflict when splitting workspaces in the same project, the resource name should postfix with
the workspace name:

```
resource-name-${terraform.workspace}
```

There are two main workspace in this project:

1. `prod`: The production environment
2. `dev`: The staging environment

Please make sure you're in the right workspace before you modify any resource.

It's recommended to destroy all staging resource after testing to decrease cost.

To apply resources to workspace, use a specific value file for it. Provide the value file by argument `-var-file`, for
example, in `prod` workspace:

```
terraform apply -var-file=prod.tfvars
```

## Conventions

* All variables in this project should be defined in `vars.tf` file as a convention.
* Resource naming convention should be lowercase single letters separated with underscores.
* File name of input value files to applying to specific workspace should be `<workspace name>.tfvars`.
* Collect the variable into object-like structure when they are used in the same or related resource.

```hcl
# Instead of define separately
variable "my_pod_cpu_request" {
  type = string
}
variable "my_pod_cpu_limit" {
  type = string
}

# Use object type to group them together
variable "my_pod" {
  type = object({
    cpu_request = string
    cpu_limit = string
  })
}
```

* Don't repeat the resource type in their names.

```hcl
# Instead of:
resource "kubernetes_deployment_v1" "my_app_deployment" {
  ...
}
resource "kubernetes_horizontal_pod_autoscaler_v2" "my_app_hpa" {
  ...
}

# Just keep it simple
resource "kubernetes_deployment_v1" "my_app" {
  ...
}
resource "kubernetes_horizontal_pod_autoscaler_v2" "my_app" {
  ...
}
```
