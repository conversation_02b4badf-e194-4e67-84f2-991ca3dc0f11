import logging

import orjson
from google.cloud import pubsub

from app.config import settings
from app.services.exceptions import PubSubServiceError

logger = logging.getLogger(__file__)


class GoogleCloudPubSubPublisher(object):
    def __init__(
        self,
        project: str = None,
        credential_file: str = None,
    ):
        self.credential_file = credential_file
        self.project = project or settings.GCP_PROJECT_NAME
        self._client = None

    @property
    def client(self):
        if not self._client:
            if self.credential_file:
                self._client = pubsub.PublisherClient.from_service_account_file(
                    self.credential_file
                )
            else:
                self._client = pubsub.PublisherClient()
        return self._client

    def publish(
        self,
        topic_name: str,
        message: dict,
        attrs: dict = None,
    ):
        attrs = attrs or {}
        self.topic = self.client.topic_path(self.project, topic_name)
        message = orjson.dumps(message)
        future = self.client.publish(self.topic, message, **attrs)

        try:
            message_id = future.result()
        except Exception as e:
            raise PubSubServiceError(
                project=self.project,
                topic_name=topic_name,
                published_message=message,
                attrs=attrs,
                origin_e=e,
            )
        else:
            if settings.DEBUG:
                logger.info(
                    'Message is published successfully: \n'
                    f'topic_name: {topic_name}\n'
                    f'message: {message}\n'
                    f'attrs: {attrs}\n'
                )
            return message_id
