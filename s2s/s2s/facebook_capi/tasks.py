import json
from operator import itemgetter
from .. import amqp
from .publishers import FacebookCAPIPublisherFailedBatchDecompressed


__all__ = ['failed_batch_handler']


def failed_batch_handler():
    target_queue = 'facebook_capi_failed_batch'

    def callback(ch, method, properties, body):
        result = json.loads(body.decode())
        data, version, pixel_id, access_token = itemgetter(
            'data', 'version', 'pixel_id', 'access_token'
        )(result)
        print(f"Received failed batch, size: {len(data)}")
        # Publish decompressed events to topic
        with FacebookCAPIPublisherFailedBatchDecompressed(
            max_messages=1000
        ) as publisher:
            for event in data:
                publisher.add_message(
                    data=json.dumps(event),
                    version=str(version),
                    pixel_id=str(pixel_id),
                    access_token=str(access_token),
                )
        ch.basic_ack(delivery_tag=method.delivery_tag)

    with amqp.channel_scope() as channel:
        channel.basic_qos(prefetch_count=1)
        channel.basic_consume(target_queue, callback)
        print(f"Starting consuming task queue {target_queue} ...")
        channel.start_consuming()
