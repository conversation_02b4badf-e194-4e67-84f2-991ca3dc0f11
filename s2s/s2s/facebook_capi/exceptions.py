import json


class EcommercePixelDoesNotExist(Exception):
    """The requested ecommerce pixel does not exists."""

    def __str__(self) -> str:
        return f'{self.__class__.__name__}: {super().__str__()}'


class FacebookPixelDatumInvaild(Exception):
    """
    The Facebook pixel datum is invalid.
    """

    def __init__(self, payload=None) -> None:
        self.payload = payload

    def __str__(self) -> str:
        return f'{self.__class__.__name__}, Payload: {self.payload}'


class FacebookError(Exception):
    """
    All errors specific to Facebook api requests and Facebook ads design will be
    subclassed from FacebookError which is subclassed from Exception.
    """

    pass


class FacebookApiConnectTimeout(FacebookError):
    """
    The request to Facebook api timed out.
    """

    pass


class FacebookRequestError(FacebookError):
    """
    Raised when an api request fails. Returned by error() method on a
    FacebookResponse object returned through a callback function (relevant
    only for failure callbacks) if not raised at the core api call method.
    """

    def __init__(
        self,
        http_status,
        body,
    ):
        self._http_status = http_status
        try:
            self.body = json.loads(body)
        except (TypeError, ValueError):
            self.body = body

        self.message = None
        self.type = None
        self.code = None
        self.is_transient = False
        self.error_subcode = None
        self.error_user_title = None
        self.error_user_msg = None

        if self.body and 'error' in self.body:
            self.error = self.body['error']
            if 'message' in self.error:
                self.message = self.error['message']
            if 'type' in self.error:
                self.type = self.error['type']
            if 'code' in self.error:
                self.code = self.error['code']
            if 'error_subcode' in self.error:
                self.error_subcode = self.error['error_subcode']
            if 'is_transient' in self.error:
                self.is_transient = self.error['is_transient']
            if 'error_user_title' in self.error:
                self.error_user_title = self.error['error_user_title']
            if 'error_user_msg' in self.error:
                self.error_user_msg = self.error['error_user_msg']

        else:
            self.error = None

    @property
    def format_error_user_message(self):
        if self.error_user_msg:
            return f'{self.error_user_title}({self.error_user_msg})'
        return self.error_user_title

    def __str__(self):
        msg = f'FacebookRequestError: {self.format_error_user_message or self.message}'
        if self.code:
            msg += f' [code {self.code}]'
        if self.error_subcode:
            msg += f' [subcode {self.error_subcode}]'
        return msg
