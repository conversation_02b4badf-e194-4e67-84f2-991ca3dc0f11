<script src="https://unpkg.com/mermaid@8.13.3/dist/mermaid.min.js"></script>

## Transactions

The BigTable only support single-row transaction. The operation of an Entity in user unify contains multiple cross-tables
and cross-rows operations. 

The whole operation might break at any point or experiencing a race condition. The operation should be retry-able without
side effects. Therefore, break down the operation into 4 transactions and use 4 simple locks to control the transaction states.

**Transaction 1**

Make sure Entity and Permanent row are created and the `emails` and `mobiles` fields are patched.

**Transaction 2**

Make sure the attributes are created across lookup tables {email, mobile...} which point to entity's permanent.

**Transaction 3**

Doubly link permanents base on lookup results.

**Transaction 4**

Traversal the graph from entity's permanent, sort the graph to find the root node, then update to group pointer for all nodes.

## Performance

|transaction|best case|worst case|
|---|---|---|
|tx1|O(3+1)|O(5+1), when entity both has a email and a mobile|
|tx2|O(1)|O(5+1), when entity has all attributes|
|tx3|O(1)|O(2 * K + 1) ~= O(K)|
|tx4|O(1)|O(V + E + logV + V + 1) ~= O(V)|

* `K`: All permanent edges found from lookup tables
* `V`: The count of permanents in the group


## Flowchart

```mermaid
stateDiagram-v2
    state tx1 <<choice>>
    state tx2 <<choice>>
    state tx3 <<choice>>
    state tx4 <<choice>>
    [*] --> EntityTable
    PutEntity --> GetPermanent
    PutMobiles --> PutLookupTablePermanents
    tx1 --> GetPermanent: tx1=N
    tx2 --> PutLookupTablePermanents: tx2=N
    tx3 --> GetLookupTablePermanents: tx3=N
    tx4 --> Traversal: tx4=N
    tx4 --> [*]
    GetLookupTablePermanents --> PutPermanentPermanents
    state EntityTable {
        state is_entity_exists <<choice>>
        [*] --> getEntity
        getEntity --> is_entity_exists
        is_entity_exists --> tx1: exists
        is_entity_exists --> PutEntity : not exists
        tx1 --> tx2: tx1=Y
        tx2 --> tx3: tx2=Y
        tx3 --> tx4: tx3=Y
    }
    state PermanentTable {
        PutPermanentPermanents: PutPermanents
        [*] --> GetPermanent
        state is_permanent_exists <<choice>>
        GetPermanent --> is_permanent_exists
        is_permanent_exists --> PutEmails: exists
        is_permanent_exists --> PutPermanent : not exists
        PutPermanent --> PutEmails
        PutEmails --> PutMobiles
        note right of PutMobiles : When done, set Tx1 to Y
        PutPermanentPermanents --> Traversal
        note right of PutPermanentPermanents : When done, set Tx3 to Y
        Traversal --> Traversal
        Traversal --> SortPermanents
        SortPermanents --> PutGroup
        PutGroup --> PutGroup
        note right of PutGroup : When done, set Tx4 to Y
        PutGroup --> [*]
    }
    state LookupTables {
        PutLookupTablePermanents: PutPermanents
        GetLookupTablePermanents: GetPermanents
        [*] --> PutLookupTablePermanents
        note right of PutLookupTablePermanents : When done, set Tx2 to Y
        PutLookupTablePermanents --> GetLookupTablePermanents
    }
```