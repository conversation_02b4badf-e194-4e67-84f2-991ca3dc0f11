from app.config import settings


class Subscriber(object):
    def __init__(self, client):
        self._client = client
        self._subscriptions = {}

    def add_subscription(self, topic_name, subscription_name):
        sub_path = self._client.subscription_path(
            settings.GCP_PROJECT_NAME, subscription_name
        )
        topic_path = f'projects/{settings.GCP_PROJECT_NAME}/topics/{topic_name}'
        self._client.create_subscription(
            request={
                'topic': topic_path,
                'name': sub_path,
            }
        )
        self._subscriptions[topic_name] = sub_path

    def pull_message(
        self,
        topic_name: str,
        ack_messages: bool = True,
    ):
        sub_path = self._subscriptions[topic_name]
        response = self._client.pull(
            request={
                'subscription': sub_path,
                'max_messages': 1,
            },
            timeout=3,
        )

        ack_ids = [msg.ack_id for msg in response.received_messages]
        if ack_ids:
            if ack_messages:
                self._client.acknowledge(
                    request={
                        'subscription': sub_path,
                        'ack_ids': ack_ids,
                    }
                )
        return response

    def delete_subscription(self, topic_name):
        self._client.delete_subscription(
            request={'subscription': self._subscriptions[topic_name]},
        )
        del self._subscriptions[topic_name]

    def __getitem__(self, topic):
        return self._subscriptions[topic]
