steps:
  - id: pull_config_api_cache
    name: "gcr.io/cloud-builders/docker"
    entrypoint: "bash"
    args: ["-c", "docker pull ${_CONFIG_API_IMAGE}:${_CACHE_TAG} || exit 0"]
    waitFor: ["-"]

  - id: pull_api_cache
    name: "gcr.io/cloud-builders/docker"
    entrypoint: "bash"
    args: ["-c", "docker pull ${_API_IMAGE}:${_CACHE_TAG} || exit 0"]
    waitFor: ["-"]

  - id: pull_s2s_cache
    name: "gcr.io/cloud-builders/docker"
    entrypoint: "bash"
    args: ["-c", "docker pull ${_S2S_IMAGE}:${_CACHE_TAG} || exit 0"]
    waitFor: ["-"]

  - id: pull_bq_writer_cache
    name: "gcr.io/cloud-builders/docker"
    entrypoint: "bash"
    args: ["-c", "docker pull ${_BQ_WRITER_IMAGE}:${_CACHE_TAG} || exit 0"]
    waitFor: ["-"]

  - id: pull_ip2location_lite
    name: "gcr.io/cloud-builders/docker"
    entrypoint: "bash"
    args:
      [
        "-c",
        "docker pull ${_IP2LOCATION_LITE_IMAGE} && docker tag ${_IP2LOCATION_LITE_IMAGE} ad_track_ip2location",
      ]
    waitFor: ["-"]

  - id: build_config_api
    name: "gcr.io/cloud-builders/docker"
    dir: "config_api"
    args:
      [
        "build",
        "--platform=linux/amd64",
        "-t",
        "ad_track_config_api",
        "--cache-from",
        "${_CONFIG_API_IMAGE}:${_CACHE_TAG}",
        ".",
      ]
    waitFor: ["pull_config_api_cache"]

  - id: build_api
    name: "gcr.io/cloud-builders/docker"
    dir: "api"
    args:
      [
        "build",
        "--platform=linux/amd64",
        "-t",
        "ad_track_api",
        "--cache-from",
        "${_API_IMAGE}:${_CACHE_TAG}",
        ".",
      ]
    waitFor: ["pull_api_cache"]

  - id: build_s2s
    name: "gcr.io/cloud-builders/docker"
    args:
      [
        "build",
        "--platform=linux/amd64",
        "-f",
        "s2s/Dockerfile",
        "-t",
        "ad_track_s2s",
        "--cache-from",
        "${_S2S_IMAGE}:${_CACHE_TAG}",
        ".",
      ]
    waitFor: ["pull_s2s_cache"]

  - id: build_bq_writer
    name: "gcr.io/cloud-builders/docker"
    args:
      [
        "build",
        "--platform=linux/amd64",
        "-f",
        "bq_writer/Dockerfile",
        "-t",
        "ad_track_bq_writer",
        "--cache-from",
        "${_BQ_WRITER_IMAGE}:${_CACHE_TAG}",
        ".",
      ]
    waitFor: ["pull_bq_writer_cache"]

  - id: run_config_containers
    name: "docker/compose:1.24.1"
    args:
      [
        "-f",
        "docker-compose.config.yml",
        "up",
        "--no-build",
        "--scale",
        "proxy=0",
        "-d",
      ]
    env:
      - "COMPOSE_HTTP_TIMEOUT=200"
    waitFor: ["build_config_api"]

  - id: run_api_containers
    name: "docker/compose:1.24.1"
    args:
      [
        "-f",
        "docker-compose.api.yml",
        "up",
        "--no-build",
        "--scale",
        "proxy=0",
        "-d",
      ]
    env:
      - "COMPOSE_HTTP_TIMEOUT=200"
    waitFor: ["build_api", "run_config_containers"]

  - id: run_s2s_containers
    name: "docker/compose:1.24.1"
    args:
      [
        "-f",
        "docker-compose.s2s.yml",
        "up",
        "--no-build",
        "--scale",
        "proxy=0",
        "-d",
      ]
    env:
      - "COMPOSE_HTTP_TIMEOUT=200"
    waitFor: ["build_s2s", "run_config_containers"]

  - id: run_bq_writer_containers
    name: "gcr.io/cloud-builders/docker"
    entrypoint: "bash"
    args:
      - "-c"
      - |
        curl -L "https://github.com/docker/compose/releases/download/1.24.1/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        chmod +x /usr/local/bin/docker-compose
        ./run_bq_writer_services.sh -d
    env:
      - "COMPOSE_HTTP_TIMEOUT=200"
    waitFor: ["build_bq_writer", "pull_ip2location_lite"]

  - id: wait_for_services_ready
    name: "gcr.io/cloud-builders/docker"
    entrypoint: "bash"
    args:
      - "-c"
      - |
        for service in config-api api capi-sub-batch capi-sub-singleton capi-failed-batch-handler tagtoo-event-subscriber
        do
          until [ "$(docker inspect -f {{.State.Health.Status}} $service)" == "healthy" ]; do
              echo -n "Wait for $service health check ... " && echo $(docker logs $service --tail 1)
              sleep 3
          done
        done
    waitFor: ["run_api_containers", "run_s2s_containers"]
    timeout: 120s

  - id: test_api
    name: "gcr.io/cloud-builders/docker"
    dir: "tests/api"
    entrypoint: "bash"
    args: ["./test_all.sh"]
    timeout: 60s

  - id: test_s2s_batch
    name: "gcr.io/cloud-builders/docker"
    dir: "tests/s2s"
    entrypoint: "bash"
    args: ["./test_batch.sh"]
    timeout: 60s

  - id: test_s2s_failed_batch
    name: "gcr.io/cloud-builders/docker"
    dir: "tests/s2s"
    entrypoint: "bash"
    args: ["./test_failed_batch.sh"]
    timeout: 60s

  - id: test_s2s_lta
    name: "gcr.io/cloud-builders/docker"
    dir: "tests/s2s"
    entrypoint: "bash"
    args: ["./test_lta.sh"]
    timeout: 60s

  - id: test_bq_writer
    name: "gcr.io/cloud-builders/docker"
    dir: "tests/bq_writer"
    entrypoint: "bash"
    args: ["./test_all.sh"]
    timeout: 60s

timeout: 1800s
options:
  machineType: "N1_HIGHCPU_8"
substitutions:
  _CONFIG_API_IMAGE: asia.gcr.io/tagtoo-tracking/event-config-api
  _API_IMAGE: asia.gcr.io/tagtoo-tracking/event-api
  _S2S_IMAGE: asia.gcr.io/tagtoo-tracking/event-s2s
  _BQ_WRITER_IMAGE: asia.gcr.io/tagtoo-tracking/event-bq-writer
  _IP2LOCATION_LITE_IMAGE: asia.gcr.io/tagtoo-tracking/ip2location-mysql-lite
  _CACHE_TAG: dev-latest
