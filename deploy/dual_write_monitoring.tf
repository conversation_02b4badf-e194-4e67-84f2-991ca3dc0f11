# BigQuery 雙寫入架構監控配置
# 此檔案定義雙寫入架構的監控警報和儀表板

# 本地變數定義監控閾值
locals {
  dual_write_error_rate_threshold = var.dual_write_error_rate_threshold
  dual_write_latency_threshold    = var.dual_write_latency_threshold
}

# 雙寫入架構效能警報策略
resource "google_monitoring_alert_policy" "dual_write_performance_alert" {
  display_name = "BigQuery Dual-Write Performance Alert"
  combiner     = "OR"

  documentation {
    content = <<-EOT
      雙寫入架構效能警報：

      **檢查項目：**
      1. integrated_event 寫入錯誤率
      2. 資料轉換失敗情況
      3. 吞吐量和延遲指標
      4. 資源使用情況

      **處理步驟：**
      1. 檢查 bq-writer-tagtoo-event-subscriber Pod 日誌
      2. 確認 BigQuery 服務狀態
      3. 檢查資源使用率和 HPA 狀態
      4. 必要時調整 INTEGRATED_WRITE_SAMPLE_RATE
    EOT
    mime_type = "text/markdown"
  }

  conditions {
    display_name = "Integrated Event Write Error Rate High"

    condition_threshold {
      filter = "metric.type=\"custom.googleapis.com/bq_writer/transformation_result\" AND metric.labels.success=\"false\""
      comparison = "COMPARISON_GT"
      threshold_value = local.dual_write_error_rate_threshold  # 1%
      duration = "300s"

      trigger {
        count = 1
      }

      aggregations {
        alignment_period = "60s"
        per_series_aligner = "ALIGN_RATE"
        cross_series_reducer = "REDUCE_SUM"
      }
    }
  }

  conditions {
    display_name = "Dual Write Latency High"

    condition_threshold {
      filter = "metric.type=\"custom.googleapis.com/bq_writer/dual_write_latency\""
      comparison = "COMPARISON_GT"
      threshold_value = 100  # 100ms - 調整為更合理的閾值
      duration = "300s"

      trigger {
        count = 1
      }

      aggregations {
        alignment_period = "60s"
        per_series_aligner = "ALIGN_PERCENTILE_95"
        cross_series_reducer = "REDUCE_MEAN"
      }
    }
  }

  alert_strategy {
    auto_close = "604800s"  # 7 days
  }
}

# BigQuery 成本監控警報
resource "google_monitoring_alert_policy" "bigquery_cost_alert" {
  display_name = "BigQuery Daily Cost Alert"
  combiner     = "OR"

  documentation {
    content = <<-EOT
      BigQuery 日成本警報：

      **觸發條件：** 日成本超過 $60 USD

      **處理步驟：**
      1. 檢查 BigQuery 查詢使用量
      2. 確認雙寫入是否正常運作
      3. 檢查是否有異常的大量查詢
      4. 考慮調整 INTEGRATED_WRITE_SAMPLE_RATE
    EOT
    mime_type = "text/markdown"
  }

  conditions {
    display_name = "Daily BigQuery Cost Exceeds Budget"

    condition_threshold {
      filter = "metric.type=\"billing.googleapis.com/billing/total_cost\" AND resource.labels.service=\"BigQuery\""
      comparison = "COMPARISON_GT"
      threshold_value = 60  # $60 USD
      duration = "0s"

      trigger {
        count = 1
      }

      aggregations {
        alignment_period = "86400s"  # 24 hours
        per_series_aligner = "ALIGN_SUM"
        cross_series_reducer = "REDUCE_SUM"
      }
    }
  }

  alert_strategy {
    auto_close = "86400s"  # 1 day
  }
}

# 資源使用率警報
resource "google_monitoring_alert_policy" "resource_utilization_alert" {
  display_name = "BQ Writer Resource Utilization Alert"
  combiner     = "OR"

  documentation {
    content = <<-EOT
      BQ Writer 資源使用率警報：

      **監控項目：**
      - 容器記憶體使用率 > 85%
      - Pod 數量接近上限 (> 35)
      - 節點記憶體使用率 > 90%

      **處理步驟：**
      1. 檢查 Pod 資源使用情況
      2. 確認 HPA 是否正常運作
      3. 檢查節點池資源狀態
      4. 必要時調整資源請求或限制
    EOT
    mime_type = "text/markdown"
  }

  conditions {
    display_name = "High Memory Usage"

    condition_threshold {
      filter = "metric.type=\"kubernetes.io/container/memory/used_bytes\" AND resource.labels.container_name=\"bq-writer-tagtoo-event-subscriber\""
      comparison = "COMPARISON_GT"
      threshold_value = 0.85  # 85%
      duration = "300s"

      trigger {
        count = 1
      }

      aggregations {
        alignment_period = "60s"
        per_series_aligner = "ALIGN_MEAN"
        cross_series_reducer = "REDUCE_MEAN"
      }
    }
  }

  conditions {
    display_name = "Pod Readiness Issues"

    condition_threshold {
      filter = "metric.type=\"kubernetes.io/pod/ready_condition\" AND resource.labels.pod_name=~\"bq-writer-tagtoo-event-subscriber.*\""
      comparison = "COMPARISON_LT"
      threshold_value = 1  # Pod 不在 Ready 狀態
      duration = "300s"

      trigger {
        count = 1
      }

      aggregations {
        alignment_period = "60s"
        per_series_aligner = "ALIGN_MEAN"
        cross_series_reducer = "REDUCE_SUM"
      }
    }
  }

  notification_channels = [
    google_monitoring_notification_channel.email_notification.name
  ]

  alert_strategy {
    auto_close = "86400s"  # 1 day
  }
}

# 資料一致性監控警報
resource "google_monitoring_alert_policy" "data_consistency_alert" {
  display_name = "Data Consistency Alert"
  combiner     = "OR"

  documentation {
    content = <<-EOT
      資料一致性警報：

      **監控項目：**
      - tracking_id 碰撞率異常
      - raw_json 大小超過目標
      - 資料轉換失敗率過高

      **處理步驟：**
      1. 檢查 tracking_id 生成邏輯
      2. 確認 raw_json 最佳化是否正常
      3. 檢查資料轉換器錯誤日誌
      4. 驗證兩表資料一致性
    EOT
    mime_type = "text/markdown"
  }

  conditions {
    display_name = "Raw JSON Size Too Large"

    condition_threshold {
      filter = "metric.type=\"custom.googleapis.com/bq_writer/raw_json_size\""
      comparison = "COMPARISON_GT"
      threshold_value = 500  # 500 bytes
      duration = "300s"

      trigger {
        count = 1
      }

      aggregations {
        alignment_period = "60s"
        per_series_aligner = "ALIGN_PERCENTILE_95"
        cross_series_reducer = "REDUCE_MEAN"
      }
    }
  }

  alert_strategy {
    auto_close = "86400s"  # 1 day
  }
}

# 通知渠道配置（需要根據實際環境調整）
resource "google_monitoring_notification_channel" "email_notification" {
  display_name = "BQ Writer Team Email"
  type         = "email"

  labels = {
    email_address = "<EMAIL>"
  }
}

# 將通知渠道整合到主要警報策略中
# 注意：這個修改需要在原有的 dual_write_performance_alert 資源中加入 notification_channels

# 自定義指標描述符
resource "google_monitoring_metric_descriptor" "dual_write_latency" {
  type         = "custom.googleapis.com/bq_writer/dual_write_latency"
  metric_kind  = "GAUGE"
  value_type   = "DOUBLE"
  display_name = "Dual Write Latency"
  description  = "雙寫入操作延遲時間（毫秒）"

  labels {
    key         = "success"
    value_type  = "STRING"
    description = "操作是否成功"
  }
}

resource "google_monitoring_metric_descriptor" "transformation_result" {
  type         = "custom.googleapis.com/bq_writer/transformation_result"
  metric_kind  = "CUMULATIVE"
  value_type   = "INT64"
  display_name = "Transformation Result"
  description  = "資料轉換結果計數"

  labels {
    key         = "success"
    value_type  = "STRING"
    description = "轉換是否成功"
  }

  labels {
    key         = "error_type"
    value_type  = "STRING"
    description = "錯誤類型（如果失敗）"
  }
}

resource "google_monitoring_metric_descriptor" "raw_json_size" {
  type         = "custom.googleapis.com/bq_writer/raw_json_size"
  metric_kind  = "GAUGE"
  value_type   = "INT64"
  display_name = "Raw JSON Size"
  description  = "raw_json 欄位大小（位元組）"
}
