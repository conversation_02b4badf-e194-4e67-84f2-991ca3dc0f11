# https://www.terraform.io/docs/providers/google/r/pubsub_topic.html
resource "google_pubsub_topic" "facebook_capi" {
  name       = "facebook-capi-${var.environment}"
  depends_on = [google_project_service.gcp_services]
  labels = {
    environment = var.environment
  }
}
resource "google_pubsub_topic" "facebook_capi_compressed" {
  name       = "facebook-capi-compressed-${var.environment}"
  depends_on = [google_project_service.gcp_services]
  labels = {
    environment = var.environment
  }
}
resource "google_pubsub_topic" "facebook_capi_s2s_failed_batch_decompressed" {
  name       = "facebook-capi-s2s-failed-batch-decompressed-${var.environment}"
  depends_on = [google_project_service.gcp_services]
  labels = {
    environment = var.environment
  }
}
resource "google_pubsub_topic" "lta" {
  name       = "lta-${var.environment}"
  depends_on = [google_project_service.gcp_services]
  labels = {
    environment = var.environment
  }
}
resource "google_pubsub_topic" "lta_facebook_capi_batch" {
  name       = "lta-facebook-capi-batch-${var.environment}"
  depends_on = [google_project_service.gcp_services]
  labels = {
    environment = var.environment
  }
}
resource "google_pubsub_topic" "tagtoo_event" {
  name       = "tagtoo-event-${var.environment}"
  depends_on = [google_project_service.gcp_services]
  labels = {
    environment = var.environment
  }
}

resource "google_pubsub_topic" "tagtoo_event_to_bigquery_deadletter" {
  name       = "tagtoo-event-to-bigquery-deadletter-${terraform.workspace}"
  depends_on = [google_project_service.gcp_services]
  labels = {
    environment = terraform.workspace
  }
}

# https://cloud.google.com/pubsub/docs/access-control#permissions_and_roles
# https://www.terraform.io/docs/providers/google/r/pubsub_topic_iam.html
resource "google_pubsub_topic_iam_binding" "facebook_capi_iam_publisher_bind" {
  topic   = google_pubsub_topic.facebook_capi.name
  role    = "roles/pubsub.publisher"
  members = var.pub_sub_topic_facebook_capi.publishers
}

resource "google_pubsub_topic_iam_binding" "lta_facebook_capi_iam_publisher_bind" {
  topic   = google_pubsub_topic.lta.name
  role    = "roles/pubsub.publisher"
  members = var.pub_sub_topic_lta.publishers
}

resource "google_pubsub_topic_iam_binding" "lta_facebook_capi_batch_iam_publisher_bind" {
  topic   = google_pubsub_topic.lta_facebook_capi_batch.name
  role    = "roles/pubsub.publisher"
  members = var.pub_sub_topic_lta_facebook_capi_batch.publishers
}

# https://www.terraform.io/docs/providers/google/r/pubsub_subscription.html
resource "google_pubsub_subscription" "facebook_capi_s2s_to_compress" {
  name  = "facebook-capi-s2s-to-compress-${var.environment}"
  topic = google_pubsub_topic.facebook_capi.name
  labels = {
    environment = var.environment
    component   = "event-s2s"
    consumer    = "dataflow"
    destination = "pubsub-topic"
  }
  message_retention_duration = "604800s" # 7 day
  retain_acked_messages      = false
  ack_deadline_seconds       = 60
  enable_message_ordering    = false
}

resource "google_pubsub_subscription" "facebook_capi_s2s_batch_subs" {
  for_each = toset(var.event_api.api_versions)

  name  = "facebook-capi-s2s-batch-${each.key}-${var.environment}"
  topic = google_pubsub_topic.facebook_capi_compressed.name
  labels = {
    environment = var.environment
    component   = "event-s2s"
    destination = "facebook"
    consumer    = "event-s2s-subscriber"
  }
  filter                     = "attributes.version=\"${each.key}\""
  message_retention_duration = "604800s" # 7 day
  retain_acked_messages      = false
  ack_deadline_seconds       = 60
  enable_message_ordering    = false
}
resource "google_pubsub_subscription" "facebook_capi_s2s_singleton_subs" {
  for_each = toset(var.event_api.api_versions)

  name  = "facebook-capi-s2s-singleton-${each.key}-${var.environment}"
  topic = google_pubsub_topic.facebook_capi_s2s_failed_batch_decompressed.name
  labels = {
    environment = var.environment
    component   = "event-s2s"
    destination = "facebook"
    consumer    = "event-s2s-subscriber"
  }
  filter                     = "attributes.version=\"${each.key}\""
  message_retention_duration = "604800s" # 7 day
  retain_acked_messages      = false
  ack_deadline_seconds       = 60
  enable_message_ordering    = false
}
resource "google_pubsub_subscription" "lta_subs" {
  for_each = toset(var.event_api.api_versions)

  name  = "lta-${each.key}-${var.environment}"
  topic = google_pubsub_topic.lta.name
  labels = {
    environment = var.environment
    component   = "event-s2s"
    destination = "facebook"
    consumer    = "event-s2s-lta-subscriber"
  }
  message_retention_duration = "604800s" # 7 day
  retain_acked_messages      = false
  ack_deadline_seconds       = 60
  enable_message_ordering    = false
}

resource "google_pubsub_subscription" "lta_facebook_capi_s2s_batch_subs" {
  for_each = toset(var.event_api.api_versions)

  name  = "lta-facebook-capi-s2s-batch-${each.key}-${var.environment}"
  topic = google_pubsub_topic.lta_facebook_capi_batch.name
  labels = {
    environment = var.environment
    component   = "event-s2s"
    destination = "facebook"
    consumer    = "event-s2s-lta-subscriber"
  }
  filter                     = "attributes.version=\"${each.key}\""
  message_retention_duration = "604800s" # 7 day
  retain_acked_messages      = false
  ack_deadline_seconds       = 60
  enable_message_ordering    = false
}
resource "google_pubsub_subscription" "tagtoo_event_to_bigquery_subs" {
  for_each = toset(var.event_api.api_versions)

  name  = "tagtoo-event-to-bigquery-${each.key}-${var.environment}"
  topic = google_pubsub_topic.tagtoo_event.name
  labels = {
    environment = var.environment
    component   = "tagtoo-event"
    consumer    = "bq-writer-subscriber"
    destination = "bigquery"
  }
  filter                     = "attributes.version=\"${each.key}\""
  message_retention_duration = "604800s" # 7 day
  retain_acked_messages      = false
  ack_deadline_seconds       = 60
  enable_message_ordering    = false

  dead_letter_policy {
    dead_letter_topic     = google_pubsub_topic.tagtoo_event_to_bigquery_deadletter.id
    max_delivery_attempts = 5
  }
}

resource "google_pubsub_subscription" "tagtoo_event_to_bigquery_deadletter_sub" {
  name  = "tagtoo-event-to-bigquery-deadletter-sub-${terraform.workspace}"
  topic = google_pubsub_topic.tagtoo_event_to_bigquery_deadletter.name
  labels = {
    environment = terraform.workspace
    component   = "tagtoo-event"
    consumer    = "bq-writer-subscriber"
    destination = "bigquery"
  }
  message_retention_duration = "604800s" # 7 day
  retain_acked_messages      = false
  ack_deadline_seconds       = 60
  expiration_policy {
    ttl = "2678400s" # 31 days
  }
}
