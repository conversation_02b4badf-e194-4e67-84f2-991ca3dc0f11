provider "google" {
  project = var.gcp_project
}

provider "cloudflare" {
  email   = var.cloudflare.email
  api_key = var.cloudflare.token
}

# https://www.terraform.io/docs/providers/google/r/google_project_service.html
resource "google_project_service" "gcp_services" {
  count                      = length(var.gcp_service_list)
  project                    = var.gcp_project
  service                    = var.gcp_service_list[count.index]
  disable_dependent_services = false
  disable_on_destroy         = false
}

provider "kubernetes" {
  host                   = "https://${google_container_cluster.event.endpoint}"
  cluster_ca_certificate = base64decode(google_container_cluster.event.master_auth.0.cluster_ca_certificate)
  token                  = data.google_client_config.google_client.access_token
}

data "google_client_config" "google_client" {}
