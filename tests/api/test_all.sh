#!/bin/bash
#
# Run multiple tests with different inputs

abs_path() { echo "$(cd "$(dirname "$1")" && pwd -P)/$(basename "$1")"; }
random_subscription()
{
  echo "projects/tagtoo-tracking/subscriptions/test-$(openssl rand -hex 6)"
}

capi_topic="projects/tagtoo-tracking/topics/facebook-capi"
tagtoo_topic="projects/tagtoo-tracking/topics/tagtoo-event"

./test_event.sh "$capi_topic" $(random_subscription) 1 $(abs_path capi.json) && \
./test_event.sh "$tagtoo_topic" $(random_subscription) 0 $(abs_path tagtoo.missing_field.json) && \
./test_event.sh "$tagtoo_topic" $(random_subscription) 24 $(abs_path tagtoo.valid_event.json)