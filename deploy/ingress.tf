resource "kubernetes_manifest" "managed_cert" {
  manifest = {
    apiVersion = "networking.gke.io/v1"
    kind       = "ManagedCertificate"
    metadata = {
      name      = "tagtoo-co-managed-cert-${var.environment}"
      namespace = var.kubernetes_namespace
    }
    spec = {
      domains = [
        "${var.event_api.ingress_subdomain}.tagtoo.co",
      ]
    }
  }
}

resource "kubernetes_ingress_v1" "event_ingress" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = "event-ingress"
    annotations = {
      "kubernetes.io/ingress.global-static-ip-name" : google_compute_global_address.event_api.name
      "ingress.kubernetes.io/secure-backends"  = "true"
      "kubernetes.io/ingress.class"            = "gce"
      "networking.gke.io/managed-certificates" = kubernetes_manifest.managed_cert.manifest["metadata"]["name"]
    }
  }

  spec {
    rule {
      host = "${var.event_api.ingress_subdomain}.tagtoo.co"
      http {
        path {
          backend {
            service {
              name = kubernetes_service_v1.event_api_web.metadata[0].name
              port {
                number = 80
              }
            }
          }
          path = "/*"
        }
      }
    }
    rule {
      host = "${var.event_config_api.ingress_subdomain}.tagtoo.co"
      http {
        path {
          backend {
            service {
              name = kubernetes_service_v1.event_config_api_web.metadata[0].name
              port {
                number = 80
              }
            }
          }
          path = "/*"
        }
      }
    }
  }
}

resource "kubernetes_ingress_v1" "event_ingress_ipv6" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = "event-ingress-ipv6"
    annotations = {
      "kubernetes.io/ingress.global-static-ip-name" : google_compute_global_address.event_api_ipv6.name
      "ingress.kubernetes.io/secure-backends"  = "true"
      "kubernetes.io/ingress.class"            = "gce"
      "networking.gke.io/v1.EnableIpv6"        = "true"
      "networking.gke.io/managed-certificates" = kubernetes_manifest.managed_cert.manifest["metadata"]["name"]

    }
  }

  spec {
    rule {
      host = "${var.event_api.ingress_subdomain}.tagtoo.co"
      http {
        path {
          backend {
            service {
              name = kubernetes_service_v1.event_api_web.metadata[0].name
              port {
                number = 80
              }
            }
          }
          path = "/*"
        }
      }
    }
    rule {
      host = "${var.event_config_api.ingress_subdomain}.tagtoo.co"
      http {
        path {
          backend {
            service {
              name = kubernetes_service_v1.event_config_api_web.metadata[0].name
              port {
                number = 80
              }
            }
          }
          path = "/*"
        }
      }
    }
  }
}
