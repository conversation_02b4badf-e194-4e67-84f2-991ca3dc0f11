- 存放路徑為 `ad-track/deploy/bigquery/table_functions/`，在這邊的 SQL 是由 terraform 管理跟建立，相關的設定可以在 `ad-track/deploy/bigquery.tf` 找到，terraform 這邊只有負責建立這些 Table Function，執行是由 `ad-track/user_unify/` 底下的程式處理
- 主要的工作為產出 [Flowchart](./../workflow.md#determine-groups-update-grouping-reslut-detail-flow-chart) 中的 `Daily Group Result` 跟 `Daily Entity Data`

### get_entity_by_date.sql
- Function Name：get_entity_by_date()
- Arguments
    - start_date：STRING
    - end_date：STRING
```sql
SELECT
    DISTINCT permanent,
    MIN(event_time) OVER (PARTITION BY permanent) entity_time,
    user.em,
    user.ph,
    user.un,
    -- user.fbp,
    -- 排除不合規則的 gid
    IF(
        REGEXP_CONTAINS(TRIM(user.gid), r'^GA\d\.\d\.\d+\.\d+'),
        TRIM(user.gid),
        NULL
    ) gid
FROM
    `tagtoo-tracking.event_prod.tagtoo_event`
WHERE
    DATE(event_time, 'Asia/Taipei') BETWEEN DATE(start_date)
    AND DATE(end_date)
```
- 負責從 `tagtoo-tracking.event_prod.tagtoo_event` 產出 `Daily Entity Data`，並將 `entity_time` 設為最早的訪問時間
- 基於 [ISSUE 244](https://github.com/Tagtoo/ad-track/issues/244) 遇到的問題，目前是把 `fbp` 移除掉，因為看起來當前的參考價值較低，目前的其他 Table Function 都先排除掉使用 `fbp`
- [ISSUE 247](https://github.com/Tagtoo/ad-track/issues/247#issuecomment-1376920453) 中發現，有些不合規格的 `gid` 最終會導致許多不相關的群組合併，因此先加上預處理，避免此情況再次發生

### get_entity_neighbors_by_date.sql
- Function Name：get_entity_neighbors_by_date()
- Arguments
    - start_date：STRING
    - end_date：STRING
```sql
WITH entity_data AS (
    SELECT
        *
    FROM
        `tagtoo-tracking.event_prod.get_entity_by_date`(start_date, end_date)
)
SELECT
    ed.permanent AS base_permanent,
    ed2.permanent AS neighbors,
    ed2.entity_time,
    IF (ed.em = ed2.em, ed.em, NULL) AS em,
    IF (ed.ph = ed2.ph, ed.ph, NULL) AS ph,
    IF (ed.un = ed2.un, ed.un, NULL) AS un,
    -- IF (ed.fbp = ed2.fbp, ed.fbp, NULL) AS fbp,
    IF (ed.gid = ed2.gid, ed.gid, NULL) AS gid
FROM
    entity_data ed
    JOIN entity_data ed2 ON (
        ed2.em = ed.em
        OR ed2.ph = ed.ph
        OR ed2.un = ed.un
        -- OR ed2.fbp = ed.fbp
        OR ed2.gid = ed.gid
    )
ORDER BY
    ed.permanent
```
- 基於 Table Function `get_entity_by_date()` 產出 `Daily Entity Data` 後，使用 self join 找出具有相同屬性的 permanent，定為 neighbors

### get_grouping_result_by_date.sql
- Function Name：get_grouping_result_by_date()
- Arguments
    - start_date：STRING
    - end_date：STRING
```sql
-- 先將 get_entity_neighbors_by_date() 得到的結果整理，先做第一次聚合
-- 會以 entity_time 作為排序基礎，主要是為了接下來第二次聚合準備
WITH group_results AS (
    SELECT
        neighbor_data.base_permanent AS permanent,
        ARRAY_AGG(
            neighbor_data.neighbors
            ORDER BY
                entity_time,
                neighbors
        ) AS neighbors,
        ARRAY_AGG(
            entity_time
            ORDER BY
                entity_time,
                neighbors
        ) AS entity_times,
        ARRAY_AGG(DISTINCT em IGNORE NULLS) AS emails,
        ARRAY_AGG(DISTINCT ph IGNORE NULLS) AS phones,
        ARRAY_AGG(DISTINCT un IGNORE NULLS) AS usernames,
        -- ARRAY_AGG(DISTINCT fbp IGNORE NULLS) AS fbps,
        ARRAY_AGG(DISTINCT gid IGNORE NULLS) AS gids
    FROM
        `tagtoo-tracking.event_prod.get_entity_neighbors_by_date`(start_date, end_date) neighbor_data
    GROUP BY
        1
)
-- 第二次聚合，將相同 group id 的聚合在一起
-- 由於 ARRAY_CONCAT_AGG 沒有支援 DISTINCT，之後會再處理重複的結果
SELECT
    group_id,
    group_time,
    ARRAY_AGG(DISTINCT permanent) permanents,
    ARRAY_CONCAT_AGG(emails) emails,
    ARRAY_CONCAT_AGG(phones) phones,
    ARRAY_CONCAT_AGG(usernames) usernames,
    -- ARRAY_CONCAT_AGG(fbps) fbps,
    ARRAY_CONCAT_AGG(gids) gids
FROM
    -- 資料整理，以時間最早的 permanent 作為 group id
    (
        SELECT
            permanent,
            entity_times [OFFSET (0)] group_time,
            neighbors [OFFSET (0)] group_id,
            emails,
            phones,
            usernames,
            -- fbps,
            gids
        FROM
            group_results
        ORDER BY
            group_id
    )
GROUP BY
    1,
    2
```
- 基於 Table Function `get_entity_neighbors_by_date()` 的結果，產出最後的 `Daily Group Result`
- 當前產生的結果，都會存放在 `tagtoo-tracking.event_grouping_result_prod` 底下
