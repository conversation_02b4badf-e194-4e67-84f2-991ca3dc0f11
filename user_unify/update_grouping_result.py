import argparse
import logging
from datetime import date, datetime, timedelta

import pytz
from google.cloud import bigquery

from user_unify import process, settings
from user_unify.service import error_reporting_service

logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s %(levelname)s] %(message)s',
)
logger = logging.getLogger(__file__)


def parse_arguments():
    yesterday = (
        datetime.now(tz=pytz.timezone('Asia/Taipei')) - timedelta(days=1)
    ).strftime('%Y-%m-%d')
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '-s',
        '--start-date',
        type=str,
        default=yesterday,
        help='Start date of input event data',
    )
    parser.add_argument(
        '-e',
        '--end-date',
        type=str,
        default=yesterday,
        help='End date of input event data',
    )
    parser.add_argument(
        '-r',
        '--recalc-daily-grouping-result',
        action='store_true',
        help='Recalculate daily grouping result even though it is existed.',
    )
    args = parser.parse_args()

    assert args.start_date <= args.end_date, (
        f'Invalid argument. end_date should be later than start_date.'
        f'({args.start_date=}, {args.end_date=})'
    )

    return args


if __name__ == '__main__':
    args = parse_arguments()

    client = bigquery.Client(project=settings.GCP_PROJECT, location='asia-east1')
    dataset = f'{settings.GCP_PROJECT}.{settings.BQ_DATASET}'
    grouping_result_dataset = (
        f'{settings.GCP_PROJECT}.{settings.BQ_GROUPING_RESULT_DATASET}'
    )
    snapshot_dataset = f'{settings.GCP_PROJECT}.{settings.BQ_SNAPSHOT_DATASET}'
    logger.info(f'Process starts! ({args.start_date} ~ {args.end_date})')

    date_range = (
        date.fromordinal(ordinal)
        for ordinal in range(
            datetime.strptime(args.start_date, '%Y-%m-%d').toordinal(),
            datetime.strptime(args.end_date, '%Y-%m-%d').toordinal() + 1,
        )
    )
    for date_ in date_range:
        process.generate_group_result_snapshot(
            event_date=date_,
            client=client,
            dataset=dataset,
            snapshot_dataset=snapshot_dataset,
        )
        try:
            result = process.determine_groups(
                event_date=date_,
                client=client,
                dataset=dataset,
                grouping_result_dataset=grouping_result_dataset,
                recalc_daily_grouping_result=args.recalc_daily_grouping_result,
            )
        except Exception as e:
            process.rollback_user_unify_groups_table(
                event_date=date_,
                client=client,
                dataset=dataset,
                snapshot_dataset=snapshot_dataset,
            )
            logger.exception("An error occurred when generating group result")
            error_reporting_service.report_exception()
    logger.info(
        'Group candidate determination process is completed! '
        f'({args.start_date} ~ {args.end_date})'
    )

    process.generate_group_permanent_table(
        client=client,
        dataset=dataset,
    )

    logger.info(f'Whole process is completed ! ({args.start_date} ~ {args.end_date})')
