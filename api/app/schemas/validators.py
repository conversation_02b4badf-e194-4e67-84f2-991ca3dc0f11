from dataclasses import dataclass

from fastapi import Request
from fastapi.exceptions import ValidationError

from app.enums import ChannelType, EventDataScope
from app.schemas.event import EventData
from app.schemas.facebook import FacebookEventData
from app.schemas.tagtoo import TagtooEventData, TagtooEventDataBackend


class EventDataValidator(object):
    @dataclass
    class ErrorContent:
        loc: tuple
        body: dict
        scope: str
        msg: str
        type: str

    def __init__(
        self,
        request: Request,
        event_data: dict,
        event_config: dict,
        *args,
        **kwargs,
    ):
        self._errors = []
        self._request = request
        self._event_data = event_data
        self._event_config = event_config
        self._has_top_level_error = False
        self._valid_scopes = []
        self._validated_data = {}

    def is_scope_valid(self, scope: EventDataScope):
        return scope in self._valid_scopes

    def errors(self):
        return self._errors

    def append_error(
        self,
        ve: ValidationError,
        body: dict,
        scope=None,
    ):
        assert isinstance(
            ve, ValidationError
        ), 'Input type of \'ve\' should be a \'pydantic.ValidationError\' instance'
        self._errors.extend(
            [
                self.ErrorContent(
                    loc=(tuple([scope]) + e['loc'] if scope is not None else e['loc']),
                    body=body,
                    scope=scope,
                    msg=e['msg'],
                    type=e['type'],
                )
                for e in ve.errors()
            ]
        )

        if scope is None:
            self._has_top_level_error = True

    def has_scope_error(self, scope: str):
        return scope in [e.scope for e in self._errors]

    def has_top_level_error(self):
        return self._has_top_level_error

    def validate(self):
        event_data = dict(self._event_data)
        channel_type = self._event_config['client']['channel_type']
        facebook_event_data = event_data.pop(
            EventDataScope.FACEBOOK_CAPI.data_key, None
        )
        tagtoo_event_data = event_data.pop(EventDataScope.TAGTOO_EVENT.data_key, None)

        # Validate top-level fields. If any error is occurred, the event
        # should not be published.
        try:
            event_data = EventData(**event_data)
            self._validated_data.update(event_data.dict(by_alias=True))
        except ValidationError as e:
            self.append_error(e, event_data)

        # Validate fields by scopes
        if facebook_event_data:
            try:
                facebook_event_data = FacebookEventData(**facebook_event_data)
                self._validated_data.update(
                    {
                        EventDataScope.FACEBOOK_CAPI.data_key: facebook_event_data.dict(
                            by_alias=True
                        )
                    }
                )
            except ValidationError as e:
                self.append_error(
                    e, facebook_event_data, scope=EventDataScope.FACEBOOK_CAPI.data_key
                )
            else:
                self._valid_scopes.append(EventDataScope.FACEBOOK_CAPI)

        if tagtoo_event_data:
            try:
                if channel_type == ChannelType.FRONTEND:
                    tagtoo_event_data = TagtooEventData(**tagtoo_event_data)
                elif channel_type == ChannelType.BACKEND:
                    tagtoo_event_data = TagtooEventDataBackend(**tagtoo_event_data)
                self._validated_data.update(
                    {
                        EventDataScope.TAGTOO_EVENT.data_key: tagtoo_event_data.dict(
                            by_alias=True
                        )
                    }
                )
            except ValidationError as e:
                self.append_error(
                    e, tagtoo_event_data, scope=EventDataScope.TAGTOO_EVENT.data_key
                )
            else:
                self._valid_scopes.append(EventDataScope.TAGTOO_EVENT)

        return self._validated_data

    def _construct_detail(self):
        details = []
        for i, err in enumerate(self._errors):
            err_locs = [str(loc) for loc in err.loc]
            field_path_string = '.'.join(err_locs)
            field_value = err.body
            for loc in err.loc[1:]:  # First element is scope
                if isinstance(field_value, dict):
                    field_value = field_value.get(loc)
                elif isinstance(field_value, list):
                    field_value = field_value[loc]
            details += [
                f'{i+1}. {err.msg} (Field: "{field_path_string}", Value: {field_value})'
            ]
        detail_message = ' '.join(details)
        return detail_message

    def construct_error_message(self):
        num_of_errors = len(self._errors)
        if num_of_errors:
            ec_id = self._event_config['ec_id']
            detail_message = self._construct_detail()

            error_string = 'error' if num_of_errors == 1 else 'errors'
            message = (
                f'EventDataValidationError: ({num_of_errors} {error_string}) => {detail_message}\n'
                f'EC ID => {ec_id}\n'
                f'Request Body => {self._event_data}\n'
                f'Request Headers => {dict(self._request.headers)}'
            )
        else:
            message = 'No validation errors'
        return message
