import traceback
import logging
import ipaddress
from operator import itemgetter
from shared import pubsub, error_report
from google.cloud import bigquery
from user_agents import parse
from . import settings
from .ip2location import query_location, is_internal_ip

# 創建 logger
logger = logging.getLogger(__name__)


class EventBigQueryWriterService(error_report.ErrorReportingService):
    project = settings.GCP_PROJECT_ID
    credentials = None  # Use VM service account
    service = 'event-bq-writer'

    def report(self, msg):
        logger.error(msg)

    def report_row_error(self, errors, data):
        msg = f"InsertEventRowError: {errors}, Payload: {data}"
        self.report(msg)

    def report_exception(self, *args, **kwargs):
        logger.error("Exception occurred:")
        logger.error(traceback.format_exc())


class TagtooEventSubscriberV1(
    pubsub.parser.MessageParserMixin, pubsub.BaseSubscriberClient
):
    project_id = settings.GCP_PROJECT_ID
    subscription_name = settings.TAGTOO_EVENT_SUBSCRIPTION_V1
    message_attribute_parsers = {
        'version': pubsub.parser.parse_string,
        'ec_id': pubsub.parser.parse_int,
    }
    service_account_path = None  # Use VM service account

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.client = bigquery.Client(
            project=settings.GCP_PROJECT_ID
        )  # Use VM service account
        self.error_report_service = EventBigQueryWriterService(version='v1')

    def callback_function(self, message):
        result = self.parse(message)
        data, version, ec_id = itemgetter('data', 'version', 'ec_id')(result)

        # Patch main fields
        data['ec_id'] = ec_id

        # Patch location fields
        ip_value = data.get('ip_address', None)
        ipv4 = None
        ipv6 = None

        # 處理 IP 地址，支援新舊兩種格式
        if isinstance(ip_value, dict):
            # 新格式：API 已經將 IP 分類為 IPv4 和 IPv6
            ipv4 = ip_value.get('ipv4')
            ipv6 = ip_value.get('ipv6')
        elif isinstance(ip_value, str):
            # 舊格式：字串形式的 IP 地址，需要在這裡分類
            # TODO: 長期來看，當所有消息都使用新格式後，可以移除這段處理字串格式的邏輯
            # 這段代碼僅用於向下兼容舊版本的消息格式
            try:
                ip_obj = ipaddress.ip_address(ip_value)
                if ip_obj.version == 4:
                    ipv4 = ip_value
                elif ip_obj.version == 6:
                    ipv6 = ip_value
            except Exception:
                # 無效的 IP 地址，保留為 None
                pass

        # 優先使用 IPv4 進行地理位置解析
        ip_to_resolve = ipv4 or ipv6

        if ip_to_resolve:
            # 檢查是否為內部 IP
            if is_internal_ip(ip_to_resolve):
                # 對於內部 IP，直接跳過解析
                logger.info(f"Skipping location resolution for internal IP: {ip_to_resolve}")
                location = None  # 設置 location 為 None，避免後續引用錯誤
                data['location'] = None  # 明確設置 location 字段為 None，保持一致的資料結構
            else:
                try:
                    location = query_location(ip_to_resolve)
                except Exception:  # noqa
                    # 對於外部 IP 解析錯誤，仍然使用原來的處理方式
                    message.nack()
                    self.error_report_service.report_exception()
                    return False

                if location:
                    data['location'] = {
                        'country_code': location.country_code,
                        'region_name': location.region_name,
                        'city_name': location.city_name,
                        'latitude': location.latitude,
                        'longitude': location.longitude,
                        'zip_code': location.zip_code,
                    }
                else:
                    logger.warning(f"Unable to resolve location for external IP: {ip_to_resolve}")

        # 更新 IP 地址格式，確保一致性
        if ipv4 or ipv6:
            data['ip_address'] = {'ipv4': ipv4, 'ipv6': ipv6}

        # Patch user agent fields
        ua_string = data.pop('user_agent', None)
        if ua_string:
            ua = parse(ua_string)
            data['user_agent'] = {
                'browser': ua.browser.family,
                'browser_version': ua.browser.version_string,
                'os': ua.os.family,
                'os_version': ua.os.version_string,
                'device': ua.device.family,
                'is_mobile': ua.is_mobile,
                'is_tablet': ua.is_tablet,
                'is_pc': ua.is_pc,
                'is_touch_capable': ua.is_touch_capable,
                'is_bot': ua.is_bot,
            }
        else:
            # 添加默認值，並標記為默認值，方便數據清理
            logger.warning(f"Missing user_agent field, using default values. Message ID: {message.message_id}")
            data['user_agent'] = {
                'browser': 'DEFAULT_MISSING_UA',
                'browser_version': '0.0',
                'os': 'DEFAULT_MISSING_UA',
                'os_version': '0.0',
                'device': 'DEFAULT_MISSING_UA',
                'is_mobile': False,
                'is_tablet': False,
                'is_pc': True,
                'is_touch_capable': False,
                'is_bot': False,
            }

        table_id = f'{settings.GCP_PROJECT_ID}.{settings.BIGQUERY_DATASET_ID}.{settings.BIGQUERY_TAGTOO_EVENT_TABLE_ID}'
        try:
            errors = self.client.insert_rows_json(table_id, [data])
        except Exception:  # noqa
            message.nack()
            self.error_report_service.report_exception()
            return False
        if errors:
            self.error_report_service.report_row_error(errors, data)
        message.ack()
