from typing import List


def resolve_multiple_candidates(
    data: List[dict],
) -> dict:
    root = {}

    def find(node):
        root.setdefault(node, node)
        if root[node] != node:
            root[node] = find(root[node])
        return root[node]

    def union(x, y):
        parent_x = find(x)
        parent_y = find(y)
        if parent_x < parent_y:
            root[parent_y] = parent_x
        elif parent_x > parent_y:
            root[parent_x] = parent_y

    # data 為 candidate > 1 的結果
    # 從 BigQuery 中撈出後，並轉成 dict 形式
    # 由於 permanent 早就已經在新的 Grouping Result 中，所以在這邊不會使用到
    # 就只需要專注所有 candidate 如何分群即可
    for datum in data:
        head = datum['group_ids'][0]
        for group_id in datum['group_ids']:
            union(head, group_id)

    _ = [find(key) for key in root.keys()]
    return root
