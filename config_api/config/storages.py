from django.conf import settings
from google.oauth2.service_account import Credentials
from storages.backends.gcloud import GoogleCloudStorage
from urllib.parse import urljoin

if settings.USE_GCS:

    class StaticStorage(GoogleCloudStorage):
        """GoogleCloudStorage suitable for Django's Static files"""

        bucket_name = settings.GS_STATIC_BUCKET
        default_acl = 'publicRead'

        def __init__(self, *args, **kwargs):
            if not settings.STATIC_URL:
                raise Exception('STATIC_URL has not been configured')
            super(StaticStorage, self).__init__(*args, **kwargs)

        @property
        def credentials(self):
            if settings.GS_CREDENTIAL_PATH:
                return Credentials.from_service_account_file(
                    settings.GS_CREDENTIAL_PATH
                )
            return None  # Use VM service account

        def url(self, name):
            """.url that doesn't call <PERSON>."""
            return urljoin(settings.STATIC_URL, name)
