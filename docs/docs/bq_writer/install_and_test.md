<script src="https://unpkg.com/mermaid@8.13.3/dist/mermaid.min.js"></script>

## Docker Compose

```mermaid
flowchart TB
  subgraph docker bridge network
    subgraph network:bq_writer
        bq-writer-pubsub -- pull --> tagtoo-event-subscriber
        tagtoo-event-subscriber -- query --> ip2location
    end
  end
  tagtoo-event-subscriber --> bigquery-temp-table[(BigQuery temp table)]
```

1. In order to create a BigQuery temp table for this compose, run the script at root:
   ```shell
   ./run_bq_writer_services.sh
   # ... or you can specifing docker compose arguments, e.g.
   ./run_bq_writer_services.sh -d
   ```
2. Turn down the services
   ```shell
   docker-compose -f docker-compose.bq_writer.yml down
   ```

!!! info
    The Pub/Sub local service is an emulator built from `messagebird/gcloud-pubsub-emulator`.

!!! info
    There's currently no BigQuery emulator tools, therefore, connect the local service to a BigQuery temp table. Please
    refer to `run_bq_writer_services.sh` for details.

    The temp table is auto-expired in 1 day and the table ID is exposed to the application using environment variable 
    `BIGQUERY_DATASET_ID` and `BIGQUERY_TAGTOO_EVENT_TABLE_ID`.

!!! info
    In local environment, the IP2Location is built from the light-weight version `DB9LITE` database.

## Test

Use shell scripts to test across services. Start the services and run these commands at `tests/bq_writer` folder:

```shell
./test_all.sh
```
