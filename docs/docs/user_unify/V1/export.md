<script src="https://unpkg.com/mermaid@8.13.3/dist/mermaid.min.js"></script>

There are 3 things in user unify graph we are interested in:

1. What group a permanent belongs to? / What are the permanents in a given group?
2. What are the emails in a given group?
3. What are the mobiles in a given group?

## Map Tables from BigTable to BigQuery

In [Schema](/schema) page, we demonstrate the Group Overlay of user unify, is a **Parent Pointer Tree** structure, which
means we only stores the group pointer, not the result for whole group. Therefore, the database only represent a data structure,
there is no easy way to query or display the mapping result without traversal the group.

Took advantage of [External Data Link](https://cloud.google.com/bigquery/docs/external-tables), we can map our
data structure from BigTable to BigQuery. It's easy to aggregate and concat our mapping result in BigQuery, and more over,
this result can directly feed back and integrated with the event data.

### Workflow

```mermaid
flowchart LR
    A[BigTable Permanent Table] ---|external link| B[BigQuery Permanent Table]
    B --> C([Query Job 1])
    B --> D([Query Job 2])
    
    C --> E[Permanent/Group Mapping Result Table]
    D --> F[Group Result Table]
```

### Example

Use a simple-3-node graph to illustrate how table is mapped between BigTable and BigQuery. In BigQuery, there's no
sparse column, instead, it's use **nested** structure to store data.

**Permanent Table in BigTable**

|rowkey|meta.timestamp|meta.group|emails.e1|permanents.p3|
|---|---|---|---|---|
|p1|1636516671000000000|p1|||

|rowkey|meta.timestamp|meta.group|mobiles.m1|permanents.p3|
|---|---|---|---|---|
|p2|1636516672000000000|p1|||

|rowkey|meta.timestamp|meta.group|emails.e1|mobiles.m1|permanents.p1|permanents.p2|
|---|---|---|---|---|---|---|
|p3|1636516673000000000|p1|||||

**Linked Permanent Table in BigQuery**

|rowkey|meta|emails|mobiles|permanents|
|---|---|---|---|---|
|p1|<nobr>[{"column.name": "group", "column.value": "p1"},{"column.name": "timestamp", "column.value": "1636516671000000000"}]</nobr>|<nobr>[{"column.name": "e1", "column.value": ""}]</nobr>||<nobr>[{"column.name": "p3", "column.value": ""}]</nobr>|
|p2|<nobr>[{"column.name": "group", "column.value": "p1"},{"column.name": "timestamp", "column.value": "1636516672000000000"}]</nobr>||<nobr>[{"column.name": "m1", "column.value": ""}]</nobr>|<nobr>[{"column.name": "p3", "column.value": ""}]</nobr>|
|p3|<nobr>[{"column.name": "group", "column.value": "p1"},{"column.name": "timestamp", "column.value": "1636516673000000000"}]</nobr>|<nobr>[{"column.name": "e1", "column.value": ""}]</nobr>|<nobr>[{"column.name": "m1", "column.value": ""}]</nobr>|<nobr>[{"column.name": "p1", "column.value": ""},{"column.name": "p2", "column.value": ""}]</nobr>|

**Permanent/Group Mapping Result Table**

|permanent|group_id|
|---|---|
|p1|p1|
|p2|p1|
|p3|p1|

**Group Result Table**

|group_id|permanents|emails|mobiles|
|---|---|---|---|
|p1|<nobr>["p1","p2","p3"]</nobr>|<nobr>["e1"]</nobr>|<nobr>["m1"]</nobr>|