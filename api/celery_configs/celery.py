import asyncio

from celery import Celery
from celery.utils.log import get_task_logger  # Initialize celery

from app.config import settings
from app.utils import update_event_configs

celery_app = Celery(
    'celery_configs',
    broker=f'redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}/1',
    backend=f'redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}/1',
)

celery_app.conf.task_default_queue = 'default-queue'

celery_log = get_task_logger(__name__)


@celery_app.task(ignore_result=True)
def task_update_event_configs():
    asyncio.run(update_event_configs())


@celery_app.on_after_configure.connect
def setup_periodic_tasks(sender, **kwargs):
    sender.add_periodic_task(
        settings.EVENT_CONFIG_DATA_UPDATE_PERIOD,
        task_update_event_configs.s(),
        name='update event config data',
    )
