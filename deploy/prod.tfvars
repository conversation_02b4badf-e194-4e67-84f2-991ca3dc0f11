environment = "prod"

deletion_protection = true

wait_for_rollout = false

container_cluster_event = {
  kubernetes_pool = {
    preemptible = false
    service_account = "<EMAIL>"
  }
  api_pool = {
    preemptible     = false
    machine_type    = "e2-highcpu-4"
    service_account = "<EMAIL>"
  }
  s2s_system_pool = {
    preemptible  = false
    machine_type = "e2-medium"
    service_account = "<EMAIL>"
  }
  s2s_lta_subscriber_pool = {
    preemptible     = false
    machine_type    = "e2-standard-4"
    service_account = "<EMAIL>"
  }
  s2s_subscriber_pool = {
    preemptible     = false
    machine_type    = "e2-medium"
    service_account = "<EMAIL>"
  }
  config_api_pool = {
    preemptible     = false
    machine_type    = "e2-medium"
    service_account = "<EMAIL>"
  }
  bq_writer_subscriber_pool = {
    preemptible     = true
    machine_type    = "n4-highcpu-4"  # 升級以支援雙寫入架構記憶體需求
    service_account = "<EMAIL>"
  }
  user_unify_pool = {
    preemptible     = false
    machine_type    = "e2-standard-2"
    service_account = "<EMAIL>"
  }
}

event_api = {
  web = {
    image_tag                         = "develop-28bf458d"
    min_replicas                      = 4
    max_replicas                      = 20
    target_cpu_utilization_percentage = 60
    env = {
      debug                 = "False"
      secret_key            = "rzHsRL6NIUUgr7c8DHpEcxagBljCfzy0"
      config_api_auth_token = "5d7667a6d8eaf13553bef21f27ebb2ff06fbf0e1"
      api_doc_username      = "tagtoo_admin"
      api_doc_password      = "Qazwsxedc2021"
    }
    requests = {
      cpu    = "500m"
      memory = "512Mi"
    }
    limits = {
      cpu    = "700m"
      memory = "768Mi"
    }
  }
  ingress_subdomain = "event"
  api_versions      = ["v1"]
}

event_s2s = {
  common = {
    env = {
      mode                  = "prod"
      config_api_auth_token = "5d7667a6d8eaf13553bef21f27ebb2ff06fbf0e1"
    }
  }

  lta_subscriber = {
    image_tag                         = "fix-fb-s2s-lta-6f67b6f"
    min_replicas                      = 1
    max_replicas                      = 2
    target_cpu_utilization_percentage = 90
    max_messages                      = 1
    requests = {
      cpu    = "800m"
      memory = "800Mi"
    }
    limits = {
      cpu    = "1"
      memory = "1Gi"
    }
  }

  facebook_capi_subscriber = {
    image_tag = "fix-fb-s2s-lta-6f67b6f"

    batch = {
      min_replicas                      = 1
      max_replicas                      = 2
      target_cpu_utilization_percentage = 90
      max_messages                      = 350
      requests = {
        cpu    = "200m"
        memory = "200Mi"
      }
      limits = {
        cpu    = "300m"
        memory = "1000Mi"
      }
    }
    singleton = {
      min_replicas                      = 1
      max_replicas                      = 3
      target_cpu_utilization_percentage = 50
      max_messages                      = 100
      requests = {
        cpu    = "100m"
        memory = "200Mi"
      }
      limits = {
        cpu    = "300m"
        memory = "1000Mi"
      }
    }
    lta_batch = {
      min_replicas                      = 1
      max_replicas                      = 5
      target_cpu_utilization_percentage = 60
      max_messages                      = 500
      requests = {
        cpu    = "500m"
        memory = "500Mi"
      }
      limits = {
        cpu    = "750m"
        memory = "1000Mi"
      }
    }
  }
}

event_config_api = {
  ingress_subdomain = "event-cnf"
  web = {
    image_tag = "develop-b4222cd"
    env = {
      debug         = "False"
      secret_key    = "t=gn3p22)8qwgqz0vd)x3q91xwn=c53^t6%!*w!e)5dmm3#0#x"
      allowed_hosts = "event-cnf.tagtoo.co,config-api-web"
    }
    requests = {
      cpu    = "300m"
      memory = "300Mi"
    }
    limits = {
      cpu    = "500m"
      memory = "600Mi"
    }
  }
}

bq_writer = {
  env = {
    test_mode                      = "False"
    # 雙寫入架構配置 (v2.1 tracking_id 核心策略)
    integrated_write_enabled       = "false"  # 初始禁用，漸進式啟用
    integrated_write_sample_rate   = "0.01"   # 1% 採樣率，用於初期測試
    # 佇列和重試配置
    integrated_queue_maxsize       = "2000"   # 背景寫入佇列大小
    max_immediate_retries          = "3"      # 立即重試次數
    max_delayed_retries            = "10"     # 延遲重試次數
    bq_debug_daily_budget          = "10.0"   # BigQuery 除錯查詢日預算 (USD)
  }
  ip2location = {
    image_tag = "2025-04-14"
    requests = {
      # 雙寫入架構資源調整 (+35% for v2.1 tracking_id strategy)
      cpu               = "135m"  # 100m * 1.35
      memory            = "540Mi" # 400Mi * 1.35
      ephemeral-storage = "4Gi"   # 3Gi * 1.35 ≈ 4Gi
    }
  }
  tagtoo_event_subscriber = {
    containers_per_pod                = 5
    image_tag                         = "master-45dbfc6"
    min_replicas                      = 4
    max_replicas                      = 30  # 調整以符合實際節點容量 (20 nodes × 2 pods/node = 40 pods 容量)
    target_cpu_utilization_percentage = 70  # 降低以提供更好的穩定性
    max_messages                      = 10
    requests = {
      # 雙寫入架構資源調整 (+35% for v2.1 tracking_id strategy)
      cpu               = "135m"  # 100m * 1.35
      memory            = "135Mi" # 100Mi * 1.35
      ephemeral-storage = "135Mi" # 100Mi * 1.35
    }
  }
}

user_unify = {
  candidate_prediction_job = {
    image_tag = "master-dc56937"
    requests = {
      cpu    = "500m"
      memory = "1.5G"
    }
    limits = {
      cpu    = "800m"
      memory = "1.5G"
    }
  }
}
