locals {
  event_api_prefix = "api"
  event_api_env    = "${local.event_api_prefix}-env"
  event_api_web    = "${local.event_api_prefix}-web"
}

resource "kubernetes_config_map_v1" "event_api_env" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = local.event_api_env
  }
  data = {
    PUBSUB_EVENT_FACEBOOK_CAPI_TOPIC_NAME = google_pubsub_topic.facebook_capi.name
    PUBSUB_EVENT_TAGTOO_EVENT_TOPIC_NAME  = google_pubsub_topic.tagtoo_event.name
    GCP_PROJECT_NAME                      = var.gcp_project
    GCP_ERROR_REPORTING_SERVICE_NAME      = "event-api"
    DEBUG                                 = var.event_api.web.env.debug
    EVENT_API_SECRET_KEY                  = var.event_api.web.env.secret_key
    EVENT_API_LOGGING_CONFIG_FILE         = "app/config/logging/fastapi-dev.ini"
    EVENT_API_LOGGING_FILE                = "/var/log/output.log"
    REDIS_HOST                            = "127.0.0.1"
    EVENT_CONFIG_API_SERVER_HOST          = "http://${kubernetes_service_v1.event_config_api_web.metadata[0].name}"
    EVENT_CONFIG_API_TOKEN                = var.event_api.web.env.config_api_auth_token
    EVENT_CONFIG_API_TOKEN_EXPIRE_TIME    = 43200
    EVENT_CONFIG_DATA_UPDATE_PERIOD       = 900
    PORT                                  = 8000
    EVENT_API_DOC_USERNAME                = var.event_api.web.env.api_doc_username
    EVENT_API_DOC_PASSWORD                = var.event_api.web.env.api_doc_password
  }
}

resource "kubernetes_deployment_v1" "event_api_web" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = local.event_api_web
    labels = {
      "app.kubernetes.io/name" = local.event_api_web
    }
  }
  spec {
    selector {
      match_labels = {
        "app.kubernetes.io/name" = local.event_api_web
      }
    }
    template {
      metadata {
        labels = {
          "app.kubernetes.io/name" = local.event_api_web
        }
      }
      spec {
        container {
          image = "redis:6-alpine"
          name  = "db"
          resources {
            requests = {
              cpu    = "100m"
              memory = "50Mi"
            }
          }
          volume_mount {
            mount_path = "/data"
            name       = "db"
          }
        }
        container {
          image   = "asia.gcr.io/tagtoo-tracking/event-api:${var.event_api.web.image_tag}"
          name    = "web"
          command = ["./scripts/entrypoint.sh"]
          args    = ["/start.sh"]
          env_from {
            config_map_ref {
              name = kubernetes_config_map_v1.event_api_env.metadata[0].name
            }
          }
          resources {
            limits = {
              cpu    = var.event_api.web.limits.cpu
              memory = var.event_api.web.limits.memory
            }
            requests = {
              cpu    = var.event_api.web.requests.cpu
              memory = var.event_api.web.requests.memory
            }
          }
          liveness_probe {
            http_get {
              path = "/liveness"
              port = 8000
            }
            timeout_seconds       = 3
            initial_delay_seconds = 15
          }
          readiness_probe {
            http_get {
              path = "/readiness"
              port = 8000
            }
            timeout_seconds       = 3
            initial_delay_seconds = 15
          }
        }
        container {
          image   = "asia.gcr.io/tagtoo-tracking/event-api:${var.event_api.web.image_tag}"
          name    = "worker"
          command = ["./scripts/entrypoint.sh"]
          args    = ["./scripts/start_worker.sh"]
          env_from {
            config_map_ref {
              name = kubernetes_config_map_v1.event_api_env.metadata[0].name
            }
          }
          resources {
            limits = {
              cpu    = "50m"
              memory = "200Mi"
            }
            requests = {
              cpu    = "50m"
              memory = "150Mi"
            }
          }
        }
        volume {
          name = "db"
          empty_dir {}
        }
        node_selector = {
          "application" = "event"
          "name"        = "api"
        }
        toleration {
          effect   = "NoSchedule"
          key      = "name"
          operator = "Equal"
          value    = "api"
        }
      }
    }
  }
  lifecycle {
    ignore_changes = [
      spec[0].replicas,
    ]
  }
  wait_for_rollout = var.wait_for_rollout
}

resource "kubernetes_horizontal_pod_autoscaler_v2" "event_api_web" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = local.event_api_web
  }
  spec {
    min_replicas = var.event_api.web.min_replicas
    max_replicas = var.event_api.web.max_replicas

    metric {
      type = "Resource"
      resource {
        name = "cpu"
        target {
          type                = "Utilization"
          average_utilization = var.event_api.web.target_cpu_utilization_percentage
        }
      }
    }


    scale_target_ref {
      api_version = "apps/v1"
      kind        = "Deployment"
      name        = kubernetes_deployment_v1.event_api_web.metadata[0].name
    }

    behavior {
      scale_up {
        stabilization_window_seconds = 60  # 減少擴展冷卻期
        select_policy                = "Max"
        policy {
          type           = "Percent"
          value          = 100
          period_seconds = 60
        }
        policy {
          type           = "Pods"
          value          = 5
          period_seconds = 60
        }
      }
      scale_down {
        stabilization_window_seconds = 300
        select_policy                = "Max"
        policy {
          type           = "Percent"
          value          = 10
          period_seconds = 60
        }
      }
    }
  }
  lifecycle {
    ignore_changes = [
      spec[0].behavior,
    ]
  }
}

resource "kubernetes_service_v1" "event_api_web" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = local.event_api_web
  }
  spec {
    selector = {
      "app.kubernetes.io/name" = local.event_api_web
    }
    type             = "NodePort"
    session_affinity = null
    ip_family_policy = "PreferDualStack"
    ip_families      = ["IPv4", "IPv6"]
    port {
      port        = 80
      target_port = 8000
    }
  }
}
