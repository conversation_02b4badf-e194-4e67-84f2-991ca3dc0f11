from dataclasses import dataclass, field
from typing import List, Literal, Optional

from pydantic import (
    AnyUrl,
    BaseModel,
    Field,
    HttpUrl,
    root_validator,
    validator,
)

import pycountry
from google.cloud import translate_v2 as translate

from .enums import (
    AuthMethodEnum,
    ItemAvailabilityEnum,
    PaymentMethodEnum,
    ShippingMethodEnum,
)
from .exceptions import TagtooEventInvalidPropertiesError


class TagtooEventItem(BaseModel):
    id: str = Field(..., title='Product ID')
    name: str = Field(..., title='Product Name')
    price: Optional[float] = Field(None, title='Product unit price')
    quantity: Optional[int] = Field(None, title='Product quantity')
    availability: Optional[ItemAvailabilityEnum] = Field()


class TagtooEventCustomData(BaseModel):
    payment_method: Optional[PaymentMethodEnum] = Field(None)
    shipping_method: Optional[ShippingMethodEnum] = Field(None)
    copy_string: Optional[str] = Field(None, title='The web context copied by the user')
    status: Optional[bool] = Field(
        None, title='To show the on/off or entry/exit status of an event'
    )
    focus_minutes: Optional[int] = Field(
        None, title='To show how long an user stays on one page'
    )
    campaign_name: Optional[str] = Field(None, title='Ecommerce campaign or form title')
    auth_method: Optional[AuthMethodEnum] = Field(None)
    order_id: Optional[str] = Field(None, title='The unique ID for an order')
    document_height: Optional[int] = Field(None, title='The height of a web page')
    scroll_top: Optional[int] = Field(None, title='The scroll top of a web page')
    search_string: Optional[str] = Field(
        None, title='The string entered by the user for the search'
    )
    breadcrumb: Optional[str] = Field(None, title='The breadcrumb for a product page')


@dataclass
class EventPropertyRule(object):
    required: set = field(default_factory=set)
    optional: set = field(default_factory=set)


event_required_properties_map = {
    # Note:
    # - All event name should be specified in this map or it'll be viewed as invalid event
    #   at field level validation on 'TagtooEvent.name'.
    # - [EventPropertyRule()] means no properties are allowed for this event.
    'add_payment_info': [
        EventPropertyRule(
            required={'custom_data.payment_method'},
            optional={'value', 'currency', 'items'},
        ),
    ],
    'add_shipping_info': [
        EventPropertyRule(
            required={'value', 'currency', 'items', 'custom_data.shipping_method'},
        ),
    ],
    'add_to_cart': [
        EventPropertyRule(
            required={'value', 'currency', 'items'},
        ),
    ],
    'add_to_wishlist': [
        EventPropertyRule(
            required={'value', 'currency', 'items'},
        ),
    ],
    'checkout': [
        EventPropertyRule(
            required={'value', 'currency', 'items'},
        ),
    ],
    'lead': [
        EventPropertyRule(
            required={'custom_data.campaign_name'},
            optional={'value', 'currency'},
        ),
    ],
    'copy': [
        EventPropertyRule(
            required={'custom_data.copy_string'},
        ),
    ],
    'focus': [
        EventPropertyRule(
            required={'custom_data.status'},
        ),
        EventPropertyRule(
            required={'custom_data.focus_minutes'},
        ),
    ],
    'login': [
        EventPropertyRule(
            required={'custom_data.auth_method'},
        ),
    ],
    'page_view': [EventPropertyRule()],
    'purchase': [
        EventPropertyRule(
            required={'value', 'currency', 'items', 'custom_data.order_id'},
        ),
    ],
    'refund': [
        EventPropertyRule(
            required={'value', 'currency', 'custom_data.order_id'},
            optional={'items'},
        ),
    ],
    'register': [
        EventPropertyRule(
            required={'custom_data.auth_method'},
        ),
    ],
    'remove_from_cart': [
        EventPropertyRule(
            required={'value', 'currency', 'items'},
        ),
    ],
    'scroll': [
        EventPropertyRule(
            required={'custom_data.document_height', 'custom_data.scroll_top'},
        ),
    ],
    'search': [
        EventPropertyRule(
            required={'custom_data.search_string'},
        ),
    ],
    'share': [EventPropertyRule()],
    'switch_tab': [
        EventPropertyRule(
            required={'custom_data.status'},
        ),
    ],
    'view_cart': [
        EventPropertyRule(
            required={'value', 'currency', 'items'},
        ),
    ],
    'view_item_list': [
        EventPropertyRule(
            required={'items', 'custom_data.breadcrumb'},
        ),
    ],
    'view_item': [
        EventPropertyRule(
            required={'value', 'currency', 'items'},
        ),
    ],
    'view_promotion': [
        EventPropertyRule(
            required={'items', 'custom_data.campaign_name'},
        ),
    ],
}


CurrencyCodes = tuple(c.alpha_3 for c in list(pycountry.currencies))


class TagtooEvent(BaseModel):
    name: Literal[tuple(event_required_properties_map.keys())]
    value: Optional[float] = Field(None, title='The total value (price) of event')
    currency: Optional[Literal[CurrencyCodes]] = Field(None)  # Restrict to ISO-4217
    items: Optional[List[TagtooEventItem]] = Field([])
    custom_data: Optional[TagtooEventCustomData] = Field({})

    @classmethod
    def _validate_properties(
        cls,
        event,
        event_prop_rules: List[set],
        input_event_props: set,
        input_custom_data_props: set,
    ):
        input_custom_data_props = {
            f'custom_data.{prop}' for prop in input_custom_data_props
        }
        input_props = input_event_props | input_custom_data_props

        for rule in event_prop_rules:
            if not rule.required <= input_props:
                continue
            input_non_required_props = input_props - rule.required
            if input_non_required_props <= rule.optional:
                return

        raise TagtooEventInvalidPropertiesError(
            event=event,
            event_prop_rules=event_prop_rules,
            input_props=input_props,
        )

    @root_validator(pre=True)
    def check_event_required_properties(cls, values):
        event = values['name']
        event_propertie_rules: List[set] = event_required_properties_map[event]

        input_event_level_props: set = values.keys() - {'name', 'custom_data'}
        input_custom_data_props: set = values.get('custom_data', {}).keys()

        cls._validate_properties(
            event,
            event_propertie_rules,
            input_event_level_props,
            input_custom_data_props,
        )

        return values

    @validator('items')
    def check_num_of_items(cls, v, values, **kwargs):
        if values['name'] in ('view_item_list', 'view_promotion') and len(v) > 10:
            raise ValueError(
                'Number of items should not be greater than 10 when event="view_item_list".'
            )
        return v


class TagtooUserData(BaseModel):
    em: Optional[str] = Field(None, title='Hashed Email')
    un: Optional[str] = Field(None, title='User Name')
    ph: Optional[str] = Field(None, title='Hashed Phone')
    gd: Optional[str] = Field(None, title='Hashed Gender')
    db: Optional[str] = Field(None, title='Hashed Date of birth')
    fbp: Optional[str] = Field(None, title='Facebook session ID')
    fbc: Optional[str] = Field(None, title='Facebook click ID')
    ga: Optional[str] = Field(None, title='Google analytics session ID')
    gid: Optional[str] = Field(None, title='Google analytics user ID')
    lmid: Optional[str] = Field(None, title='Crescendo LINE member ID')


class TagtooSession(BaseModel):
    id: str = Field(..., title='A short-lived session ID')
    source: Optional[str] = Field(
        None,
        title='the session source or utm_source query param',
    )
    medium: Optional[str] = Field(
        None,
        title='the session medium or utm_medium query param',
    )
    campaign: Optional[str] = Field(
        None,
        title='the session campaign or utm_campaign query param',
    )
    term: Optional[str] = Field(
        None,
        title='the session keyword or utm_term query param',
    )
    content: Optional[str] = Field(
        None,
        title='the session context or utm_content query param',
    )


translate_client = translate.Client()
LanguageCodes = tuple(lang['language'] for lang in translate_client.get_languages())


class TagtooEventData(BaseModel):
    permanent: str = Field(
        ..., title='a long-lived user ID hashed from FingerPrintJS and IP'
    )
    language: Literal[LanguageCodes]
    link: HttpUrl = Field(..., title='Current page link')
    referrer: Optional[AnyUrl] = Field(None, title='Previous page link')

    ip_address: Optional[str] = Field(None, title='Client IP address')
    user_agent: Optional[str] = Field(None, title='Client user agent')

    event: TagtooEvent = Field(..., title='A map that contains event details')
    user: Optional[TagtooUserData] = Field(
        {}, title='A map that contains user information'
    )
    session: TagtooSession = Field(..., title='A map that contains session information')


class TagtooEventDataBackend(TagtooEventData):
    '''
    This is for doing validations based on channel type, which is not
    provided via event API but event config API.
    '''

    user_agent: str  # Required when channel_type is backend
