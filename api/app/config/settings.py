import configparser
import logging.config
import os
from functools import lru_cache

from pydantic import BaseSettings

from environs import Env

env = Env()


class Settings(BaseSettings):
    DEBUG: bool = env.bool('DEBUG', False)
    SECRET_KEY: str = env.str('EVENT_API_SECRET_KEY')
    ACCESS_TOKEN_EXPIRE_MINUTES: int = env.int(
        'EVENT_API_ACCESS_TOKEN_EXPIRE_MINUTES', 30
    )
    LOGGING_CONFIG_FILE: str = env.str('EVENT_API_LOGGING_CONFIG_FILE')
    LOGGING_FILE: str = env.str('EVENT_API_LOGGING_FILE')
    API_DOC_USERNAME: str = env.str('EVENT_API_DOC_USERNAME')
    API_DOC_PASSWORD: str = env.str('EVENT_API_DOC_PASSWORD')
    API_TOKEN_HEADER: str = 'X-TOKEN'

    # GCP settings
    GCP_PROJECT_NAME: str = env.str('GCP_PROJECT_NAME')
    GCP_ERROR_REPORTING_SERVICE_NAME: str = env.str('GCP_ERROR_REPORTING_SERVICE_NAME')
    PUBSUB_EVENT_FACEBOOK_CAPI_TOPIC_NAME: str = env.str(
        'PUBSUB_EVENT_FACEBOOK_CAPI_TOPIC_NAME'
    )
    PUBSUB_EVENT_TAGTOO_EVENT_TOPIC_NAME: str = env.str(
        'PUBSUB_EVENT_TAGTOO_EVENT_TOPIC_NAME'
    )

    # Redis
    REDIS_HOST: str = env.str('REDIS_HOST')
    REDIS_TYPE: str = env.str('REDIS_TYPE', 'redis')
    REDIS_PORT: int = env.int('REDIS_PORT', 6379)

    # Services
    EVENT_CONFIG_API_SERVER_HOST: str = env.str('EVENT_CONFIG_API_SERVER_HOST')
    EVENT_CONFIG_API_TOKEN: str = env.str('EVENT_CONFIG_API_TOKEN')
    EVENT_CONFIG_API_TOKEN_EXPIRE_TIME: int = env.int(
        'EVENT_CONFIG_API_TOKEN_EXPIRE_TIME', 21600
    )  # sec
    EVENT_CONFIG_DATA_UPDATE_PERIOD: int = env.int(
        'EVENT_CONFIG_DATA_UPDATE_PERIOD', 3600
    )  # sec


@lru_cache
def get_settings():
    return Settings()


settings = get_settings()


# Logging Setup
class EnvInterpolation(configparser.BasicInterpolation):
    """Interpolation which expands environment variables in values."""

    def before_get(self, parser, section, option, value, defaults):
        value = super().before_get(parser, section, option, value, defaults)
        return os.path.expandvars(value)


def init_logging():
    os.makedirs(os.path.dirname(settings.LOGGING_FILE), exist_ok=True)
    log_config = configparser.ConfigParser(interpolation=EnvInterpolation())
    log_config.read(settings.LOGGING_CONFIG_FILE)
    logging.config.fileConfig(log_config, disable_existing_loggers=False)
