The documentation tool in this project is [MkDocs](https://www.mkdocs.org/getting-started/), with the
[Material](https://squidfunk.github.io/mkdocs-material/getting-started/) theme.

## Commands

1. Install the MkDocs and plugins in your virtual environment

   ```shell
   pip install mkdocs
   pip install mkdocs-material
   pip install mkdocstrings
   ```

2. Move to `docs` folder

   ```shell
   cd docs
   ```

3. Run server locally at [http://127.0.0.1:8000](http://127.0.0.1:8000)

   ```shell
   mkdocs serve
   ```

4. Deploy to GitHub page by pushing to the `gh-pages` branch

   ```shell
   mkdocs gh-deploy
   ```

!!! notes
    You should apply the modification at master branch or its branches via command mkdocs gh-deploy. DO NOT directly edit or push commit at gh-pages branch. For further information, see [https://www.mkdocs.org/user-guide/deploying-your-docs/#project-pages](https://www.mkdocs.org/user-guide/deploying-your-docs/#project-pages)

## Custom Domain

The custom domain is declared using the `CNAME` file. Please follow the [instructions](https://docs.github.com/en/pages/configuring-a-custom-domain-for-your-github-pages-site/managing-a-custom-domain-for-your-github-pages-site)
to configure the DNS proxy rules for the custom domain.
