# BigQuery Writer 雙寫入架構效能基準測試報告
測試時間: 2025-08-18T09:09:14.513554
測試版本: v2.1 (tracking_id 核心策略)

## 測試環境
- CPU: 8 cores
- Memory: 15 GB
- Python: 3.8.20 (default, Sep 27 2024, 06:05:08) 
[GCC 12.2.0]

## 測試結果摘要

### Light Load
**配置**: 10 msg/s × 60s

| 指標 | 單一寫入 | 雙寫入 | 影響 |
|------|---------|--------|------|
| 吞吐量 (msg/s) | 53.39 | 27.29 | -48.89% |
| CPU 使用率 | -12.10% | 0.70% | +12.80% |
| 記憶體使用率 | 0.00% | 0.00% | +0.00% |
| 平均延遲 (ms) | - | 36.45 | - |
| P95 延遲 (ms) | - | 39.39 | - |
| tracking_id 生成時間 (ms) | - | 0.0201 | - |
| 平均 raw_json 大小 (bytes) | - | 384 | - |
| 最大 raw_json 大小 (bytes) | - | 386 | - |

### Normal Load
**配置**: 50 msg/s × 120s

| 指標 | 單一寫入 | 雙寫入 | 影響 |
|------|---------|--------|------|
| 吞吐量 (msg/s) | 52.42 | 27.68 | -47.19% |
| CPU 使用率 | -11.50% | 1.20% | +12.70% |
| 記憶體使用率 | 0.00% | 0.00% | +0.00% |
| 平均延遲 (ms) | - | 36.00 | - |
| P95 延遲 (ms) | - | 38.68 | - |
| tracking_id 生成時間 (ms) | - | 0.0175 | - |
| 平均 raw_json 大小 (bytes) | - | 388 | - |
| 最大 raw_json 大小 (bytes) | - | 390 | - |

### Heavy Load
**配置**: 100 msg/s × 180s

| 指標 | 單一寫入 | 雙寫入 | 影響 |
|------|---------|--------|------|
| 吞吐量 (msg/s) | 56.52 | 27.27 | -51.75% |
| CPU 使用率 | -14.50% | 1.30% | +15.80% |
| 記憶體使用率 | -0.10% | 0.00% | +0.10% |
| 平均延遲 (ms) | - | 36.55 | - |
| P95 延遲 (ms) | - | 39.07 | - |
| tracking_id 生成時間 (ms) | - | 0.0190 | - |
| 平均 raw_json 大小 (bytes) | - | 391 | - |
| 最大 raw_json 大小 (bytes) | - | 394 | - |

### Peak Load
**配置**: 200 msg/s × 300s

| 指標 | 單一寫入 | 雙寫入 | 影響 |
|------|---------|--------|------|
| 吞吐量 (msg/s) | 55.98 | 27.69 | -50.53% |
| CPU 使用率 | -7.70% | 1.20% | +8.90% |
| 記憶體使用率 | 0.00% | -0.10% | -0.10% |
| 平均延遲 (ms) | - | 35.98 | - |
| P95 延遲 (ms) | - | 38.76 | - |
| tracking_id 生成時間 (ms) | - | 0.0232 | - |
| 平均 raw_json 大小 (bytes) | - | 392 | - |
| 最大 raw_json 大小 (bytes) | - | 394 | - |
