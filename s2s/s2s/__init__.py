import logging
import os

from . import settings
from .logging_utils import setup_cloud_logging, EnhancedCloudLoggingHandler, CustomStructuredLogHandler
from google.cloud.logging.handlers import StructuredLogHandler

# 在生產環境中設定 Google Cloud Logging
if settings.MODE == 'prod':
    # 獲取 root logger 並移除所有處理器
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 獲取 s2s logger 並移除所有處理器
    s2s_logger = logging.getLogger('s2s')
    for handler in s2s_logger.handlers[:]:
        s2s_logger.removeHandler(handler)

    # 使用自定義的 Cloud Logging 設置，使用 CustomStructuredLogHandler
    s2s_logger = setup_cloud_logging('s2s', project_id=settings.GCP_PROJECT_ID, use_structured=True)
    s2s_logger.setLevel(logging.getLevelName(settings.LOGGING_LEVEL))

    # 確保沒有其他處理器
    for handler in s2s_logger.handlers[:]:
        if not isinstance(handler, (StructuredLogHandler, CustomStructuredLogHandler)):
            s2s_logger.removeHandler(handler)
else:
    # 設定基本的 Python logging
    logging.basicConfig(format='[%(asctime)s %(levelname)s] %(message)s')
    s2s_logger = logging.getLogger('s2s')
    s2s_logger.setLevel(logging.getLevelName(settings.LOGGING_LEVEL))
