# Create a Cloud Router
resource "google_compute_router" "asia_east1_router" {
  name    = "${google_compute_network.event.name}-asia-east1-router"
  network = google_compute_network.event.name
  region  = "asia-east1"
  project = var.gcp_project
}

# Create a Cloud NAT gateway attached to the router above
resource "google_compute_router_nat" "asia_east1_nat" {
  name                                = "${google_compute_network.event.name}-asia-east1-nat"
  router                              = google_compute_router.asia_east1_router.name
  region                              = google_compute_router.asia_east1_router.region
  project                             = var.gcp_project
  nat_ip_allocate_option              = "AUTO_ONLY" # or "MANUAL_ONLY" if you want to specify static IPs
  source_subnetwork_ip_ranges_to_nat  = "ALL_SUBNETWORKS_ALL_IP_RANGES"
  enable_endpoint_independent_mapping = true

  log_config {
    enable = true
    filter = "ALL"
  }
}
