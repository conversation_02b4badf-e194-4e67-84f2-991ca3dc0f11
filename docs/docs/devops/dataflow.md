## Deployment

To deploy dataflow runtime, there is a little bit different from deploying over processes:
One should create and stage a template onto a GCS bucket first. See [here](https://cloud.google.com/dataflow/docs/guides/templates/creating-templates#create-and-stage-a-classic-template).

This action has been defined as a make command (for s2s as example):

```shell
make deploy-fb-capi-compressor-dataflow-template WORKSPACE=prod
```

This will create a template with its naming based on the date you trigger the command and the workspace you are going to deploy via terraform. The workspace can be specified by `WORKSPACE` parameter, just as above.

After generating the template, one still have to modify the template specified in terraform file (`dataflow.tf`) then trigger `terraform apply -var-file=prod.tfvars`. The runner will then be applied to GCP.

## Note

There was an issue we met when updating the version of apache-beam about compatibility. For further information, see <https://github.com/Tagtoo/ad-track/issues/169>.
