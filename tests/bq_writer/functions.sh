#!/bin/bash

error() { echo -e "\e[91m$1\e[m"; exit 1; }
success() { echo -e "\e[92m$1\e[m"; }

function purge_subscription() {
docker exec -i bq-writer python << END
import sys
from datetime import datetime
from google.cloud import pubsub_v1
subscriber = pubsub_v1.SubscriberClient()
try:
  subscriber.seek(request={'subscription': '$1', 'time': datetime.now()})
  sys.exit(0)
except Exception as exc:
  print(exc)
  sys.exit(-1)
END
}

function publish_test_data() {
docker exec -i bq-writer python << END
import sys
import json
import time
from google.cloud import pubsub_v1
publisher = pubsub_v1.PublisherClient()
try:
  with open('$1', 'r') as file:
    data = json.load(file)
  future = publisher.publish('$2', json.dumps(data).encode('utf-8'), version='$3', ec_id='$4')
  future.result()
  sys.exit(0)
except Exception as exc:
  print(exc)
  sys.exit(-1)
END
}

function validate_table_row() {
# Field ec_id is only use to identify row for different test case, this field won't be compared.
docker exec -i bq-writer python << END
import sys
import json
import traceback
from google.cloud import bigquery
from bq_writer import settings
client = bigquery.Client()
query = f"""
  SELECT * FROM \`{settings.GCP_PROJECT_ID}.event_test.{settings.BIGQUERY_TAGTOO_EVENT_TABLE_ID}\`
  WHERE ec_id = $2 LIMIT 1;
"""

try:
  query_job = client.query(query)
  row = [dict(row) for row in query_job][0]
  del row['ec_id']
  result = json.loads(json.dumps(row, default=str))
  with open('$1', 'r') as file:
    output = json.load(file)
    del output['ec_id']
  assert output == result, "Output data does not equals to query data from BigQuery."
  sys.exit(0)
except Exception as exc:
  print(traceback.format_exc())
  sys.exit(-1)
END
}