#!/bin/bash

error() { echo -e "\e[91m$1\e[m"; exit 0; }
success() { echo -e "\e[92m$1\e[m"; }

USER_AGENT="Mozilla/5.0+(compatible; IP2Location/MySQL-Docker; https://hub.docker.com/r/ip2location/mysql)"
CODES=("DB9")

if [ -f /ip2location.conf ]; then
	/etc/init.d/mysql restart >/dev/null 2>&1
	tail -f /dev/null
fi

if [ "$API_TOKEN" == "FALSE" ]; then
	error "Missing download token."
fi

if [ "$PRODUCT_CODE" == "FALSE" ]; then
	error "Missing database code."
fi

if [ "$MYSQL_PASSWORD" == "FALSE" ]; then
	MYSQL_PASSWORD="$(< /dev/urandom tr -dc _A-Z-a-z-0-9 | head -c${1:-12})"
fi

FOUND=""
for i in "${CODES[@]}"; do
	if [ "$i" == "$PRODUCT_CODE" ] ; then
		FOUND="$PRODUCT_CODE"
	fi
done

if [ -z $FOUND == "" ]; then
	error "Download code is invalid."
fi

CODE=$(echo $PRODUCT_CODE | sed 's/-//')

echo -n " > Create directory /_tmp "

mkdir /_tmp

[ ! -d /_tmp ] && error "[ERROR]" || success "[OK]"

cd /_tmp

echo -n " > Download IP2Location database "
echo "[DEBUG] IP_TYPE = '$IP_TYPE'"

# 設定下載目錄
DOWNLOAD_DIR="/ip2location_data"
mkdir -p "$DOWNLOAD_DIR"

# 設定檔案路徑
IPV4_ZIP="$DOWNLOAD_DIR/ipv4.zip"
IPV6_ZIP="$DOWNLOAD_DIR/ipv6.zip"

# 設定下載 URL
IPV4_URL="https://www.ip2location.com/download?token=${API_TOKEN}&code=${CODE}CSV"
IPV6_URL="https://www.ip2location.com/download?token=${API_TOKEN}&code=${CODE}CSVIPV6"

# **函數：檢查 ZIP 檔案是否有效**
check_zip_validity() {
    local file="$1"
    
    # 如果檔案不存在或大小為 0，則返回失敗
    if [ ! -f "$file" ] || [ ! -s "$file" ]; then
        echo "[ERROR] $file is missing or empty!"
        return 1
    fi

    # 顯示檔案類型
    file "$file"

    # 如果是 ASCII 文字，則表示下載失敗（通常是 API 錯誤訊息）
    if file "$file" | grep -q "ASCII text"; then
        echo "[ERROR] $file is not a valid ZIP file! Possible error message inside:"
        cat "$file"
        return 1
    fi

    # 檢查 ZIP 檔案完整性
    if ! unzip -t "$file" >/dev/null 2>&1; then
        echo "[ERROR] $file is corrupted!"
        return 1
    fi

    echo "[INFO] $file is a valid ZIP file."
    return 0
}

download_if_needed() {
    local file="$1"
    local url="$2"

    if check_zip_validity "$file"; then
        echo "[INFO] Using existing file: $file"
        return 0
    fi

    echo "[INFO] Downloading $file from $url..."
    wget -O "$file" --user-agent="$USER_AGENT" "$url"

    # 再次檢查 ZIP 檔案是否有效
    if ! check_zip_validity "$file"; then
        echo "[ERROR] Download failed or file is corrupted: $file"
        exit 1
    fi
}


if [ "$IP_TYPE" = "IPV4" ]; then
	wget -O ipv4.zip -q --user-agent="$USER_AGENT" "https://www.ip2location.com/download?token=${API_TOKEN}&code=${CODE}CSV" > /dev/null 2>&1

	[ ! -z "$(grep 'NO PERMISSION' database.zip)" ] && error "[DENIED]"

	RESULT=$(unzip -t ipv4.zip >/dev/null 2>&1)

	#[ $? -ne 0 ] && error "[FILE CORRUPTED]"
	if [ $? -ne 0 ]; then
        echo "[FILE CORRUPTED ipv4]"
        echo "$RESULT"
        exit 1
    fi
elif [ "$IP_TYPE" = "IPV6" ]; then
	wget -O ipv6.zip -q --user-agent="$USER_AGENT" "https://www.ip2location.com/download?token=${API_TOKEN}&code=${CODE}CSVIPV6" > /dev/null 2>&1

	[ ! -z "$(grep 'NO PERMISSION' database.zip)" ] && error "[DENIED]"

	RESULT=$(unzip -t ipv6.zip >/dev/null 2>&1)

	if [ $? -ne 0 ]; then
        echo "[FILE CORRUPTED ipv6]"
        echo "$RESULT"
        exit 1
    fi
else
	# **下載或使用快取的 ZIP 檔案**
	download_if_needed "$IPV4_ZIP" "$IPV4_URL"
	download_if_needed "$IPV6_ZIP" "$IPV6_URL"


	# echo "[INFO] Downloading IP2Location database..."
	# wget -O ipv4.zip -q --user-agent="$USER_AGENT" "https://www.ip2location.com/download?token=${API_TOKEN}&code=${CODE}CSV"
	# wget -O ipv6.zip -q --user-agent="$USER_AGENT" "https://www.ip2location.com/download?token=${API_TOKEN}&code=${CODE}CSVIPV6"

	# # **檢查檔案是否成功下載**
	# ls -lh ipv4.zip ipv6.zip
	# file ipv4.zip
	# file ipv6.zip

	# for FILE in ipv4.zip ipv6.zip; do
	# 	echo "[DEBUG] Checking file type for $FILE..."
	# 	file "$FILE"

	# 	echo "[DEBUG] Displaying first 20 lines of $FILE:"
	# 	cat "$FILE" | head -n 20

	# 	# 如果是 ASCII 文字，顯示完整內容
	# 	if file "$FILE" | grep -q "ASCII text"; then
	# 		echo "[ERROR] $FILE is not a valid ZIP file! Possible error message inside:"
	# 		cat "$FILE"
	# 		exit 1
	# 	fi
	# done


	# # **檢查 NO PERMISSION**
	# [ ! -z "$(grep 'NO PERMISSION' ipv4.zip)" ] && error "[DENIED]"
	# [ ! -z "$(grep 'NO PERMISSION' ipv6.zip)" ] && error "[DENIED]"

	# # **檢查 ZIP 是否有效**
	# RESULT=$(unzip -t ipv4.zip 2>&1)
	# if [ $? -ne 0 ]; then
	# 	echo "[FILE CORRUPTED ipv4]"
	# 	echo "$RESULT"
	# 	exit 1
	# fi

	# RESULT=$(unzip -t ipv6.zip 2>&1)
	# if [ $? -ne 0 ]; then
	# 	echo "[FILE CORRUPTED ipv6]"
	# 	echo "$RESULT"
	# 	exit 1
	# fi
fi

success "[OK]"

for ZIP in $(ls | grep '.zip'); do
	CSV=$(unzip -l $ZIP | grep '.CSV' | awk '{ print $4 }')
	echo -n " > Decompress $CSV from $ZIP "

	unzip -jq $ZIP $CSV

	if [ ! -f $CSV ]; then
		error "[ERROR]"
	fi

	success "[OK]"
done






echo "Starting MySQL..."
nohup mysqld --datadir=/var/lib/mysql --socket=/var/run/mysqld/mysqld.sock > /dev/null 2>&1 &

# 確保 MySQL 目錄存在並擁有正確的權限
mkdir -p /var/run/mysqld /var/lib/mysql /var/log/mysql
chown -R mysql:mysql /var/run/mysqld /var/lib/mysql /var/log/mysql
chmod 755 /var/run/mysqld /var/log/mysql

# 檢查 MySQL 資料目錄是否已初始化
if [ ! -d "/var/lib/mysql/mysql" ]; then
    echo "[INFO] Initializing MySQL database..."
    mysqld --initialize-insecure --user=mysql --datadir=/var/lib/mysql
fi

# 確保錯誤日誌文件存在
touch /var/log/mysql/error.log
chown mysql:mysql /var/log/mysql/error.log

# 使用 mysqld_safe 來啟動 MySQL
echo "Starting MySQL using mysqld_safe..."
nohup mysqld_safe --datadir=/var/lib/mysql --socket=/var/run/mysqld/mysqld.sock > /dev/null 2>&1 &

# 等待 MySQL 完全啟動
echo "Waiting for MySQL socket to be created..."
for i in {1..10}; do
    if [ -S /var/run/mysqld/mysqld.sock ]; then
        echo "MySQL socket is ready!"
        break
    fi
    echo "MySQL is not ready yet... retrying in 2 seconds..."
    sleep 2
done


echo "[DEBUG] Checking if MySQL process is running..."
ps aux | grep -E 'mysqld|mariadb' | grep -v grep

if ! ps aux | grep -E 'mysqld|mariadb' | grep -v grep > /dev/null; then
    echo "[ERROR] MySQL did not start successfully."
    echo "[DEBUG] Checking MySQL logs..."
    ls -lah /var/log/mysql/
    cat /var/log/mysql/error.log || echo "[ERROR] No MySQL error log found!"
    exit 1
fi

# 再次確認 MySQL 可以連線
if ! mysqladmin ping -uroot --silent; then
    echo "[ERROR] MySQL is not responding."
    exit 1
fi

echo "[OK] MySQL is running and ready!"


echo -n " > [MySQL] Create database \"ip2location_database\" "
RESPONSE="$(mysql -e 'CREATE DATABASE IF NOT EXISTS ip2location_database' 2>&1)"

[ ! -z "$(echo $RESPONSE)" ] && error "[$RESPONSE]" || success "[OK]"

echo -n " > [MySQL] Create table \"ip2location_database_tmp\" "

RESPONSE="$(mysql ip2location_database -e 'DROP TABLE IF EXISTS ip2location_database_tmp' 2>&1)"

case "$CODE" in
	DB9|DB9LITE )
		FIELDS=',`region_name` VARCHAR(128) NOT NULL,`city_name` VARCHAR(128) NOT NULL,`latitude` DOUBLE NULL DEFAULT NULL,`longitude` DOUBLE NULL DEFAULT NULL,`zip_code` VARCHAR(30) NULL DEFAULT NULL'
	;;
esac

RESPONSE="$(mysql ip2location_database -e 'CREATE TABLE ip2location_database_tmp (`ip_from` DECIMAL(39,0) UNSIGNED NOT NULL,`ip_to` DECIMAL(39,0) UNSIGNED NOT NULL,`country_code` CHAR(2) NOT NULL,`country_name` VARCHAR(64) NOT NULL'"$FIELDS"',INDEX `idx_ip_to` (`ip_to`)) ENGINE=MyISAM' 2>&1)"

[ ! -z "$(echo $RESPONSE)" ] && error "[ERROR]" || success "[OK]"

for CSV in $(ls | grep '.CSV'); do
	echo -n " > [MySQL] Load $CSV into database "
	RESPONSE="$(mysql ip2location_database -e 'LOAD DATA LOCAL INFILE '\'''$CSV''\'' INTO TABLE ip2location_database_tmp FIELDS TERMINATED BY '\'','\'' ENCLOSED BY '\''\"'\'' LINES TERMINATED BY '\''\r\n'\''' 2>&1)"
	[ ! -z "$(echo $RESPONSE)" ] && error "[ERROR]" || success "[OK]"
done

echo -n " > [MySQL] Drop table \"ip2location_database\" "

RESPONSE="$(mysql ip2location_database -e 'DROP TABLE IF EXISTS ip2location_database' 2>&1)"

[ ! -z "$(echo $RESPONSE)" ] && error "[ERROR]" || success "[OK]"

echo -n " > [MySQL] Rename table \"ip2location_database_tmp\" to \"ip2location_database\" "

RESPONSE="$(mysql ip2location_database -e 'RENAME TABLE ip2location_database_tmp TO ip2location_database' 2>&1)"

[ ! -z "$(echo $RESPONSE)" ] && error "[ERROR]" || success "[OK]"