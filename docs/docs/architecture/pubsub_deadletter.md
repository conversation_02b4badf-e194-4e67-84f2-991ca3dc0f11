# Pub/Sub 死信佇列機制

## 概述

死信佇列（Dead Letter Queue，簡稱 DLQ）是一種消息處理模式，用於處理無法成功處理的消息。在我們的系統中，我們為 `tagtoo-event-to-bigquery` 訂閱實現了死信佇列機制，以提高系統的可靠性和彈性。

## 架構

```mermaid
flowchart LR
    Publisher[Publisher] -->|發布消息| Topic[tagtoo-event 主題]
    Topic -->|訂閱| Subscription[tagtoo-event-to-bigquery 訂閱]
    Subscription -->|處理成功| BigQuery[(BigQuery)]
    Subscription -->|處理失敗\n5次嘗試後| DeadLetter[tagtoo-event-to-bigquery-deadletter 主題]
    DeadLetter -->|訂閱| DeadLetterSub[tagtoo-event-to-bigquery-deadletter-sub 訂閱]
    DeadLetterSub -->|分析失敗原因| Analysis[失敗分析]
    DeadLetterSub -.->|可選: 修復後重新處理| Topic
```

死信佇列機制的工作流程如下：

1. 消息發布到 `tagtoo-event` 主題
2. `tagtoo-event-to-bigquery` 訂閱接收消息並嘗試處理
3. 如果處理失敗（例如，消息格式錯誤、處理超時等），消息會在多次嘗試後被轉發到死信主題
4. 死信主題 `tagtoo-event-to-bigquery-deadletter` 存儲這些失敗的消息
5. 死信訂閱 `tagtoo-event-to-bigquery-deadletter-sub` 可以被用來處理或分析這些失敗的消息

## 配置

死信佇列機制通過 Terraform 進行配置，相關配置位於 `deploy/pubsub.tf` 文件中：

```hcl
resource "google_pubsub_topic" "tagtoo_event_to_bigquery_deadletter" {
  name       = "tagtoo-event-to-bigquery-deadletter-${terraform.workspace}"
  depends_on = [google_project_service.gcp_services]
  labels = {
    environment = terraform.workspace
  }
}

resource "google_pubsub_subscription" "tagtoo_event_to_bigquery_subs" {
  for_each = toset(var.event_api.api_versions)

  name  = "tagtoo-event-to-bigquery-${each.key}-${var.environment}"
  topic = google_pubsub_topic.tagtoo_event.name
  labels = {
    environment = var.environment
    component   = "tagtoo-event"
    consumer    = "bq-writer-subscriber"
    destination = "bigquery"
  }
  filter                     = "attributes.version=\"${each.key}\""
  message_retention_duration = "604800s" # 7 day
  retain_acked_messages      = false
  ack_deadline_seconds       = 60
  enable_message_ordering    = false

  dead_letter_policy {
    dead_letter_topic     = google_pubsub_topic.tagtoo_event_to_bigquery_deadletter.id
    max_delivery_attempts = 5
  }
}

resource "google_pubsub_subscription" "tagtoo_event_to_bigquery_deadletter_sub" {
  name  = "tagtoo-event-to-bigquery-deadletter-sub-${terraform.workspace}"
  topic = google_pubsub_topic.tagtoo_event_to_bigquery_deadletter.name
  labels = {
    environment = terraform.workspace
    component   = "tagtoo-event"
    consumer    = "bq-writer-subscriber"
    destination = "bigquery"
  }
  message_retention_duration = "604800s" # 7 day
  retain_acked_messages      = false
  ack_deadline_seconds       = 60
  expiration_policy {
    ttl = "2678400s" # 31 days
  }
}
```

## 關鍵參數

- **max_delivery_attempts**: 消息在被轉發到死信主題之前的最大嘗試次數（設置為 5）
- **message_retention_duration**: 消息在主題中保留的時間（設置為 7 天）
- **expiration_policy**: 死信訂閱的過期策略（設置為 31 天）

## 監控與告警

我們已經設置了監控和告警，以便在死信佇列中積累過多消息時通知團隊。相關配置位於 `deploy/monitoring.tf` 文件中。

### 告警說明

當消息無法被正常處理並轉移到死信佇列時，系統會自動觸發告警。這些告警會顯示在 Google Cloud Console 的 Monitoring > Alerting 頁面中，並可能通過配置的通知渠道（如電子郵件、Slack 等）發送通知。

告警示例：

- **告警名稱**：Unacked messages in deadletter queue
- **條件**：死信佇列中有未確認的消息
- **持續時間**：通常設置為幾分鐘，表示問題持續存在的時間
- **嚴重性**：根據配置可能是 INFO、WARNING 或 CRITICAL

### 告警處理流程

當收到死信佇列告警時，應按照以下步驟處理：

1. **確認告警**：在 Google Cloud Console 中確認告警，表示您已經注意到問題

2. **檢查死信佇列**：使用 Google Cloud Console 或 gcloud 命令查看死信佇列中的消息數量和內容

   ```bash
   # 查看死信佇列的統計信息
   gcloud pubsub subscriptions describe projects/tagtoo-tracking/subscriptions/tagtoo-event-to-bigquery-deadletter-sub-prod

   # 拉取死信佇列中的消息（不會確認消息，僅查看）
   gcloud pubsub subscriptions pull \
     projects/tagtoo-tracking/subscriptions/tagtoo-event-to-bigquery-deadletter-sub-prod \
     --limit=10 --format=json

   # 拉取並確認消息（會從佇列中移除消息）
   gcloud pubsub subscriptions pull --auto-ack \
     projects/tagtoo-tracking/subscriptions/tagtoo-event-to-bigquery-deadletter-sub-prod \
     --limit=10 --format=json
   ```

3. **分析失敗原因**：

   !!! warning "重要提示"
       Pub/Sub 死信佇列本身不會記錄消息處理失敗的具體原因。要確定失敗原因，需要結合分析消息內容和查看應用程序日誌。

   a. **檢查消息屬性**：死信佇列中的消息會包含原始消息的所有屬性，以及一些額外的屬性，如 `deadLetterSourceSubscription`（原始訂閱）和 `deliveryAttempt`（嘗試傳遞次數），但不會包含失敗的具體原因。

   b. **檢查消息內容**：解析消息的數據部分，查看是否有格式錯誤或缺少必要字段。這可能有助於推斷失敗的原因。

   c. **查看處理程序日誌**：檢查 `bq-writer-tagtoo-event-subscriber` 的日誌，這是確定失敗原因的最直接方法。

   ```bash
   # 查看處理程序的最近日誌
   kubectl logs -f deployment/bq-writer-tagtoo-event-subscriber --tail=100

   # 或者查看特定 pod 的日誌
   kubectl logs -f <pod-name>
   ```

   d. **查看 Cloud Logging**：在 Google Cloud Logging 中查找與消息處理相關的錯誤日誌

   ```bash
   # 在 Cloud Logging 中查找與特定消息 ID 相關的錯誤日誌
   gcloud logging read "resource.type=k8s_container AND resource.labels.namespace_name=default AND resource.labels.container_name=bq-writer-tagtoo-event-subscriber AND severity>=ERROR AND textPayload:MESSAGE_ID" --limit=10

   # 查看最近的錯誤日誌
   gcloud logging read "resource.type=k8s_container AND resource.labels.namespace_name=default AND resource.labels.container_name=bq-writer-tagtoo-event-subscriber AND severity>=ERROR" --limit=20
   ```

   e. **常見失敗原因**：
      - 消息格式不符合預期（如 JSON 格式錯誤）
      - 消息缺少必要字段或字段類型錯誤
      - BigQuery 表結構與消息結構不匹配
      - 處理超時（如 IP2Location 查詢過慢）
      - 權限問題（如 BigQuery 寫入權限不足）
      - 網絡連接問題
      - 資源限制（如內存不足）

4. **修復問題**：根據失敗原因修復代碼或配置問題

5. **重新處理消息**：如果需要，將死信佇列中的消息重新發送到原始主題進行處理

   ```bash
   # 使用腳本將死信佇列中的消息重新發送到原始主題
   # 注意：這需要先編寫一個腳本來讀取死信佇列並重新發布消息
   python scripts/republish_deadletter_messages.py \
     --source-subscription=projects/tagtoo-tracking/subscriptions/tagtoo-event-to-bigquery-deadletter-sub-prod \
     --target-topic=projects/tagtoo-tracking/topics/tagtoo-event
   ```

6. **驗證修復**：確保修復後，新的消息不再進入死信佇列

   ```bash
   # 監控死信佇列的消息數量
   watch -n 60 "gcloud pubsub subscriptions describe projects/tagtoo-tracking/subscriptions/tagtoo-event-to-bigquery-deadletter-sub-prod | grep 'message_retention_duration\|message_count'"
   ```

## 故障排除

如果您發現大量消息被轉發到死信佇列，請按照以下步驟進行故障排除：

1. 檢查 `bq-writer-tagtoo-event-subscriber` 的日誌，查找處理失敗的原因
2. 檢查消息格式是否符合預期
3. 確認 BigQuery 表結構是否與消息結構匹配
4. 檢查 BigQuery 寫入權限是否正確

## 恢復處理

如果需要重新處理死信佇列中的消息，可以使用以下方法：

1. 創建一個臨時訂閱來讀取死信主題中的消息
2. 使用腳本將這些消息重新發布到原始主題
3. 確保修復了導致消息處理失敗的根本原因

## 最佳實踐

- 定期檢查死信佇列中的消息數量
- 分析死信消息的模式，以識別系統中的潛在問題
- 考慮實現自動化流程來處理常見的死信情況
