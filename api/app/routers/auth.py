import secrets

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.openapi.docs import get_redoc_html, get_swagger_ui_html
from fastapi.openapi.utils import get_openapi
from fastapi.security import HTT<PERSON><PERSON>asi<PERSON>, HTTPBasicCredentials
from starlette.requests import Request

from app.config import settings

router = APIRouter()


# to get a string like this run:
# openssl rand -hex 32
ALGORITHM = 'HS256'

fake_users_db = {
    'tagtoo_admin': {
        'username': 'tagtoo_admin',
        'full_name': 'Tagtoo Admin',
        'email': '<EMAIL>',
        'hashed_password': '$2b$12$W1QeRYj9xNSrKvpib.GTkusKA.uHfWd.c3fkbPC9Q.xGEXvKT67Ne',
        'disabled': False,
    }
}

security = HTTPBasic()


def get_current_username(credentials: HTTPBasicCredentials = Depends(security)):
    correct_username = secrets.compare_digest(
        credentials.username, settings.API_DOC_USERNAME
    )
    correct_password = secrets.compare_digest(
        credentials.password, settings.API_DOC_PASSWORD
    )
    if not (correct_username and correct_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Basic"},
        )
    return credentials.username


@router.get("/docs", tags=['docs'], include_in_schema=False)
async def get_documentation(
    username: str = Depends(get_current_username),
):
    return get_swagger_ui_html(openapi_url="/openapi.json", title="docs")


@router.get('/redoc', tags=['docs'], include_in_schema=False)
async def get_documentation_redoc(
    username: str = Depends(get_current_username),
):
    return get_redoc_html(openapi_url='/openapi.json', title='docs')


@router.get("/openapi.json", tags=['docs'], include_in_schema=False)
async def openapi(request: Request, username: str = Depends(get_current_username)):
    return get_openapi(title="FastAPI", version="0.1.0", routes=request.app.routes)
