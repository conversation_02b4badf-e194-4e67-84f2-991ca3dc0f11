WITH group_results AS (
    SELECT
        neighbor_data.base_permanent AS permanent,
        ARRAY_AGG(
            neighbor_data.neighbors
            ORDER BY
                entity_time,
                neighbors
        ) AS neighbors,
        ARRAY_AGG(
            entity_time
            ORDER BY
                entity_time,
                neighbors
        ) AS entity_times,
        ARRAY_AGG(DISTINCT em IGNORE NULLS) AS emails,
        ARRAY_AGG(DISTINCT ph IGNORE NULLS) AS phones,
        ARRAY_AGG(DISTINCT un IGNORE NULLS) AS usernames,
        -- ARRAY_AGG(DISTINCT fbp IGNORE NULLS) AS fbps,
        ARRAY_AGG(DISTINCT gid IGNORE NULLS) AS gids
    FROM
        `tagtoo-tracking.event_prod.get_entity_neighbors_by_date`(start_date, end_date) neighbor_data
    GROUP BY
        1
)
SELECT
    group_id,
    group_time,
    ARRAY_AGG(DISTINCT permanent) permanents,
    ARRAY_CONCAT_AGG(emails) emails,
    ARRAY_CONCAT_AGG(phones) phones,
    ARRAY_CONCAT_AGG(usernames) usernames,
    -- <PERSON><PERSON>Y_CONCAT_AGG(fbps) fbps,
    ARRAY_CONCAT_AGG(gids) gids
FROM
    (
        SELECT
            permanent,
            entity_times [OFFSET (0)] group_time,
            neighbors [OFFSET (0)] group_id,
            emails,
            phones,
            usernames,
            -- fbps,
            gids
        FROM
            group_results
        ORDER BY
            group_id
    )
GROUP BY
    1,
    2