# 系統概述

User Unify 是一個用於識別和統一用戶身份的系統，通過分析用戶的各種標識符（如電子郵件、手機號碼、用戶名等）來確定哪些標識符屬於同一個用戶。系統目前使用 V2 版本，基於 BigQuery 實現，每天通過 cron job 定期執行處理流程。

## 主要功能

- **身份統一**：識別和關聯同一用戶的多個標識符
- **用戶畫像**：建立完整的用戶身份視圖
- **查詢優化**：提供高效的用戶群組查詢

## 系統架構

User Unify V2 系統基於 BigQuery 實現，主要由以下組件構成：

1. **數據收集層**：從 tagtoo_event 表中提取用戶數據
2. **處理層**：執行用戶身份統一算法
3. **存儲層**：將結果儲存在 BigQuery 表中
4. **查詢層**：提供高效的查詢接口

## 系統優勢

- **高效性**：利用 BigQuery 的大數據處理能力
- **可擴展性**：支持大規模用戶數據處理
- **可靠性**：提供快照和回滾機制
- **準確性**：通過多種標識符匹配提高準確率

## 主要步驟與流程

User Unify 系統的處理流程分為以下主要步驟：

1. **創建快照**：執行操作前創建 user_unify_groups 表的快照
2. **獲取每日分組結果**：從 tagtoo_event 表中提取當天的用戶數據並分組
3. **合併新實體**：將每日分組結果合併到 user_unify_groups 表中
4. **預測候選群組**：找出每個 permanent 可能屬於的 group_id 群組
5. **解析多候選群組**：使用 Union Find 算法處理一個 permanent 屬於多個 group_id 的情況
6. **結果寫入**：將處理結果寫入相關表
7. **錯誤處理**：提供錯誤處理和回滾機制

詳細的步驟說明請參考[詳細工作流程](workflow.md)。
