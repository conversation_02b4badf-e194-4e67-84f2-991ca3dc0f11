#!/bin/bash
#
# E2E test from Pub/Sub topic to BigQuery Table
#
# Usage:
#   command topic_path subscription_path input_file output_file

source ./functions.sh

topic_path="$1"
subscription_path="$2"
input_file="$3"
output_file="$4"
# In BigQuery, it's difficult to truncate table, in order to identify row for each test case, use an inconstant value.
ec_id=$$

trap "exit 1" HUP INT PIPE QUIT TERM
trap "rm ../../bq_writer/test_input_data.$$ ../../bq_writer/test_output_data.$$" EXIT

echo -n " > Purge subscription $subscription_path ... "

if purge_subscription "$subscription_path"; then success '[OK]'; else error '[ERROR]'; fi

echo -n " > Copy test data into bq-writer container ... "

docker cp "$input_file" bq-writer:/app/test_input_data.$$
docker cp "$output_file" bq-writer:/app/test_output_data.$$

function test_data_exists(){
  docker exec -i bq-writer sh -c "[ -f /app/test_input_data.$$ ] && [ -f /app/test_output_data.$$ ] && exit 0 || exit 1"
}

if test_data_exists; then success '[OK]'; else error '[ERROR]'; fi

echo -n " > Publish test data into topic $topic_path ... "

if publish_test_data "/app/test_input_data.$$" "$topic_path" "v1" "$ec_id"; then success '[OK]'; else error '[ERROR]'; fi

echo -n " > Wait for data inserting into BigQuery table "

sleep 5

echo -n " > Validate data from BigQuery table ... "

if validate_table_row "/app/test_output_data.$$" "$ec_id"; then success '[OK]'; else error '[ERROR]'; fi