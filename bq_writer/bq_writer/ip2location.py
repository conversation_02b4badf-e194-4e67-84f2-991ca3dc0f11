from dataclasses import dataclass
from contextlib import contextmanager
import ipaddress
import mariadb
from . import settings


@dataclass
class Location:
    country_code: str
    region_name: str
    city_name: str
    latitude: float
    longitude: float
    zip_code: str


@contextmanager
def ip2location_connection():
    conn = mariadb.connect(
        user='root', host=settings.IP2LOCATION_DB_HOST, database='ip2location_database'
    )
    try:
        yield conn
    except mariadb.Error:
        raise
    finally:
        conn.close()


def is_internal_ip(ip: str) -> bool:
    """檢查 IP 是否為內部 IP 地址"""
    try:
        ip_obj = ipaddress.ip_address(ip)
        # 檢查 IP 是否為私有地址或回環地址
        return ip_obj.is_private or ip_obj.is_loopback
    except ValueError:
        # 無效的 IP 地址
        return False


def query_location(ip: str) -> Location:
    # 檢查是否為內部 IP
    if is_internal_ip(ip):
        return None

    with ip2location_connection() as conn:
        cursor = conn.cursor()

        # 檢查是否為 IPv6 地址
        is_ipv6 = ':' in ip

        if is_ipv6:
            # 使用更複雜的查詢處理 IPv6 地址，因為 MariaDB 不支援 128 位元
            cursor.execute(
                """
                SELECT
                    `country_code`,
                    `region_name`,
                    `city_name`,
                    `latitude`,
                    `longitude`,
                    `zip_code`
                FROM (
                    SELECT INET6_ATON(?) AS ip_bin
                ) AS ip,
                ip2location_database AS ip_info
                WHERE (
                    CAST(CONV(SUBSTRING(HEX(ip.ip_bin), 1, 16), 16, 10) AS DECIMAL(39, 0)) * POW(2, 64) +
                    CAST(CONV(SUBSTRING(HEX(ip.ip_bin), 17, 16), 16, 10) AS DECIMAL(39, 0))
                ) BETWEEN ip_info.ip_from AND ip_info.ip_to
                LIMIT 1;
                """,
                (ip,)
            )
        else:
            # 使用 INET_ATON 函數處理 IPv4 地址
            cursor.execute(
                """
                SELECT
                    `country_code`,
                    `region_name`,
                    `city_name`,
                    `latitude`,
                    `longitude`,
                    `zip_code`
                FROM `ip2location_database` WHERE INET_ATON(?) BETWEEN ip_from AND ip_to LIMIT 1;
                """,
                (ip,)
            )

        result = cursor.fetchone()
        cursor.close()
        # Required a valid country at least
        if result and result[0] != '-':
            # Replace values {'-', 0.0} to None
            return Location(
                *map(lambda x: x if x != '-' and x != 0.0 else None, result)
            )
        return None
