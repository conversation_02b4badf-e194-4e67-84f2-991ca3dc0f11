import json
from datetime import datetime
from functools import reduce
from google.cloud import pubsub_v1
from .exceptions import MessageParseError


def parse_json(value):
    return json.loads(value) if value else []


def parse_int(value):
    return int(float(value))


def parse_date(value, date_format='%m/%d/%Y'):
    return datetime.strptime(value, date_format).date()


def parse_string(value):
    if isinstance(value, bytes):
        value = value.decode('utf-8')
    cleaned = str(value).strip()
    if not cleaned:
        raise ValueError('Empty String')
    return cleaned


def parse_boolean(value):
    try:
        parsed = int(value)
        assert parsed in (0, 1)
        return parsed == 1
    except (ValueError, AssertionError):
        pass

    try:
        parsed = str(value).strip().lower()
        assert parsed in ('true', 'false')
        return parsed == 'true'
    except (ValueError, AssertionError):
        pass

    raise ValueError(f'Cannot convert {value} to type Boolean.')


class MessageParserMixin(object):
    message_data_parsers = (parse_json,)
    message_attribute_parsers = {}

    def __init__(self):
        super().__init__()

    def parse(self, message: pubsub_v1.subscriber.message.Message):
        try:
            attr_not_exists = set(self.message_attribute_parsers.keys()) - set(
                message.attributes.keys()
            )
            assert (
                not attr_not_exists
            ), f"Message attributes {attr_not_exists} not exists."
            return {
                'data': reduce(
                    lambda x, func: func(x), self.message_data_parsers, message.data
                ),
                **{
                    key: self.message_attribute_parsers[key](value)
                    if key in self.message_attribute_parsers
                    else value
                    for key, value in message.attributes.items()
                },
            }
        except Exception as exc:
            raise MessageParseError(exc)
