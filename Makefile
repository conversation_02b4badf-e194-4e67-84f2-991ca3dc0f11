GCP_PROJECT=tagtoo-tracking
GCR_URI=asia.gcr.io/${GCP_PROJECT}
SEARCH_CONFIG:=$(shell gcloud config configurations list | awk '$$1 ~ /${GCP_PROJECT}/' | wc -l)
BRANCH_NAME=$(shell git rev-parse --abbrev-ref HEAD | sed 's/\//-/g' )
TAG_NAME=${BRANCH_NAME}-$(shell git rev-parse --short HEAD)
EVENT_API_IMAGE=${GCR_URI}/event-api
EVENT_S2S_IMAGE = ${GCR_URI}/event-s2s
EVENT_CONFIG_API_IMAGE=${GCR_URI}/event-config-api
EVENT_IP2LOCATION_IMAGE=${GCR_URI}/ip2location-mysql
EVENT_BQ_WRITER_IMAGE=${GCR_URI}/event-bq-writer
EVENT_UU_IMAGE=${GCR_URI}/event-user-unify
IP2LOCATION_API_TOKEN=fVbjKMZgUzOiJ8Gyz3y1N6R33EB6IDQjEfgtJMjehbezHicZPJrOwnttEUM4dLds
IP2LOCATION_PRODUCT_CODE=DB9

env:
	@if [ $(SEARCH_CONFIG) = 0 ]; then \
		gcloud config configurations create ${GCP_PROJECT}; \
		gcloud config set project ${GCP_PROJECT}; \
		gcloud config set compute/region asia-east1; \
		gcloud config set compute/zone asia-east1-a; \
		gcloud auth activate-service-account --key-file=./gcloud/sa/terraform-user.json; \
	else \
	  gcloud config configurations activate ${GCP_PROJECT}; \
	fi

event-api-up:
	docker-compose -f docker-compose.api.yml up

event-api-down:
	docker-compose -f docker-compose.api.yml down

config-docker:
	gcloud auth configure-docker --quiet

pull-dev-latest: env config-docker
	docker pull ${EVENT_API_IMAGE}:dev-latest
	docker tag ${EVENT_API_IMAGE}:dev-latest ad_track_api
	docker pull ${EVENT_S2S_IMAGE}:dev-latest
	docker tag ${EVENT_S2S_IMAGE}:dev-latest ad_track_s2s
	docker pull ${EVENT_CONFIG_API_IMAGE}:dev-latest
	docker tag ${EVENT_CONFIG_API_IMAGE}:dev-latest ad_track_config_api
	docker pull ${EVENT_BQ_WRITER_IMAGE}:dev-latest
	docker tag ${EVENT_BQ_WRITER_IMAGE}:dev-latest ad_track_bq_writer

build-event-api: env config-docker
	DOCKER_BUILDKIT=1 docker buildx build --platform linux/amd64 -f api/Dockerfile -t ${GCR_URI}/event-api:${TAG_NAME} ./api
	@if [ "$(LATEST)" = "true" ]; then \
		docker tag ${EVENT_API_IMAGE}:${TAG_NAME} ${EVENT_API_IMAGE}:dev-latest; \
		docker push ${EVENT_API_IMAGE}:dev-latest; \
	fi
	docker push ${EVENT_API_IMAGE}:${TAG_NAME}
	rm ~/.docker/config.json

build-event-s2s: env config-docker
	DOCKER_BUILDKIT=1 docker buildx build --platform linux/amd64 -f s2s/Dockerfile -t ${EVENT_S2S_IMAGE}:${TAG_NAME} .
	@if [ "$(LATEST)" = "true" ]; then \
		docker tag ${EVENT_S2S_IMAGE}:${TAG_NAME} ${EVENT_S2S_IMAGE}:dev-latest; \
		docker push ${EVENT_S2S_IMAGE}:dev-latest; \
	fi
	docker push ${EVENT_S2S_IMAGE}:${TAG_NAME}
	rm ~/.docker/config.json

build-event-s2s-cloud: env config-docker
	gcloud builds submit --config s2s/cloudbuild.yaml --substitutions=_TAG_NAME=${TAG_NAME} .

build-event-config-api: env config-docker
	DOCKER_BUILDKIT=1 docker buildx build --platform linux/amd64 -f config_api/Dockerfile -t ${EVENT_CONFIG_API_IMAGE}:${TAG_NAME} ./config_api
	@if [ "$(LATEST)" = "true" ]; then \
		docker tag ${EVENT_CONFIG_API_IMAGE}:${TAG_NAME} ${EVENT_CONFIG_API_IMAGE}:dev-latest; \
		docker push ${EVENT_CONFIG_API_IMAGE}:dev-latest; \
	fi
	docker push ${EVENT_CONFIG_API_IMAGE}:${TAG_NAME}
	rm ~/.docker/config.json

build-event-bq-writer: env config-docker
	DOCKER_BUILDKIT=1 docker buildx build --platform linux/amd64 -f bq_writer/Dockerfile -t ${EVENT_BQ_WRITER_IMAGE}:${TAG_NAME} .
	@if [ "$(LATEST)" = "true" ]; then \
		docker tag ${EVENT_BQ_WRITER_IMAGE}:${TAG_NAME} ${EVENT_BQ_WRITER_IMAGE}:dev-latest; \
		docker push ${EVENT_BQ_WRITER_IMAGE}:dev-latest; \
	fi
	docker push ${EVENT_BQ_WRITER_IMAGE}:${TAG_NAME}
	rm ~/.docker/config.json

date = $(shell date +%Y-%m-%d)
build-ip2location: env config-docker
	DOCKER_BUILDKIT=1 docker buildx build --platform linux/amd64 --pull -f ip2location/Dockerfile -t ${EVENT_IP2LOCATION_IMAGE}:${date} \
	    --build-arg PRODUCT_CODE=${IP2LOCATION_PRODUCT_CODE} --build-arg API_TOKEN=${IP2LOCATION_API_TOKEN} \
	    --no-cache ./ip2location
	docker push ${EVENT_IP2LOCATION_IMAGE}:${date}
	rm ~/.docker/config.json

build-event-user-unify-job: env config-docker
	DOCKER_BUILDKIT=1 docker buildx build --platform linux/amd64 -f user_unify/Dockerfile -t ${EVENT_UU_IMAGE}:${TAG_NAME} .
	@if [ "$(LATEST)" = "true" ]; then \
		docker tag ${EVENT_UU_IMAGE}:${TAG_NAME} ${EVENT_UU_IMAGE}:dev-latest; \
		docker push ${EVENT_UU_IMAGE}:dev-latest; \
	fi
	docker push ${EVENT_UU_IMAGE}:${TAG_NAME}
	rm ~/.docker/config.json

manage:
	docker exec -it deploy sh

config-cluster:
	docker exec -it deploy gcloud container clusters get-credentials "$(CLUSTER)"

deploy-up:
	docker-compose -f docker-compose.deploy.yml up -d

deploy-down:
	docker-compose -f docker-compose.deploy.yml down

terraform-init:
	docker exec -it deploy sh -c "cd /src && terraform init"

terraform-plan:
	docker exec -it deploy sh -c "cd /src && terraform plan -var-file=prod.tfvars"

terraform-apply:
	docker exec -it deploy sh -c "cd /src && terraform apply -var-file=prod.tfvars"

terraform-plan-target:
	@if [ -z "$(TARGET)" ]; then \
		echo "Please pass target argument by TARGET=resource_type.resource_name right after this command." && exit 1; \
	fi
	docker exec -it deploy sh -c "cd /src && terraform plan -var-file=prod.tfvars -target=$(TARGET)"

terraform-apply-target:
	@if [ -z "$(TARGET)" ]; then \
		echo "Please pass target argument by TARGET=resource_type.resource_name right after this command." && exit 1; \
	fi
	docker exec -it deploy sh -c "cd /src && terraform apply -var-file=prod.tfvars -target=$(TARGET)"

date = $(shell date +%Y-%m-%d)
version = $(shell date +%H%M%S)
deploy-fb-capi-compressor-dataflow-template:
	@if [ -z "$(WORKSPACE)" ]; then \
  		echo "Please pass terraform workspace argument by WORKSPACE=workspace right after this command." & exit 1; \
	fi

	docker exec -it dataflow bash -c "python fb_capi_compressor.py \
	--runner DataflowRunner \
	--project=tagtoo-tracking \
	--region=asia-east1 \
	--template_location=gs://tagtoo-dataflow-templates/templates/$(WORKSPACE)/$(date)/$(version)/Facebook_CAPI_Compressor \
	--staging_location=gs://tagtoo-dataflow-templates/staging \
	--temp_location=gs://tagtoo-dataflow-templates/temp \
	--input-subscription=projects/tagtoo-tracking/subscriptions/facebook-capi-s2s-to-compress-$(WORKSPACE) \
	--output-topic=projects/tagtoo-tracking/topics/facebook-capi-compressed-$(WORKSPACE) \
	--window-size=5"
	# Beam Python SDK connectors such as ReadFromPubSub, WriteToPubSub not yet support ValueProvider
	# Still need to provide productive argument values when creating templates
