locals {
  event_s2s_prefix                                  = "s2s"
  event_s2s_env                                     = "${local.event_s2s_prefix}-env"
  event_s2s_facebook_capi_subscriber_batch          = "${local.event_s2s_prefix}-facebook-capi-subscriber-batch"
  event_s2s_facebook_capi_task_failed_batch_handler = "${local.event_s2s_prefix}-facebook-capi-failed-batch-handler"
  event_s2s_facebook_capi_subscriber_singleton      = "${local.event_s2s_prefix}-facebook-capi-subscriber-singleton"
  event_s2s_lta_subscriber                          = "${local.event_s2s_prefix}-lta-subscriber"
  event_s2s_lta_facebook_capi_batch_subscriber      = "${local.event_s2s_prefix}-lta-facebook-capi-batch-subscriber"
  event_s2s_rabbitmq                                = "${local.event_s2s_prefix}-rabbitmq"
  event_s2s_redis                                   = "${local.event_s2s_prefix}-redis"
  event_s2s_update_capi_config_data                 = "${local.event_s2s_prefix}-update-capi-config-data"
}

resource "google_compute_disk" "event_s2s_rabbitmq" {
  name = "${local.event_s2s_rabbitmq}-persistent-disk-${var.environment}"
  zone = "asia-east1-a"
  labels = {
    environment = var.environment
  }
  size = 10
  lifecycle {
    ignore_changes = [
      labels,
    ]
  }
}

resource "kubernetes_stateful_set_v1" "event_s2s_rabbitmq" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = local.event_s2s_rabbitmq
    labels = {
      "app.kubernetes.io/name" = local.event_s2s_rabbitmq
      "application"            = "event-s2s"
    }
  }

  spec {
    service_name = local.event_s2s_rabbitmq
    selector {
      match_labels = {
        "app.kubernetes.io/name" = local.event_s2s_rabbitmq
      }
    }
    template {
      metadata {
        labels = {
          "app.kubernetes.io/name" = local.event_s2s_rabbitmq
          "application"            = "event-s2s"
        }
      }
      spec {
        container {
          name  = "event-s2s-rabbitmq"
          image = "rabbitmq:3.8-management-alpine"
          volume_mount {
            name       = "rabbitmq-volume"
            mount_path = "/var/lib/rabbitmq"
          }
          resources {
            requests = {
              cpu    = "50m"
              memory = "150Mi"
            }
          }
        }
        volume {
          name = "rabbitmq-volume"
          gce_persistent_disk {
            pd_name = google_compute_disk.event_s2s_rabbitmq.name
          }
        }
        node_selector = {
          "application" = "event"
          "name"        = "s2s-system"
        }
        toleration {
          effect   = "NoSchedule"
          key      = "name"
          operator = "Equal"
          value    = "s2s-system"
        }
      }
    }
    update_strategy {
      type = "OnDelete"
    }
  }
  wait_for_rollout = false
}

resource "kubernetes_service_v1" "event_s2s_rabbitmq" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = local.event_s2s_rabbitmq
  }
  spec {
    selector = {
      "app.kubernetes.io/name" = local.event_s2s_rabbitmq
    }
    type             = "NodePort"
    session_affinity = null
    port {
      port        = 5672
      target_port = 5672
    }
  }
}

resource "google_compute_disk" "event_s2s_redis" {
  name = "${local.event_s2s_redis}-persistent-disk-${var.environment}"
  zone = "asia-east1-a"
  labels = {
    environment = var.environment
  }
  size = 10
  lifecycle {
    ignore_changes = [
      labels,
    ]
  }
}

resource "kubernetes_stateful_set_v1" "event_s2s_redis" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = local.event_s2s_redis
    labels = {
      "app.kubernetes.io/name" = local.event_s2s_redis
      "application"            = "event-s2s"
    }
  }

  spec {
    service_name = local.event_s2s_redis
    selector {
      match_labels = {
        "app.kubernetes.io/name" = local.event_s2s_redis
      }
    }
    template {
      metadata {
        labels = {
          "app.kubernetes.io/name" = local.event_s2s_redis
          "application"            = "event-s2s"
        }
      }
      spec {
        container {
          name  = "event-s2s-redis"
          image = "redis:6-alpine"
          volume_mount {
            name       = "redis-volume"
            mount_path = "/data"
          }
          resources {
            requests = {
              cpu    = "50m"
              memory = "150Mi"
            }
          }
        }
        volume {
          name = "redis-volume"
          gce_persistent_disk {
            pd_name = google_compute_disk.event_s2s_redis.name
          }
        }
        node_selector = {
          "application" = "event"
          "name"        = "s2s-system"
        }
        toleration {
          effect   = "NoSchedule"
          key      = "name"
          operator = "Equal"
          value    = "s2s-system"
        }
      }
    }
    update_strategy {
      type = "OnDelete"
    }
  }
  wait_for_rollout = false
}

resource "kubernetes_service_v1" "event_s2s_redis" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = local.event_s2s_redis
  }
  spec {
    selector = {
      "app.kubernetes.io/name" = local.event_s2s_redis
    }
    type             = "NodePort"
    session_affinity = null
    port {
      port        = 6379
      target_port = 6379
    }
  }
}

resource "kubernetes_config_map_v1" "event_s2s_env" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = local.event_s2s_env
  }
  data = {
    MODE                                          = var.event_s2s.common.env.mode
    GCP_PROJECT_ID                                = var.gcp_project
    FACEBOOK_CAPI_TOPIC_CAPI_COMPRESSED           = google_pubsub_topic.facebook_capi_compressed.name
    FACEBOOK_CAPI_TOPIC_FAILED_BATCH_DECOMPRESSED = google_pubsub_topic.facebook_capi_s2s_failed_batch_decompressed.name
    LTA_FACEBOOK_TOPIC                            = google_pubsub_topic.lta_facebook_capi_batch.name
    FACEBOOK_CAPI_SUBSCRIPTION_BATCH_V1           = google_pubsub_subscription.facebook_capi_s2s_batch_subs["v1"].name
    FACEBOOK_CAPI_SUBSCRIPTION_SINGLETON_V1       = google_pubsub_subscription.facebook_capi_s2s_singleton_subs["v1"].name
    LTA_FACEBOOK_SUBSCRIPTION_V1                  = google_pubsub_subscription.lta_subs["v1"].name
    LTA_FACEBOOK_CAPI_SUBSCRIPTION_BATCH_V1       = google_pubsub_subscription.lta_facebook_capi_s2s_batch_subs["v1"].name
    RABBITMQ_HOST                                 = kubernetes_service_v1.event_s2s_rabbitmq.metadata[0].name
    REDIS_HOST                                    = kubernetes_service_v1.event_s2s_redis.metadata[0].name
    LTA_FACEBOOK_DATA_LOADER_IMAGE                = "asia.gcr.io/tagtoo-tracking/event-s2s:${var.event_s2s.lta_subscriber.image_tag}"
    S2S_PIXELS_CONFIG_API                         = "http://${kubernetes_service_v1.event_config_api_web.metadata[0].name}/api/event_s2s/facebook_capi_pixels"
    CONFIG_API_AUTH_TOKEN                         = var.event_s2s.common.env.config_api_auth_token
    CONFIG_FACEBOOK_CAPI_TTL                      = 21600
    FACEBOOK_API_VERSION                          = "v20.0"
  }
}

resource "kubernetes_deployment_v1" "lta_subscriber" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = local.event_s2s_lta_subscriber
    labels = {
      "app.kubernetes.io/name" = local.event_s2s_lta_subscriber
      "application"            = "event-s2s"
    }
  }
  spec {
    selector {
      match_labels = {
        "app.kubernetes.io/name" = local.event_s2s_lta_subscriber
      }
    }
    template {
      metadata {
        labels = {
          "app.kubernetes.io/name" = local.event_s2s_lta_subscriber
          "application"            = "event-s2s"
        }
      }
      spec {
        init_container {
          image   = "asia.gcr.io/tagtoo-tracking/event-s2s:${var.event_s2s.lta_subscriber.image_tag}"
          name    = local.event_s2s_update_capi_config_data
          command = ["./scripts/entrypoint.sh"]
          args    = ["python", "update_db.py", "1"]
          env_from {
            config_map_ref {
              name = kubernetes_config_map_v1.event_s2s_env.metadata[0].name
            }
          }
        }
        container {
          image   = "asia.gcr.io/tagtoo-tracking/event-s2s:${var.event_s2s.lta_subscriber.image_tag}"
          name    = "lta-facebook-capi"
          command = ["python", "subscribe.py", "lta", "--version=v1", "--max-messages=${var.event_s2s.lta_subscriber.max_messages}"]
          env_from {
            config_map_ref {
              name = kubernetes_config_map_v1.event_s2s_env.metadata[0].name
            }
          }
          resources {
            limits = {
              cpu    = var.event_s2s.lta_subscriber.limits.cpu
              memory = var.event_s2s.lta_subscriber.limits.memory
            }
            requests = {
              cpu    = var.event_s2s.lta_subscriber.requests.cpu
              memory = var.event_s2s.lta_subscriber.requests.memory
            }
          }
        }
        node_selector = {
          "application" = "event"
          "name"        = "s2s-lta-subscriber"
        }
        toleration {
          effect   = "NoSchedule"
          key      = "name"
          operator = "Equal"
          value    = "s2s-lta-subscriber"
        }
      }
    }
  }
  lifecycle {
    ignore_changes = [
      spec[0].replicas,
    ]
  }
  wait_for_rollout = var.wait_for_rollout
}


resource "kubernetes_horizontal_pod_autoscaler_v2" "lta_subscriber" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = local.event_s2s_lta_subscriber
  }
  spec {
    min_replicas = var.event_s2s.lta_subscriber.min_replicas
    max_replicas = var.event_s2s.lta_subscriber.max_replicas

    metric {
      type = "Resource"
      resource {
        name = "cpu"
        target {
          type                = "Utilization"
          average_utilization = var.event_s2s.lta_subscriber.target_cpu_utilization_percentage
        }
      }
    }

    scale_target_ref {
      api_version = "apps/v1"
      kind        = "Deployment"
      name        = kubernetes_deployment_v1.lta_subscriber.metadata[0].name
    }

    behavior {
      scale_up {
        stabilization_window_seconds = 60  # 減少擴展冷卻期
        select_policy                = "Max"
        policy {
          type           = "Percent"
          value          = 100
          period_seconds = 60
        }
        policy {
          type           = "Pods"
          value          = 5
          period_seconds = 60
        }
      }
      scale_down {
        stabilization_window_seconds = 300
        select_policy                = "Max"
        policy {
          type           = "Percent"
          value          = 10
          period_seconds = 60
        }
      }
    }
  }
  lifecycle {
    ignore_changes = [
      spec[0].behavior,
    ]
  }
}

resource "kubernetes_deployment_v1" "lta_facebook_capi_batch_subscriber" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = local.event_s2s_lta_facebook_capi_batch_subscriber
    labels = {
      "app.kubernetes.io/name" = local.event_s2s_lta_facebook_capi_batch_subscriber
      "application"            = "event-s2s"
    }
  }
  spec {
    selector {
      match_labels = {
        "app.kubernetes.io/name" = local.event_s2s_lta_facebook_capi_batch_subscriber
      }
    }
    template {
      metadata {
        labels = {
          "app.kubernetes.io/name" = local.event_s2s_lta_facebook_capi_batch_subscriber
          "application"            = "event-s2s"
        }
      }
      spec {
        init_container {
          image   = "asia.gcr.io/tagtoo-tracking/event-s2s:${var.event_s2s.facebook_capi_subscriber.image_tag}"
          name    = local.event_s2s_update_capi_config_data
          command = ["./scripts/entrypoint.sh"]
          args    = ["python", "update_db.py", "1"]
          env_from {
            config_map_ref {
              name = kubernetes_config_map_v1.event_s2s_env.metadata[0].name
            }
          }
        }
        container {
          image   = "asia.gcr.io/tagtoo-tracking/event-s2s:${var.event_s2s.facebook_capi_subscriber.image_tag}"
          name    = "subscriber-batch-v1"
          command = ["python", "subscribe.py", "lta_facebook_capi_batch", "--version=v1", "--max-messages=${var.event_s2s.facebook_capi_subscriber.lta_batch.max_messages}"]
          env_from {
            config_map_ref {
              name = kubernetes_config_map_v1.event_s2s_env.metadata[0].name
            }
          }
          resources {
            limits = {
              cpu    = var.event_s2s.facebook_capi_subscriber.lta_batch.limits.cpu
              memory = var.event_s2s.facebook_capi_subscriber.lta_batch.limits.memory
            }
            requests = {
              cpu    = var.event_s2s.facebook_capi_subscriber.lta_batch.requests.cpu
              memory = var.event_s2s.facebook_capi_subscriber.lta_batch.requests.memory
            }
          }
        }
        node_selector = {
          "application" = "event"
          "name"        = "s2s-lta-subscriber"
        }
        toleration {
          effect   = "NoSchedule"
          key      = "name"
          operator = "Equal"
          value    = "s2s-lta-subscriber"
        }
      }
    }
  }
  lifecycle {
    ignore_changes = [
      spec[0].replicas,
    ]
  }
  wait_for_rollout = var.wait_for_rollout
}

resource "kubernetes_horizontal_pod_autoscaler_v2" "lta_facebook_capi_batch_subscriber" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = local.event_s2s_lta_facebook_capi_batch_subscriber
  }
  spec {
    min_replicas = var.event_s2s.facebook_capi_subscriber.lta_batch.min_replicas
    max_replicas = var.event_s2s.facebook_capi_subscriber.lta_batch.max_replicas
    metric {
      type = "Resource"

      resource {
        name = "memory"
        target {
          average_utilization = var.event_s2s.facebook_capi_subscriber.lta_batch.target_cpu_utilization_percentage
          type                = "Utilization"
        }
      }
    }

    scale_target_ref {
      api_version = "apps/v1"
      kind        = "Deployment"
      name        = kubernetes_deployment_v1.lta_facebook_capi_batch_subscriber.metadata[0].name
    }

    behavior {
      scale_up {
        stabilization_window_seconds = 60  # 減少擴展冷卻期
        select_policy                = "Max"
        policy {
          type           = "Percent"
          value          = 100
          period_seconds = 60
        }
        policy {
          type           = "Pods"
          value          = 5
          period_seconds = 60
        }
      }
      scale_down {
        stabilization_window_seconds = 300
        select_policy                = "Max"
        policy {
          type           = "Percent"
          value          = 10
          period_seconds = 60
        }
      }
    }
  }
  lifecycle {
    ignore_changes = [
      spec[0].behavior,
    ]
  }
}

resource "kubernetes_deployment_v1" "facebook_capi_subscriber_batch" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = local.event_s2s_facebook_capi_subscriber_batch
    labels = {
      "app.kubernetes.io/name" = local.event_s2s_facebook_capi_subscriber_batch
      "application"            = "event-s2s"
    }
  }
  spec {
    selector {
      match_labels = {
        "app.kubernetes.io/name" = local.event_s2s_facebook_capi_subscriber_batch
      }
    }
    template {
      metadata {
        labels = {
          "app.kubernetes.io/name" = local.event_s2s_facebook_capi_subscriber_batch
          "application"            = "event-s2s"
        }
      }
      spec {
        init_container {
          image   = "asia.gcr.io/tagtoo-tracking/event-s2s:${var.event_s2s.facebook_capi_subscriber.image_tag}"
          name    = local.event_s2s_update_capi_config_data
          command = ["./scripts/entrypoint.sh"]
          args    = ["python", "update_db.py", "1"]
          env_from {
            config_map_ref {
              name = kubernetes_config_map_v1.event_s2s_env.metadata[0].name
            }
          }
        }
        container {
          image   = "asia.gcr.io/tagtoo-tracking/event-s2s:${var.event_s2s.facebook_capi_subscriber.image_tag}"
          name    = "subscriber-batch-v1"
          command = ["python", "subscribe.py", "facebook_capi_batch", "--version=v1", "--max-messages=${var.event_s2s.facebook_capi_subscriber.batch.max_messages}"]
          env_from {
            config_map_ref {
              name = kubernetes_config_map_v1.event_s2s_env.metadata[0].name
            }
          }
          resources {
            limits = {
              cpu    = var.event_s2s.facebook_capi_subscriber.batch.limits.cpu
              memory = var.event_s2s.facebook_capi_subscriber.batch.limits.memory
            }
            requests = {
              cpu    = var.event_s2s.facebook_capi_subscriber.batch.requests.cpu
              memory = var.event_s2s.facebook_capi_subscriber.batch.requests.memory
            }
          }
        }
        node_selector = {
          "application" = "event"
          "name"        = "s2s-subscriber"
        }
        toleration {
          effect   = "NoSchedule"
          key      = "name"
          operator = "Equal"
          value    = "s2s-subscriber"
        }
      }
    }
  }
  lifecycle {
    ignore_changes = [
      spec[0].replicas,
    ]
  }
  wait_for_rollout = var.wait_for_rollout
}

resource "kubernetes_horizontal_pod_autoscaler_v2" "facebook_capi_subscriber_batch" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = local.event_s2s_facebook_capi_subscriber_batch
  }
  spec {
    min_replicas = var.event_s2s.facebook_capi_subscriber.batch.min_replicas
    max_replicas = var.event_s2s.facebook_capi_subscriber.batch.max_replicas

    metric {
      type = "Resource"
      resource {
        name = "cpu"
        target {
          type                = "Utilization"
          average_utilization = var.event_s2s.facebook_capi_subscriber.batch.target_cpu_utilization_percentage
        }
      }
    }

    scale_target_ref {
      api_version = "apps/v1"
      kind        = "Deployment"
      name        = kubernetes_deployment_v1.facebook_capi_subscriber_batch.metadata[0].name
    }

    behavior {
      scale_up {
        stabilization_window_seconds = 60  # 減少擴展冷卻期
        select_policy                = "Max"
        policy {
          type           = "Percent"
          value          = 100
          period_seconds = 60
        }
        policy {
          type           = "Pods"
          value          = 5
          period_seconds = 60
        }
      }
      scale_down {
        stabilization_window_seconds = 300
        select_policy                = "Max"
        policy {
          type           = "Percent"
          value          = 10
          period_seconds = 60
        }
      }
    }
  }
  lifecycle {
    ignore_changes = [
      spec[0].behavior,
    ]
  }
}


resource "kubernetes_deployment_v1" "facebook_capi_task_failed_batch_handler" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = local.event_s2s_facebook_capi_task_failed_batch_handler
    labels = {
      "app.kubernetes.io/name" = local.event_s2s_facebook_capi_task_failed_batch_handler
      "application"            = "event-s2s"
    }
  }
  spec {
    selector {
      match_labels = {
        "app.kubernetes.io/name" = local.event_s2s_facebook_capi_task_failed_batch_handler
      }
    }
    template {
      metadata {
        labels = {
          "app.kubernetes.io/name" = local.event_s2s_facebook_capi_task_failed_batch_handler
          "application"            = "event-s2s"
        }
      }
      spec {
        container {
          image   = "asia.gcr.io/tagtoo-tracking/event-s2s:${var.event_s2s.facebook_capi_subscriber.image_tag}"
          name    = "failed-batch-handler"
          command = ["./scripts/entrypoint.sh"]
          args    = ["python", "worker.py", "failed_batch_handler"]
          env_from {
            config_map_ref {
              name = kubernetes_config_map_v1.event_s2s_env.metadata[0].name
            }
          }
          resources {
            requests = {
              cpu    = "100m"
              memory = "150Mi"
            }
          }
        }
        node_selector = {
          "application" = "event"
          "name"        = "s2s-subscriber"
        }
        toleration {
          effect   = "NoSchedule"
          key      = "name"
          operator = "Equal"
          value    = "s2s-subscriber"
        }
      }
    }
  }
  lifecycle {
    ignore_changes = [
      spec[0].replicas,
    ]
  }
  wait_for_rollout = var.wait_for_rollout
}

resource "kubernetes_deployment_v1" "facebook_capi_subscriber_singleton" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = local.event_s2s_facebook_capi_subscriber_singleton
    labels = {
      "app.kubernetes.io/name" = local.event_s2s_facebook_capi_subscriber_singleton
      "application"            = "event-s2s"
    }
  }
  spec {
    selector {
      match_labels = {
        "app.kubernetes.io/name" = local.event_s2s_facebook_capi_subscriber_singleton
      }
    }
    template {
      metadata {
        labels = {
          "app.kubernetes.io/name" = local.event_s2s_facebook_capi_subscriber_singleton
          "application"            = "event-s2s"
        }
      }
      spec {
        init_container {
          image   = "asia.gcr.io/tagtoo-tracking/event-s2s:${var.event_s2s.facebook_capi_subscriber.image_tag}"
          name    = local.event_s2s_update_capi_config_data
          command = ["./scripts/entrypoint.sh"]
          args    = ["python", "update_db.py", "1"]
          env_from {
            config_map_ref {
              name = kubernetes_config_map_v1.event_s2s_env.metadata[0].name
            }
          }
        }
        container {
          image   = "asia.gcr.io/tagtoo-tracking/event-s2s:${var.event_s2s.facebook_capi_subscriber.image_tag}"
          name    = "subscriber-singleton-v1"
          command = ["python", "subscribe.py", "facebook_capi_singleton", "--version=v1", "--max-messages=${var.event_s2s.facebook_capi_subscriber.singleton.max_messages}"]
          env_from {
            config_map_ref {
              name = kubernetes_config_map_v1.event_s2s_env.metadata[0].name
            }
          }
          resources {
            limits = {
              cpu    = var.event_s2s.facebook_capi_subscriber.singleton.limits.cpu
              memory = var.event_s2s.facebook_capi_subscriber.singleton.limits.memory
            }
            requests = {
              cpu    = var.event_s2s.facebook_capi_subscriber.singleton.requests.cpu
              memory = var.event_s2s.facebook_capi_subscriber.singleton.requests.memory
            }
          }
        }
        node_selector = {
          "application" = "event"
          "name"        = "s2s-subscriber"
        }
        toleration {
          effect   = "NoSchedule"
          key      = "name"
          operator = "Equal"
          value    = "s2s-subscriber"
        }
      }
    }
  }
  lifecycle {
    ignore_changes = [
      spec[0].replicas,
    ]
  }
  wait_for_rollout = var.wait_for_rollout
}

resource "kubernetes_horizontal_pod_autoscaler_v2" "facebook_capi_subscriber_singleton" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = local.event_s2s_facebook_capi_subscriber_singleton
  }
  spec {
    min_replicas = var.event_s2s.facebook_capi_subscriber.singleton.min_replicas
    max_replicas = var.event_s2s.facebook_capi_subscriber.singleton.max_replicas

    metric {
      type = "Resource"
      resource {
        name = "cpu"
        target {
          type                = "Utilization"
          average_utilization = var.event_s2s.facebook_capi_subscriber.singleton.target_cpu_utilization_percentage
        }
      }
    }

    scale_target_ref {
      api_version = "apps/v1"
      kind        = "Deployment"
      name        = kubernetes_deployment_v1.facebook_capi_subscriber_singleton.metadata[0].name
    }

    behavior {
      scale_up {
        stabilization_window_seconds = 60  # 減少擴展冷卻期
        select_policy                = "Max"
        policy {
          type           = "Percent"
          value          = 100
          period_seconds = 60
        }
        policy {
          type           = "Pods"
          value          = 5
          period_seconds = 60
        }
      }
      scale_down {
        stabilization_window_seconds = 300
        select_policy                = "Max"
        policy {
          type           = "Percent"
          value          = 10
          period_seconds = 60
        }
      }
    }
  }
  lifecycle {
    ignore_changes = [
      spec[0].behavior,
    ]
  }
}

resource "kubernetes_cron_job_v1" "event_s2s_update_capi_config_data" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = local.event_s2s_update_capi_config_data
  }
  spec {
    schedule = "*/30 * * * *"
    job_template {
      metadata {}
      spec {
        backoff_limit = 3
        template {
          metadata {}
          spec {
            container {
              name    = local.event_s2s_update_capi_config_data
              image   = "asia.gcr.io/tagtoo-tracking/event-s2s:${var.event_s2s.facebook_capi_subscriber.image_tag}"
              command = ["python", "update_db.py", "1"]
              env_from {
                config_map_ref {
                  name = kubernetes_config_map_v1.event_s2s_env.metadata[0].name
                }
              }
            }
          }
        }
      }
    }
  }
}

resource "kubernetes_role_v1" "event_s2s" {
  metadata {
    name      = "event-s2s"
    namespace = var.kubernetes_namespace
    labels = {
      environment = var.environment
      application = "event-s2s"
    }
  }

  rule {
    api_groups = [""]
    resources  = ["pods"]
    verbs      = ["get", "list", "watch"]
  }
  rule {
    api_groups = ["batch", "extensions"]
    resources  = ["jobs"]
    verbs      = ["get", "list", "watch", "create", "update", "patch", "delete"]
  }
}

resource "kubernetes_role_binding_v1" "event_s2s_job_creator_bind" {
  metadata {
    name      = "event-s2s"
    namespace = var.kubernetes_namespace
  }
  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "Role"
    name      = kubernetes_role_v1.event_s2s.metadata[0].name
  }
  subject {
    kind      = "ServiceAccount"
    name      = "default"
    namespace = var.kubernetes_namespace
  }
}
