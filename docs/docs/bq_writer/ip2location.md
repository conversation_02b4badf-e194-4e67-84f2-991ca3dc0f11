<script src="https://unpkg.com/mermaid@8.13.3/dist/mermaid.min.js"></script>

To process an event, a necessary step is to resolve the IP addresses into GEO locations. We use a productive dataset
provided by [ip2location.com](https://www.ip2location.com/).

## Service Structure

The dataset should be scattered into scalable services in order to prevent SPOF (single point of failure).

Therefore, the IP2Location MySQL database is deployed in each pod as a **side-car**, serving the ip-to-location queries
from multiple subscriber clients in the same pod.

``` mermaid
stateDiagram-v2
    Subscription --> Deployment
    
    state Deployment {
        [*] --> PodA.SubscriberA : pull
        [*] --> PodA.SubscriberB : pull
        [*] --> PodA.SubscriberC : pull
        PodA.SubscriberA --> PodA.IP2Location : query
        PodA.SubscriberB --> PodA.IP2Location : query
        PodA.SubscriberC --> PodA.IP2Location : query
        --
        [*] --> PodB.SubscriberA : pull
        [*] --> PodB.SubscriberB : pull
        [*] --> PodB.SubscriberC : pull
        PodB.SubscriberA --> PodB.IP2Location : query
        PodB.SubscriberB --> PodB.IP2Location : query
        PodB.SubscriberC --> PodB.IP2Location : query
    }
```

## Prerequisite

**1. Database initialization time should be fixed for each replicas.**

There are over one million rows in the IP2Location dataset. Even though we pre-download it and stores the data into the
container image, the data still need to be loaded at container start.

How fast for a IP2Location database to start-up is depends on CPU cores. In order to make sure the start-up process is less 
than 5 minutes (The default delay for a pod to start scaling, see `horizontal-pod-autoscaler-cpu-initialization-period` argument),
the pod should at least has 2 cores of CPU.

In order to have a better CPU utilization in one Pod, increase the number of subscribers(the client of database) to utilize
the Pod's CPU after database initialization completed, that's why we have multiple subscriber to one database instead of one-to-one.

**2. Resources in a Pod is been shared between the MySQL instance and the Subscribers.**

The resource of Pod's CPU is shared by different services before and after the initialization process. That's why we did not 
directly request 2 cores of CPU for the MySQL container but make sure there are 2 cores of CPU for it to start-up, by setting a
pod affinity for node (only one pod for one node).

```mermaid
stateDiagram-v2
    State1: Init Start
    note right of State1 : MySQL allocate 80% of CPUs while loading the dataset
    State2: Init Complete
    State1 --> State2
    note left of State2
        MySQL ready, CPU allocation revised down to 1%
        Subscribers start pulling and allocating CPUs
    end note
```

## MySQL Performance Tuning

The IP2Location database is static and read-only, there is no transaction operations, thus, use MyISAM engine instead
of InnoDB engine for better read performance.

There is a built-in tool [MySQLTuner](https://github.com/major/MySQLTuner-perl) in the IP2Location container, to analyze
the performance of the database, attach the container and run command:

```
perl mysqltuner.pl --host 127.0.0.1 --user root
```

You should see the report below. The accuracy is depends on how long this instance runs.

```
-------- Performance Metrics -----------------------------------------------------------------------
[--] Up for: 4h 36m 37s (2M q [150.306 qps], 831K conn, TX: 697M, RX: 409M)
[--] Reads / Writes: 100% / 0%
[--] Binary logging is disabled
[--] Physical Memory     : 1.9G
[--] Max MySQL memory    : 570.9M
[--] Other process memory: 0B
[--] Total buffers: 282.0M global + 1.9M per thread (100 max threads)
[--] P_S Max memory usage: 95M
[--] Galera GCache Max memory usage: 0B
[OK] Maximum reached memory usage: 413.9M (20.82% of installed RAM)
[OK] Maximum possible memory usage: 570.9M (28.71% of installed RAM)
[OK] Overall possible memory usage with other process is compatible with memory available
[OK] Slow queries: 0% (0/2M)
[OK] Highest usage of available connections: 19% (19/100)
[OK] Aborted connections: 0.00%  (0/831547)
[OK] Query cache is disabled by default due to mutex contention on multiprocessor machines.
[OK] No Sort requiring temporary tables
[OK] No joins without indexes
[OK] Temporary tables created on disk: 0% (0 on disk / 4 total)
[OK] Thread cache hit rate: 99% (19 created / 831K connections)
[OK] Table cache hit rate: 95% (831K hits / 873K requests)
[OK] table_definition_cache(400) is upper than number of tables(157)
[OK] Open file limit used: 1% (34/1K)
[OK] Table locks acquired immediately: 100% (831K immediate / 831K locks)
```

Change the configurations by update the mounted Kubernetes ConfigMap which points to a local `ip2location.cnf` file.

## Dataset Update

IP2Location database update their database **monthly**, after the update, we should rebuild the database image, then 
perform a rolling update to the Deployment.

```
make build-ip2location
```