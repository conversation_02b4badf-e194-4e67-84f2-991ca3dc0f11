locals {
  bq_writer_prefix        = "bq-writer"
  bq_writer_env           = "${local.bq_writer_prefix}-env"
  tagtoo_event_subscriber = "${local.bq_writer_prefix}-tagtoo-event-subscriber"
  ip2location_conf        = "${local.bq_writer_prefix}-ip2location-conf"
}

resource "kubernetes_config_map_v1" "bq_writer_env" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = local.bq_writer_env
  }
  data = {
    TEST_MODE                      = var.bq_writer.env.test_mode
    GCP_PROJECT_ID                 = var.gcp_project
    TAGTOO_EVENT_SUBSCRIPTION_V1   = google_pubsub_subscription.tagtoo_event_to_bigquery_subs["v1"].name
    BIGQUERY_DATASET_ID            = google_bigquery_dataset.event.dataset_id
    BIGQUERY_TAGTOO_EVENT_TABLE_ID = google_bigquery_table.tagtoo_event.table_id
    IP2LOCATION_DB_HOST            = "127.0.0.1"
    # 雙寫入架構環境變數 (v2.1 tracking_id 核心策略)
    INTEGRATED_WRITE_ENABLED       = var.bq_writer.env.integrated_write_enabled
    INTEGRATED_WRITE_SAMPLE_RATE   = var.bq_writer.env.integrated_write_sample_rate
  }
}

resource "kubernetes_config_map_v1" "ip2location_conf" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = local.ip2location_conf
  }
  data = {
    "custom.cnf" : file("mysql/ip2location.cnf")
  }
}

resource "kubernetes_deployment_v1" "tagtoo_event_subscriber" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = local.tagtoo_event_subscriber
    labels = {
      "app.kubernetes.io/name" = local.tagtoo_event_subscriber
    }
  }
  spec {
    strategy {
      type = "RollingUpdate"
      rolling_update {
        max_surge       = 1
        max_unavailable = 0
      }
    }
    selector {
      match_labels = {
        "app.kubernetes.io/name" = local.tagtoo_event_subscriber
      }
    }
    template {
      metadata {
        labels = {
          "app.kubernetes.io/name" = local.tagtoo_event_subscriber
        }
      }
      spec {
        affinity {
          pod_affinity {
            preferred_during_scheduling_ignored_during_execution {
              weight = 100
              pod_affinity_term {
                label_selector {
                  match_expressions {
                    key      = "app.kubernetes.io/name"
                    operator = "In"
                    values   = [local.tagtoo_event_subscriber]
                  }
                }
                topology_key = "kubernetes.io/hostname"
              }
            }
          }
        }
        dynamic "container" {
          for_each = toset([
            for i in range(var.bq_writer.tagtoo_event_subscriber.containers_per_pod) : tostring(i)
          ])
          content {
            image   = "asia.gcr.io/tagtoo-tracking/event-bq-writer:${var.bq_writer.tagtoo_event_subscriber.image_tag}"
            name    = "tagtoo-event-subscriber-v1-${container.key}"
            command = ["./scripts/entrypoint.sh"]
            args    = ["python", "subscribe.py", "tagtoo_event", "--version=v1", "--max-messages=10"]
            env_from {
              config_map_ref {
                name = kubernetes_config_map_v1.bq_writer_env.metadata[0].name
              }
            }
            liveness_probe {
              exec {
                command = ["sh", "-c", "ps -eo cmd,%cpu | grep '[p]ython' | awk '{ if ($2 >= 5) print $0 }' && exit 0 || exit 1"]
              }
              initial_delay_seconds = 360
              period_seconds        = 5
              failure_threshold     = 30
            }
            resources {
              requests = {
                cpu               = var.bq_writer.tagtoo_event_subscriber.requests.cpu
                memory            = var.bq_writer.tagtoo_event_subscriber.requests.memory
                ephemeral-storage = var.bq_writer.tagtoo_event_subscriber.requests.ephemeral_storage
              }
            }
          }
        }

        container {
          image = "asia.gcr.io/tagtoo-tracking/ip2location-mysql:${var.bq_writer.ip2location.image_tag}"
          name  = "ip2location"
          resources {
            requests = {
              cpu               = var.bq_writer.ip2location.requests.cpu
              memory            = var.bq_writer.ip2location.requests.memory
              ephemeral-storage = var.bq_writer.ip2location.requests.ephemeral_storage
            }
          }
          volume_mount {
            name       = local.ip2location_conf
            mount_path = "/etc/mysql/conf.d"
          }
        }
        volume {
          name = local.ip2location_conf
          config_map {
            name = kubernetes_config_map_v1.ip2location_conf.metadata[0].name
          }
        }
        node_selector = {
          "application" = "event"
          "name"        = "bq-writer-subscriber"
        }
        toleration {
          effect   = "NoSchedule"
          key      = "name"
          operator = "Equal"
          value    = "bq-writer-subscriber"
        }
      }
    }
  }
  lifecycle {
    ignore_changes = [
      spec[0].replicas,
    ]
  }
  wait_for_rollout = var.wait_for_rollout
}

resource "kubernetes_horizontal_pod_autoscaler_v2" "tagtoo_event_subscriber" {
  metadata {
    namespace = var.kubernetes_namespace
    name      = local.tagtoo_event_subscriber
  }
  spec {
    min_replicas = var.bq_writer.tagtoo_event_subscriber.min_replicas
    max_replicas = var.bq_writer.tagtoo_event_subscriber.max_replicas

    metric {
      type = "Resource"
      resource {
        name = "cpu"
        target {
          type                = "Utilization"
          average_utilization = var.bq_writer.tagtoo_event_subscriber.target_cpu_utilization_percentage
        }
      }
    }

    metric {
      type = "External"
      external {
        metric {
          name = "pubsub.googleapis.com|subscription|num_undelivered_messages"
          selector {
            match_labels = {
              "resource.labels.subscription_id" = replace(google_pubsub_subscription.tagtoo_event_to_bigquery_subs["v1"].name, "projects/${var.gcp_project}/subscriptions/", "")
            }
          }
        }
        target {
          type          = "AverageValue"
          average_value = "1k"  # 每個 Pod 處理 1000 條消息
        }
      }
    }

    scale_target_ref {
      api_version = "apps/v1"
      kind        = "Deployment"
      name        = kubernetes_deployment_v1.tagtoo_event_subscriber.metadata[0].name
    }

    behavior {
      scale_up {
        stabilization_window_seconds = 60  # 減少擴展冷卻期
        select_policy                = "Max"
        policy {
          type           = "Percent"
          value          = 100
          period_seconds = 60
        }
        policy {
          type           = "Pods"
          value          = 5
          period_seconds = 60
        }
      }
      scale_down {
        stabilization_window_seconds = 300
        select_policy                = "Max"
        policy {
          type           = "Percent"
          value          = 10
          period_seconds = 60
        }
      }
    }
  }
  lifecycle {
    ignore_changes = [
      spec[0].behavior,
    ]
  }
}
