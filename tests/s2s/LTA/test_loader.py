"""
LTA DataLoader 智能 Hash 處理測試

測試電話號碼的智能 hash 檢測和處理邏輯
"""

import unittest
from unittest.mock import Mock, patch
import sys
import os

# 添加專案路徑到 sys.path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../..', 's2s'))

from s2s.s2s.LTA.loader import DataLoader


class TestDataLoaderPhoneHashing(unittest.TestCase):
    """測試 DataLoader 的電話號碼 hash 處理功能"""

    def setUp(self):
        """設置測試環境"""
        self.loader = DataLoader()
        # 模擬 logger 避免真實日誌輸出
        self.loader._logger = Mock()

    def test_is_already_hashed_valid_hash(self):
        """測試有效的 SHA256 hash 檢測"""
        valid_hashes = [
            'cf676475cec2ee7b7591f39bdefe7ef3dca63b14c1cdba3b19ce4219cbda2b09',
            'CF676475CEC2EE7B7591F39BDEFE7EF3DCA63B14C1CDBA3B19CE4219CBDA2B09',
            '0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef',
        ]
        
        for hash_value in valid_hashes:
            with self.subTest(hash_value=hash_value):
                self.assertTrue(
                    self.loader._is_already_hashed(hash_value),
                    f"應該識別 {hash_value} 為有效的 hash"
                )

    def test_is_already_hashed_invalid_hash(self):
        """測試無效的 hash 檢測"""
        invalid_values = [
            '886912345678',  # 原始電話號碼
            '+886-912-345-678',  # 格式化電話號碼
            'invalid_hash_string',  # 包含無效字符
            'cf676475cec2ee7b7591f39bdefe7ef3dca63b14c1cdba3b19ce4219cbda2b0',  # 63字符
            'cf676475cec2ee7b7591f39bdefe7ef3dca63b14c1cdba3b19ce4219cbda2b09g',  # 含 'g'
            '',  # 空字串
            'abc',  # 太短
            123,  # 非字串類型
            None,  # None 值
        ]
        
        for invalid_value in invalid_values:
            with self.subTest(invalid_value=invalid_value):
                self.assertFalse(
                    self.loader._is_already_hashed(invalid_value),
                    f"應該識別 {invalid_value} 為無效的 hash"
                )

    def test_safe_encode_phone_already_hashed(self):
        """測試已經 hash 的電話號碼處理"""
        hashed_phone = 'cf676475cec2ee7b7591f39bdefe7ef3dca63b14c1cdba3b19ce4219cbda2b09'
        uppercase_hashed = 'CF676475CEC2EE7B7591F39BDEFE7EF3DCA63B14C1CDBA3B19CE4219CBDA2B09'
        
        # 測試小寫 hash
        result = self.loader._safe_encode_phone(hashed_phone)
        self.assertEqual(result, hashed_phone.lower())
        self.loader._logger.debug.assert_called()
        
        # 測試大寫 hash（應轉為小寫）
        result = self.loader._safe_encode_phone(uppercase_hashed)
        self.assertEqual(result, uppercase_hashed.lower())

    def test_safe_encode_phone_raw_numbers(self):
        """測試原始電話號碼的處理"""
        test_cases = [
            ('886912345678', '886912345678'),
            ('+886-912-345-678', '886912345678'),
            ('(886) 912 345 678', '886912345678'),
            ('+886 912-345-678', '886912345678'),
        ]
        
        for input_phone, expected_normalized in test_cases:
            with self.subTest(input_phone=input_phone):
                with patch.object(self.loader, '_encode_sha_256') as mock_encode:
                    mock_encode.return_value = 'mocked_hash_value'
                    
                    result = self.loader._safe_encode_phone(input_phone)
                    
                    # 驗證呼叫了 _encode_sha_256 且參數正確
                    mock_encode.assert_called_once_with(expected_normalized)
                    self.assertEqual(result, 'mocked_hash_value')

    def test_safe_encode_phone_edge_cases(self):
        """測試邊界情況"""
        edge_cases = [
            ('', ''),  # 空字串
            ('abc', ''),  # 無數字
            ('+-()_', ''),  # 只有符號
            (None, ''),  # None 值
            (123, ''),  # 數字類型
        ]
        
        for input_value, expected in edge_cases:
            with self.subTest(input_value=input_value):
                result = self.loader._safe_encode_phone(input_value)
                self.assertEqual(result, expected)

    def test_safe_encode_phone_warning_logging(self):
        """測試警告日誌記錄"""
        # 測試正規化後為空字串的情況
        result = self.loader._safe_encode_phone('abc')
        self.assertEqual(result, '')
        self.loader._logger.warning.assert_called_once()

    def test_compose_pixel_datum_from_avro_phone_processing(self):
        """測試 AVRO 資料中電話號碼的處理邏輯"""
        # 準備測試資料
        test_user_data = [{
            'phones': [
                '886912345678',  # 原始號碼
                'cf676475cec2ee7b7591f39bdefe7ef3dca63b14c1cdba3b19ce4219cbda2b09',  # 已 hash
                '+886-987-654-321',  # 格式化號碼
            ],
            'emails': ['<EMAIL>'],
            'segment_id': ['test_segment'],
            'fb_info': [
                {'fbp_fbc_ip': ['fb.1.123.456', 'fbc.1.789', '***********']}
            ]
        }]
        
        # 模擬 _safe_encode_phone 的行為
        with patch.object(self.loader, '_safe_encode_phone') as mock_safe_encode:
            mock_safe_encode.side_effect = [
                'hashed_886912345678',
                'cf676475cec2ee7b7591f39bdefe7ef3dca63b14c1cdba3b19ce4219cbda2b09',
                'hashed_886987654321'
            ]
            
            # 執行處理
            result_generator = self.loader._compose_pixel_datum_from_avro(test_user_data)
            results = list(result_generator)
            
            # 驗證呼叫次數
            self.assertEqual(mock_safe_encode.call_count, 3)
            
            # 驗證處理結果
            self.assertEqual(len(results), 1)  # 一個批次
            batch = results[0]
            self.assertEqual(len(batch), 1)  # 一個 fb_info 項目
            
            processed_phones = batch[0]['user_data']['ph']
            expected_phones = [
                'hashed_886912345678',
                'cf676475cec2ee7b7591f39bdefe7ef3dca63b14c1cdba3b19ce4219cbda2b09',
                'hashed_886987654321'
            ]
            self.assertEqual(processed_phones, expected_phones)

    def test_compose_pixel_datum_empty_phones(self):
        """測試空電話號碼清單的處理"""
        test_user_data = [{
            'phones': [],  # 空清單
            'emails': ['<EMAIL>'],
            'segment_id': ['test_segment'],
            'fb_info': [
                {'fbp_fbc_ip': ['fb.1.123.456', 'fbc.1.789', '***********']}
            ]
        }]
        
        result_generator = self.loader._compose_pixel_datum_from_avro(test_user_data)
        results = list(result_generator)
        
        # 驗證結果
        self.assertEqual(len(results), 1)
        batch = results[0]
        self.assertEqual(len(batch), 1)
        
        processed_phones = batch[0]['user_data']['ph']
        self.assertEqual(processed_phones, [])  # 應該是空清單

    def test_integration_real_hash_function(self):
        """整合測試：使用真實的 hash 函數"""
        # 移除 logger mock，使用真實的處理流程
        self.loader._logger = Mock()
        
        original_phone = '886912345678'
        expected_hash = 'cf676475cec2ee7b7591f39bdefe7ef3dca63b14c1cdba3b19ce4219cbda2b09'
        
        # 測試原始號碼 hash
        result1 = self.loader._safe_encode_phone(original_phone)
        self.assertEqual(result1, expected_hash)
        
        # 測試已 hash 號碼處理
        result2 = self.loader._safe_encode_phone(expected_hash)
        self.assertEqual(result2, expected_hash)
        
        # 驗證不會產生雙重 hash
        self.assertEqual(result1, result2)


if __name__ == '__main__':
    # 執行測試
    unittest.main(verbosity=2)