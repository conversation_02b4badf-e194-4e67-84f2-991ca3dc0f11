# 測試 GitHub Actions 工作流程

本文檔介紹如何在合併 PR 之前測試 GitHub Actions 工作流程。

## 使用 `act` 在本地測試

[act](https://github.com/nektos/act) 是一個可以在本地運行 GitHub Actions 的工具。它使用 Docker 來模擬 GitHub Actions 的執行環境。

### 安裝 act

#### macOS

```bash
brew install act
```

#### Linux

```bash
curl -s https://raw.githubusercontent.com/nektos/act/master/install.sh | sudo bash
```

#### Windows

```bash
choco install act-cli
```

### 使用 act 測試工作流程

在專案根目錄下執行以下命令，測試特定的工作流程：

```bash
# 測試 PR 事件觸發的工作流程
act pull_request -W .github/workflows/test-mkdocs-build.yml

# 測試 push 事件觸發的工作流程
act push -W .github/workflows/mkdocs-gh_deploy.yml
```

注意：`act` 預設會使用最小的 Docker 映像，可能缺少某些依賴。您可以使用 `-P ubuntu-latest=nektos/act-environments-ubuntu:18.04` 參數指定更完整的映像。

## 使用 PR 觸發測試工作流程

我們已經設置了兩個工作流程，每個工作流程都有明確的單一職責：

1. `mkdocs-gh_deploy.yml` - 只在合併到 master 後執行部署
2. `test-mkdocs-build.yml` - 只在 PR 時測試 MkDocs 構建

這種分離的設計有以下優點：

- 職責單一，每個工作流程只負責一個任務
- 避免重複執行相同的測試步驟
- 在 PR 階段就能確認 MkDocs 構建是否正常，而不必等到合併後才發現問題
- 部署工作流程更加簡潔，只專注於部署任務

## 最佳實踐

1. 始終在 PR 階段測試工作流程的變更
2. 使用條件語句（如 `if: github.event_name == 'pull_request'`）區分 PR 和 push 事件
3. 對於需要寫入權限的操作（如部署），只在 push 到主分支時執行
4. 使用 `--strict` 參數進行構建，以確保所有警告都被視為錯誤
