variable "environment" {
  type = string
}

variable "deletion_protection" {
  type = bool
}

variable "wait_for_rollout" {
  type = bool
}

variable "kubernetes_namespace" {
  type    = string
  default = "default"
}

variable "gcp_project" {
  description = "GCP project name."
  type        = string
  default     = "tagtoo-tracking"
}

# make sure serviceusage.googleapis.com and cloudresourcemanager.googleapis.com are enabled
variable "gcp_service_list" {
  description = "List of GCP service to be enabled for a project."
  type        = list(any)
  default = [
    "compute.googleapis.com",     # Compute Engine API
    "pubsub.googleapis.com",      # Pub/Sub API
    "stackdriver.googleapis.com", # Stack Driver API
    "monitoring.googleapis.com",  # Stackdriver Monitoring API
    "logging.googleapis.com",     # Cloud Logging API
  ]
}

variable "cloudflare" {
  description = "Cloudflare authentication."
  type = object({
    email      = string
    token      = string
    account_id = string
    zones      = map(any)
  })
  default = {
    email      = "<EMAIL>"
    token      = "d25230c0266c540df085dbb8391282fda76df"
    account_id = "6f324ec491bf7897105aa8ff896fff42"
    zones = {
      "tagtoo.co" : "e2d05a479f3c2046a0400a5d859866c8"
    }
  }
}

variable "pub_sub_topic_facebook_capi" {
  description = "Pub/Sub settings for facebook capi topic."
  type = object({
    publishers = list(string)
  })
  default = {
    publishers = ["serviceAccount:<EMAIL>"]
  }
}

variable "pub_sub_topic_lta" {
  description = "Pub/Sub settings for LTA-facebook capi topic."
  type = object({
    publishers = list(string)
  })
  default = {
    publishers = ["serviceAccount:<EMAIL>"]
  }
}

variable "pub_sub_topic_lta_facebook_capi_batch" {
  description = "Pub/Sub settings for LTA-facebook capi batch topic."
  type = object({
    publishers = list(string)
  })
  default = {
    publishers = ["serviceAccount:<EMAIL>"]
  }
}

variable "dataflow_facebook_capi_compressor" {
  description = "Dataflow settings for facebook capi compressor pipeline."
  type = object({
    max_workers  = number
    machine_type = string
  })
  default = {
    max_workers  = 1
    machine_type = "n1-standard-1"
  }
}

variable "dataflow_tagtoo_event_user_deduplicator" {
  description = "Dataflow settings for tagtoo event user deduplication pipeline."
  type = object({
    max_workers  = number
    machine_type = string
  })
  default = {
    max_workers  = 3
    machine_type = "n1-standard-2"
  }
}

variable "container_cluster_event" {
  description = "Container cluster settings for event cluster."
  type = object({
    kubernetes_pool = object({
      preemptible     = bool
      service_account = string
    })
    api_pool = object({
      machine_type    = string
      service_account = string
      preemptible     = bool
    })
    s2s_system_pool = object({
      machine_type    = string
      preemptible     = bool
      service_account = string
    })
    s2s_subscriber_pool = object({
      machine_type    = string
      service_account = string
      preemptible     = bool
    })
    s2s_lta_subscriber_pool = object({
      machine_type    = string
      service_account = string
      preemptible     = bool
    })
    config_api_pool = object({
      machine_type    = string
      service_account = string
      preemptible     = bool
    })
    bq_writer_subscriber_pool = object({
      machine_type    = string
      service_account = string
      preemptible     = bool
    })
    user_unify_pool = object({
      machine_type    = string
      service_account = string
      preemptible     = bool
    })
  })
}

variable "event_api" {
  description = "General settings for event API microservices."
  type = object({
    ingress_subdomain = string
    web = object({
      image_tag                         = string
      min_replicas                      = number
      max_replicas                      = number
      target_cpu_utilization_percentage = number
      env = object({
        debug                 = string
        secret_key            = string
        config_api_auth_token = string
        api_doc_username      = string
        api_doc_password      = string
      })
      limits = object({
        cpu    = string
        memory = string
      })
      requests = object({
        cpu    = string
        memory = string
      })
    })
    api_versions = list(string)
  })
}

variable "event_s2s" {
  description = "General settings for event S2S microservices."
  type = object({
    common = object({
      env = object({
        mode                  = string
        config_api_auth_token = string
      })
    })
    lta_subscriber = object({
      image_tag                         = string
      min_replicas                      = number
      max_replicas                      = number
      target_cpu_utilization_percentage = number
      max_messages                      = number
      requests = object({
        cpu    = string
        memory = string
      })
      limits = object({
        cpu    = string
        memory = string
      })
    })
    facebook_capi_subscriber = object({
      image_tag = string
      batch = object({
        min_replicas                      = number
        max_replicas                      = number
        target_cpu_utilization_percentage = number
        max_messages                      = number
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
      singleton = object({
        min_replicas                      = number
        max_replicas                      = number
        target_cpu_utilization_percentage = number
        max_messages                      = number
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
      lta_batch = object({
        min_replicas                      = number
        max_replicas                      = number
        target_cpu_utilization_percentage = number
        max_messages                      = number
        requests = object({
          cpu    = string
          memory = string
        })
        limits = object({
          cpu    = string
          memory = string
        })
      })
    })
  })
}

variable "event_config_api" {
  description = "General settings for event config API microservices."
  type = object({
    ingress_subdomain = string
    web = object({
      image_tag = string
      env = object({
        debug         = string
        secret_key    = string
        allowed_hosts = string
      })
      limits = object({
        cpu    = string
        memory = string
      })
      requests = object({
        cpu    = string
        memory = string
      })
    })
  })
}

variable "bq_writer" {
  description = "General settings for BigQuery writer microservices."
  type = object({
    env = object({
      test_mode                      = string
      integrated_write_enabled       = string
      integrated_write_sample_rate   = string
      integrated_queue_maxsize       = string
      max_immediate_retries          = string
      max_delayed_retries            = string
      bq_debug_daily_budget          = string
    })
    ip2location = object({
      image_tag = string
      requests = object({
        cpu               = string
        memory            = string
        ephemeral_storage = string
      })
    })
    tagtoo_event_subscriber = object({
      containers_per_pod                = number
      image_tag                         = string
      max_messages                      = number
      min_replicas                      = number
      max_replicas                      = number
      target_cpu_utilization_percentage = number
      requests = object({
        cpu               = string
        memory            = string
        ephemeral_storage = string
      })
    })
  })
}


variable "user_unify" {
  description = "General settings for User-unify microservices"
  type = object({
    candidate_prediction_job = object({
      image_tag = string
      requests = object({
        cpu    = string
        memory = string
      })
      limits = object({
        cpu    = string
        memory = string
      })
    })
  })
}

# 雙寫入架構監控變數
variable "dual_write_error_rate_threshold" {
  description = "雙寫入錯誤率警報閾值"
  type        = number
  default     = 0.01  # 1%
}

variable "dual_write_latency_threshold" {
  description = "雙寫入延遲警報閾值 (秒)"
  type        = number
  default     = 5.0   # 5 秒
}
