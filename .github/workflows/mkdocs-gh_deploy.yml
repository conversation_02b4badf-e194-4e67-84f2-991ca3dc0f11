# ref: https://squidfunk.github.io/mkdocs-material/publishing-your-site/#with-github-actions
name: ci
on:
  push:
    branches:
      - master
permissions:
  contents: write
jobs:
  mkdocs-gh_deploy:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: docs/ # ref: https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions#defaultsrun
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: 3.9
      - uses: actions/cache@v4
        with:
          key: ${{ github.ref }}
          path: .cache
      - run: pip install pdm
      - run: pdm install --no-self
      - name: Deploy MkDocs
        run: pdm run mkdocs gh-deploy --force
