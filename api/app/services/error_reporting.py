import logging

from google.cloud import error_reporting

from app.config import settings

logger = logging.getLogger(__file__)


# https://googleapis.dev/python/clouderrorreporting/latest/client.html
class ErrorReportingClient(object):
    def __init__(
        self,
        project: str = None,
        service: str = None,
    ):
        self.project = project or settings.GCP_PROJECT_NAME
        self.service = service or settings.GCP_ERROR_REPORTING_SERVICE_NAME
        self._client = None

    @property
    def client(self):
        if not self._client:
            self._client = error_reporting.Client(
                project=self.project,
                service=self.service,
            )
        return self._client

    def _report_exception(self, *args, **kwargs):
        self.client.report_exception(*args, **kwargs)

    def _report(self, message):
        self.client.report(message)

    def report(self, error):
        if isinstance(error, Exception):
            self._report_exception()
        elif isinstance(error, str):
            message = error
            self._report(message)
