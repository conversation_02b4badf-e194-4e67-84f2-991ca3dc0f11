## Pub/Sub Subscriber

Run a Pub/Sub subscriber by given name.

```
python subscribe.py [-h] 
                    [--version {v1}] 
                    [--max-messages MAX_MESSAGES] {facebook_capi_batch,facebook_capi_singleton,lta,lta_facebook_capi_batch}
```

**Instances**

|Subscriber Name|Type|Description|
|---|---|---|
|facebook_capi_batch|asynchronous pull|Post multiple events (<=1000) using **batch** to Facebook Conversion API|
|facebook_capi_singleton|asynchronous pull|Post single event to Facebook Conversion API|
|lta|asynchronous pull|Grab data from GSC, compose facebook pixel data and send to facebook_capi_batch
|lta_facebook_capi_batch|asynchronous pull|Post multiple events (<=1000) using **batch** from LTA sources to Facebook Conversion API|

## MQTT Subscriber

Run a MQTT subscriber by given name.

```
python worker.py [-h] {failed_batch_handler}
```

**Instances**

|Subscriber Name|Description|
|---|---|
|failed_batch_handler|For a failed batch request, it's impossible to tell which event cause the error. Therefore, de-compressed the batch into single events and publish them to the singleton channel.|    

## System Functions

Refresh config data(e.g. Pixel Token) in Redis database from Event Config API:

```
python update_db.py [-h] {1}
```
