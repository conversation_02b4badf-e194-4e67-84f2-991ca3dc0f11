import asyncio
import ipaddress
import logging
import time
from datetime import datetime
from typing import List, <PERSON><PERSON>

from fastapi import Depends, Request, status
from fastapi.exceptions import HTTPException

import aioredis
import orj<PERSON>

from app.config import settings
from app.exceptions import EventConfigTokenNotFoundError

logger = logging.getLogger(__name__)


def now():
    return datetime.now()


async def get_db():
    db = await aioredis.from_url(
        f'redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}',
        db=0,
        encoding='utf8',
        decode_responses=True,
    )
    return db


def get_system_time():
    return time.time()


def get_ip_from_request(
    request: Request,
):
    # Extract IP string from header 'X-Forwarded-For'
    ip = request.headers.get('x-forwarded-for')
    ip = ip.split(',')[0].strip() if ip else request.client.host

    # 分類 IP 地址為 IPv4 或 IPv6
    ipv4 = None
    ipv6 = None

    try:
        ip_obj = ipaddress.ip_address(ip)
        if ip_obj.version == 4:
            ipv4 = ip
        elif ip_obj.version == 6:
            ipv6 = ip
    except Exception:
        # 如果 IP 地址無效，保留原始值以便記錄
        pass

    return {'ipv4': ipv4, 'ipv6': ipv6}


def is_global_ip(ip: str) -> bool:
    # Check if the ip is a valid global IP
    try:
        _ip = ipaddress.ip_address(ip)
        if not _ip.is_global:
            return False
    except Exception:
        return False
    return True


def construct_http_exception_message(
    request: Request,
    exc: HTTPException,
    extras: dict = None,
):
    request_body = getattr(exc, 'request_body', None)
    if request_body:
        request_body = orjson.loads(request_body)
    message_segments = [
        f'HTTPException: {exc.detail}. ',
        f'Request URL => {request.method} {request.url}. ',
        f'Status Code => {exc.status_code}. ',
        f'Request Body => {request_body}. ',
        f'Request Headers => {dict(request.headers)}. ',
    ]
    if extras:
        extra_message = orjson.dumps(extras)
        message_segments += [extra_message]
    message = '\n'.join(message_segments)
    return message


# Event-config helpers


def construct_key(token):
    return f'event-api-token-{token}'


async def store_event_configs_to_redis(
    db: aioredis.Redis,
    data: List[dict],
) -> Tuple[str, dict]:
    futures = [
        db.set(
            construct_key(d.pop('token')),
            orjson.dumps(d),
            settings.EVENT_CONFIG_API_TOKEN_EXPIRE_TIME,
        )
        for d in data
    ]
    return await asyncio.gather(*futures)


async def store_event_configs(data):
    db = await get_db()
    await store_event_configs_to_redis(db, data)


async def update_event_configs():
    from app.services import event_config_client

    event_config_data = await event_config_client.get_configs()
    await store_event_configs(event_config_data)


async def get_event_config_data_from_redis(
    db: aioredis.Redis,
    token: str,
) -> dict:
    key = construct_key(token)

    value = await db.get(key)
    if value is not None:
        data = orjson.loads(value)
        return data
    else:
        return None


async def verify_event_config_token(
    db: aioredis.Redis,
    token: str,
) -> dict:
    event_config = await get_event_config_data_from_redis(db, token)
    if event_config is None:
        raise EventConfigTokenNotFoundError(token)
    return event_config


async def get_event_config(
    token: str,
    db: aioredis.Redis = Depends(get_db),
):
    try:
        event_config = await verify_event_config_token(db, token)
    except EventConfigTokenNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f'Authentication Failed (Invalid token: {e.token})',
        )
    return event_config
