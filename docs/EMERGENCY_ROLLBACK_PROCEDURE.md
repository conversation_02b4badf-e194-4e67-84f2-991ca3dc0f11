# BigQuery 雙寫入架構緊急回滾程序

## 🚨 **緊急回滾決策矩陣**

| **問題嚴重程度** | **回滾等級** | **決策時間** | **執行人員** |
|-----------------|-------------|-------------|-------------|
| **服務中斷** | Level 3 | 立即 | 任何工程師 |
| **錯誤率 > 5%** | Level 2 | 15 分鐘內 | 資深工程師 |
| **成本超標 > 50%** | Level 1 | 30 分鐘內 | 技術主管 |
| **效能下降 > 70%** | Level 2 | 30 分鐘內 | 資深工程師 |

## 🔴 **Level 1: 立即停用功能（< 5 分鐘）**

### **步驟 1.1: 環境變數緊急停用**
```bash
# 方法 A: 直接設定環境變數（最快）
kubectl set env deployment/bq-writer-tagtoo-event-subscriber \
  INTEGRATED_WRITE_ENABLED=false \
  -n event-prod

# 方法 B: 透過 ConfigMap 更新
kubectl patch configmap bq-writer-env \
  -p '{"data":{"integrated_write_enabled":"false"}}' \
  -n event-prod
```

### **步驟 1.2: 驗證功能已停用**
```bash
# 檢查環境變數
kubectl get deployment bq-writer-tagtoo-event-subscriber -n event-prod \
  -o jsonpath='{.spec.template.spec.containers[0].env[?(@.name=="INTEGRATED_WRITE_ENABLED")].value}'

# 檢查日誌確認雙寫入已停用
kubectl logs -f deployment/bq-writer-tagtoo-event-subscriber -n event-prod | grep "雙寫入器已禁用"
```

**預期時間**: 2-5 分鐘  
**影響範圍**: 僅停用新功能，主要功能不受影響

## 🟡 **Level 2: 配置回滾（< 30 分鐘）**

### **步驟 2.1: Terraform 配置回滾**
```bash
# 進入部署容器
make deploy-up
docker exec -it deploy sh

# 備份當前配置
cd /src
cp prod.tfvars prod.tfvars.backup.$(date +%Y%m%d_%H%M%S)

# 應用回滾配置
terraform plan -var-file=prod.tfvars.rollback
terraform apply -var-file=prod.tfvars.rollback
```

### **步驟 2.2: 回滾配置範本**
```bash
# 建立回滾配置檔案
cat > prod.tfvars.rollback << 'EOF'
bq_writer = {
  env = {
    test_mode                      = "False"
    # 雙寫入架構配置 - 完全停用
    integrated_write_enabled       = "false"
    integrated_write_sample_rate   = "0.0"
    # 回滾佇列和重試配置
    integrated_queue_maxsize       = "1000"
    max_immediate_retries          = "3"
    max_delayed_retries            = "5"
    bq_debug_daily_budget          = "5.0"
  }
  ip2location = {
    image_tag = "2025-04-14"
    requests = {
      # 回滾資源配置到原始值
      cpu               = "100m"
      memory            = "400Mi"
      ephemeral-storage = "3Gi"
    }
  }
}
EOF
```

**預期時間**: 15-30 分鐘  
**影響範圍**: 資源配置回滾，移除雙寫入相關監控

## 🟠 **Level 3: 完整回滾（< 60 分鐘）**

### **步驟 3.1: Kubernetes 部署完整回滾**
```bash
# 檢查部署歷史
kubectl rollout history deployment/bq-writer-tagtoo-event-subscriber -n event-prod

# 回滾到雙寫入架構之前的版本
kubectl rollout undo deployment/bq-writer-tagtoo-event-subscriber -n event-prod --to-revision=<previous-revision>

# 驗證回滾
kubectl rollout status deployment/bq-writer-tagtoo-event-subscriber -n event-prod
```

### **步驟 3.2: BigQuery 資料庫清理**
```bash
# 選項 A: 刪除表格（如果確定不需要）
bq rm -f tagtoo-tracking:event_prod.integrated_event

# 選項 B: 建立備份後刪除
bq cp tagtoo-tracking:event_prod.integrated_event \
     tagtoo-tracking:event_backup.integrated_event_backup_$(date +%Y%m%d)
bq rm -f tagtoo-tracking:event_prod.integrated_event

# 選項 C: 只停用寫入，保留表格供分析（推薦）
# 無需額外操作，表格保留供後續分析
```

**預期時間**: 45-60 分鐘  
**影響範圍**: 完全移除雙寫入架構相關組件

## ✅ **回滾驗證檢查清單**

### **功能驗證**
- [ ] **主要寫入功能正常**: tagtoo_event 表格正常寫入
- [ ] **Pub/Sub 訊息處理正常**: 無積壓訊息
- [ ] **錯誤率恢復正常**: < 0.1%
- [ ] **系統效能恢復**: 延遲和吞吐量回到基準值

### **資源驗證**
- [ ] **CPU 使用率**: 回到 100m 基準
- [ ] **記憶體使用率**: 回到 400Mi 基準
- [ ] **Pod 數量**: 在正常範圍內
- [ ] **節點資源**: 無異常使用

### **監控驗證**
- [ ] **警報狀態**: 無異常警報
- [ ] **日誌正常**: 無錯誤日誌
- [ ] **指標正常**: 所有指標在正常範圍
- [ ] **成本恢復**: 回到基準成本水準

### **資料完整性驗證**
- [ ] **tagtoo_event 資料**: 無資料遺失
- [ ] **事件處理**: 所有事件正常處理
- [ ] **IP2Location 功能**: 正常運作
- [ ] **下游系統**: 無影響

## 📞 **緊急聯絡和升級程序**

### **聯絡清單**
1. **技術主管**: 立即通知
2. **DevOps 團隊**: 協助執行
3. **產品團隊**: 業務影響評估
4. **客戶支援**: 客戶溝通準備

### **升級程序**
1. **發現問題** → **評估嚴重程度** → **選擇回滾等級**
2. **執行回滾** → **驗證結果** → **通知相關人員**
3. **問題分析** → **改進計畫** → **重新部署準備**

## 📊 **回滾後的恢復計畫**

### **問題分析**
1. **根因分析**: 識別導致回滾的根本原因
2. **影響評估**: 評估回滾對業務的影響
3. **改進計畫**: 制定防止類似問題的改進措施

### **重新部署準備**
1. **修復驗證**: 確認問題已修復
2. **測試環境驗證**: 在測試環境完整驗證
3. **漸進式重新部署**: 更保守的部署策略
4. **加強監控**: 增加監控覆蓋率和敏感度

---

**⚠️ 重要提醒**: 
- 在執行任何回滾操作前，請先評估問題的嚴重程度
- 記錄所有回滾操作和決策過程
- 回滾完成後立即進行根因分析

*最後更新: 2025-09-03*  
*版本: v1.0*
