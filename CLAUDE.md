# Development Guidelines for ad-track

## Build & Run Commands
- Docker: `make event-api-up` (start), `make event-api-down` (stop)
- Build image: `make build-event-api LATEST=true` (and similar for other services)
- Tests: `pytest app/tests/` (API), `cd s2s && pytest` (S2S)
- Single test: `pytest app/tests/test_file.py::TestClass::test_function -v`
- Shell tests: `tests/*/test_all.sh` (runs all integration tests)

## Code Style Guidelines
- **Imports**: Standard library → third-party → local imports, alphabetized within blocks
- **Naming**: CamelCase for classes, snake_case for functions/variables, UPPERCASE for constants
- **Typing**: Use type hints for parameters and returns (`List[str]`, `Dict[str, Any]`, etc.)
- **Formatting**: 4-space indentation, max line length ~100 characters
- **Error handling**: Use specific exceptions, custom exceptions in exceptions.py
- **Documentation**: Google-style docstrings for complex functions and classes
- **Testing**: Write test cases using pytest, mock external services
- **Structure**: Organize code by function (routers, schemas, services)
- **Framework conventions**: Follow FastAPI patterns for API endpoints and Pydantic for models