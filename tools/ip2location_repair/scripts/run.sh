#!/bin/bash

# 檢查 GCP 憑證
if [ ! -f "$GOOGLE_APPLICATION_CREDENTIALS" ]; then
  echo "錯誤: GCP 憑證檔案未設定或不存在"
  echo "請設置 GOOGLE_APPLICATION_CREDENTIALS 環境變數指向你的 Google Cloud 服務帳號金鑰檔案"
  echo "例如: export GOOGLE_APPLICATION_CREDENTIALS=\"/path/to/your/service-account-file.json\""
  exit 1
fi

# 檢查工作目錄
if [ ! -f "./fix_location.py" ]; then
  echo "錯誤: 找不到 fix_location.py 文件"
  echo "請確保在 tools/ip2location_repair 目錄下執行此腳本"
  exit 1
fi

# 構建和啟動 docker-compose
echo "正在啟動 ip2location-fix 環境..."

# 判斷是否有參數傳入
if [ $# -eq 0 ]; then
  echo "使用預設參數執行..."
  docker-compose up --build ip2location_fix
else
  echo "使用自訂參數執行: $@"
  docker-compose run ip2location_fix python /app/fix_location.py "$@"
fi
