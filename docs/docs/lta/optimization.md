# LTA 數據處理優化

本文檔詳細說明了對 LTA 數據處理系統的優化措施，包括資源配置、調度策略和性能改進。

## 背景

LTA 數據處理系統負責處理大量用戶數據並將其發送到 Facebook Conversion API。在高負載情況下，我們觀察到以下問題：

1. **執行時間差異大**：相似大小的數據文件處理時間從 59 秒到 401 秒不等
2. **節點資源競爭**：多個 Job 集中在少數節點上，導致資源爭用
3. **資源分配不均**：某些節點 CPU 使用率接近 100%，而其他節點僅 20%

## 優化措施

### 1. 資源請求與限制優化

我們調整了 `k8s_job_load_lta_data.yaml` 中的資源配置：

```yaml
resources:
  requests:
    memory: 1.5Gi
    cpu: 1000m  # 從 800m 增加到 1000m
  limits:
    memory: 3Gi
    cpu: "1.5"  # 從 1 增加到 1.5
```

**優化原理**：
- 增加 CPU 請求，更準確地反映 Job 的實際資源需求
- 防止調度器將過多 Job 分配到同一節點
- 增加 CPU 限制，確保在高負載時有足夠的處理能力

### 2. Pod 反親和性規則

添加了 Pod 反親和性規則，鼓勵調度器將同類型的 Job 分散到不同節點：

```yaml
affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchExpressions:
          - key: job-type
            operator: In
            values:
            - load-lta-data
        topologyKey: "kubernetes.io/hostname"
```

**優化原理**：
- 使用 `preferredDuringSchedulingIgnoredDuringExecution` 表示這是一個軟性要求
- 設置高權重 (100) 使調度器優先考慮這一規則
- 使用 `topologyKey: "kubernetes.io/hostname"` 確保基於節點的分散

### 3. 拓撲分佈約束

添加了拓撲分佈約束，確保 Job 均勻分佈在各節點：

```yaml
topologySpreadConstraints:
- maxSkew: 1
  topologyKey: kubernetes.io/hostname
  whenUnsatisfiable: ScheduleAnyway
  labelSelector:
    matchLabels:
      job-type: load-lta-data
```

**優化原理**：
- `maxSkew: 1` 表示節點間 Job 數量差異不超過 1
- `whenUnsatisfiable: ScheduleAnyway` 確保在無法滿足約束時仍能調度
- 與反親和性規則結合，提供更精細的負載分佈控制

### 4. 優先級設置

創建了 PriorityClass 並在 Job 模板中引用：

```yaml
# PriorityClass 定義
apiVersion: scheduling.k8s.io/v1
kind: PriorityClass
metadata:
  name: lta-job-priority
value: 1000
globalDefault: false
description: "Priority class for LTA data processing jobs"

# Job 模板中引用
priorityClassName: lta-job-priority
```

**優化原理**：
- 設置適當的優先級 (1000)，高於系統默認值
- 在資源緊張時，確保 LTA Job 能獲得更好的調度
- 不設為全局默認，避免影響其他工作負載

### 5. Terraform 管理

將 PriorityClass 添加到 Terraform 配置中：

```hcl
resource "kubernetes_priority_class" "lta_job_priority" {
  metadata {
    name = "lta-job-priority"
  }
  value = 1000
  global_default = false
  description = "Priority class for LTA data processing jobs"
}
```

**優化原理**：
- 確保環境一致性，避免手動配置差異
- 遵循基礎設施即代碼 (IaC) 最佳實踐
- 簡化部署和管理流程

## 效果評估

優化措施實施後，預期會帶來以下效果：

1. **執行時間更一致**：不同 Job 的執行時間差異顯著減小
2. **資源分配更均勻**：各節點的 CPU 和內存使用率更加平衡
3. **整體執行時間縮短**：由於資源分配更均勻，整體批處理時間會縮短
4. **系統穩定性增強**：減少因資源競爭導致的性能問題

## 監控與調整

為了評估優化效果，我們應該監控以下指標：

1. **Job 執行時間分佈**：
   - 最短、最長和平均執行時間
   - 執行時間標準差

2. **節點資源使用率**：
   - CPU 和內存使用率
   - 各節點間的使用率差異

3. **Job 分佈情況**：
   - 各節點上運行的 Job 數量
   - 節點間 Job 數量的差異

如果效果不如預期，可以考慮：

1. 進一步調整資源請求和限制
2. 增加反親和性規則的權重
3. 調整拓撲分佈約束的參數
4. 實施批處理優化（需要代碼修改）

## 未來優化方向

除了當前實施的優化措施外，未來還可以考慮：

1. **動態批處理**：
   - 根據系統負載動態調整批次大小
   - 在高負載時使用較小批次，低負載時使用較大批次

2. **自適應資源請求**：
   - 根據歷史數據自動調整資源請求和限制
   - 實現更精確的資源分配

3. **更精細的監控**：
   - 添加更詳細的指標，如批次處理時間、API 響應時間等
   - 實施更精細的告警策略

4. **節點自動擴展**：
   - 根據負載自動添加或移除節點
   - 優化節點池配置，提高資源利用率

## 參考資料

- [Kubernetes Pod 優先級和搶佔](https://kubernetes.io/docs/concepts/scheduling-eviction/pod-priority-preemption/)
- [Kubernetes Pod 拓撲分佈約束](https://kubernetes.io/docs/concepts/scheduling-eviction/topology-spread-constraints/)
- [Kubernetes Pod 親和性和反親和性](https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#affinity-and-anti-affinity)
