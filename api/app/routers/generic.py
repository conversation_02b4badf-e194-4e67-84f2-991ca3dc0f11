from typing import Callable, List

from fastapi import Request, Response, status
from fastapi.exceptions import HTTPException
from fastapi.routing import APIRoute

from app.config import settings
from app.enums import EventAPIScope
from app.services import error_reporting_client
from app.utils import (
    construct_http_exception_message,
    get_db,
    get_event_config,
    get_system_time,
)


class EventAPICommonRoute(APIRoute):

    router_scope_map = {
        '/permanent': EventAPIScope.PERMANENT,
    }

    def authenticate_scopes(
        self,
        request: Request,
        scopes: List[str],
        token: str,
    ):
        url_path = request.scope['path']
        scopes_has_auth = self.router_scope_map[url_path]
        if scopes_has_auth not in scopes:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=(
                    f'Token ({token}) has no authority on scopes: '
                    f'{scopes_has_auth.name}'
                ),
            )

    def get_route_handler(self) -> Callable:
        original_route_handler = super().get_route_handler()

        async def custom_route_handler(request: Request) -> Response:
            request.state.system_time = get_system_time()
            if request.url != request.url_for('event'):
                # For endpoint `/event/v1`, the token is carried at request body, and thus
                # we'll do token verification in path operation function. For other endpoint,
                # we'll do it right before entering path operation and thus data validation can
                # be skipped if the token is invalid.
                token = request.headers.get(settings.API_TOKEN_HEADER)
                db = await get_db()
                event_config = await get_event_config(token=token, db=db)
                scopes = event_config['scopes']
                self.authenticate_scopes(request, scopes, token)

            try:
                return await original_route_handler(request)
            except HTTPException as exc:
                body = await request.body()
                exc.request_body = body
                message = construct_http_exception_message(
                    request,
                    exc,
                )
                error_reporting_client.report(message)
                raise HTTPException(status_code=exc.status_code, detail=message)

        return custom_route_handler
