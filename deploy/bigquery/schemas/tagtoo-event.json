[{"description": "The Tagtoo ecommerce ID.", "name": "ec_id", "type": "INTEGER", "mode": "REQUIRED"}, {"description": "A long-lived user ID.", "name": "permanent", "type": "STRING", "mode": "REQUIRED"}, {"description": "The selected language on ecommerce webpage.", "name": "language", "type": "STRING", "mode": "REQUIRED"}, {"description": "The current page link.", "name": "link", "type": "STRING", "mode": "REQUIRED"}, {"description": "The previous page link.", "name": "referrer", "type": "STRING", "mode": "NULLABLE"}, {"description": "The time an event is triggered. In UTC+0 format.", "name": "event_time", "type": "TIMESTAMP", "mode": "REQUIRED"}, {"description": "Client IP Address.", "name": "ip_address", "type": "STRING", "mode": "NULLABLE"}, {"description": "GEO location information resolved from client IP address.", "name": "location", "type": "RECORD", "mode": "NULLABLE", "fields": [{"description": "Two-character country code based on ISO 3166.", "name": "country_code", "type": "STRING", "mode": "REQUIRED"}, {"description": "Region or state name.", "name": "region_name", "type": "STRING", "mode": "NULLABLE"}, {"description": "City name.", "name": "city_name", "type": "STRING", "mode": "NULLABLE"}, {"description": "City latitude. Defaults to capital city latitude if city is unknown.", "name": "latitude", "type": "FLOAT", "mode": "NULLABLE"}, {"description": "City longitude. Defaults to capital city longitude if city is unknown.", "name": "longitude", "type": "FLOAT", "mode": "NULLABLE"}, {"description": "ZIP code or Postal code.", "name": "zip_code", "type": "STRING", "mode": "NULLABLE"}]}, {"description": "The parsed user agent information.", "name": "user_agent", "type": "RECORD", "mode": "REQUIRED", "fields": [{"description": "Browser name.", "name": "browser", "type": "STRING", "mode": "REQUIRED"}, {"description": "Browser version.", "name": "browser_version", "type": "STRING", "mode": "REQUIRED"}, {"description": "Operation system name.", "name": "os", "type": "STRING", "mode": "REQUIRED"}, {"description": "Operation system version.", "name": "os_version", "type": "STRING", "mode": "REQUIRED"}, {"description": "Device name.", "name": "device", "type": "STRING", "mode": "REQUIRED"}, {"description": "Is mobile.", "name": "is_mobile", "type": "BOOLEAN", "mode": "REQUIRED"}, {"description": "Is tablet.", "name": "is_tablet", "type": "BOOLEAN", "mode": "REQUIRED"}, {"description": "Is PC.", "name": "is_pc", "type": "BOOLEAN", "mode": "REQUIRED"}, {"description": "Is touch capable.", "name": "is_touch_capable", "type": "BOOLEAN", "mode": "REQUIRED"}, {"description": "Is bot.", "name": "is_bot", "type": "BOOLEAN", "mode": "REQUIRED"}]}, {"description": "The detail of an event.", "name": "event", "type": "RECORD", "mode": "REQUIRED", "fields": [{"description": "Event name.", "name": "name", "type": "STRING", "mode": "REQUIRED"}, {"description": "The total price of an event.", "name": "value", "type": "FLOAT", "mode": "NULLABLE"}, {"description": "Sale currency.", "name": "currency", "type": "STRING", "mode": "NULLABLE"}, {"description": "Product item details.", "name": "items", "type": "RECORD", "mode": "REPEATED", "fields": [{"description": "Product ID.", "name": "id", "type": "STRING", "mode": "REQUIRED"}, {"description": "Product name.", "name": "name", "type": "STRING", "mode": "REQUIRED"}, {"description": "Sale price.", "name": "price", "type": "FLOAT", "mode": "NULLABLE"}, {"description": "Product quantity.", "name": "quantity", "type": "FLOAT", "mode": "NULLABLE"}, {"description": "Product availability.", "name": "availability", "type": "STRING", "mode": "NULLABLE"}]}, {"description": "Additional data for an event.", "name": "custom_data", "type": "RECORD", "mode": "NULLABLE", "fields": [{"description": "Payment method e.g. credit_card.", "name": "payment_method", "type": "STRING", "mode": "NULLABLE"}, {"description": "Shipping method e.g. 711.", "name": "shipping_method", "type": "STRING", "mode": "NULLABLE"}, {"description": "The web context copied by the user.", "name": "copy_string", "type": "STRING", "mode": "NULLABLE"}, {"description": "To show the on/off or entry/exit status of an event.", "name": "status", "type": "BOOLEAN", "mode": "NULLABLE"}, {"description": "To show how long an user stays on one page.", "name": "focus_minutes", "type": "FLOAT", "mode": "NULLABLE"}, {"description": "Ecommerce campaign or form title.", "name": "campaign_name", "type": "STRING", "mode": "NULLABLE"}, {"description": "Auth method, e.g. google.", "name": "auth_method", "type": "STRING", "mode": "NULLABLE"}, {"description": "The unique ID for an purchase.", "name": "order_id", "type": "STRING", "mode": "NULLABLE"}, {"description": "The height of a web page.", "name": "document_height", "type": "FLOAT", "mode": "NULLABLE"}, {"description": "The scroll top of a web page.", "name": "scroll_top", "type": "FLOAT", "mode": "NULLABLE"}, {"description": "The string entered by the user for the search.", "name": "search_string", "type": "STRING", "mode": "NULLABLE"}, {"description": "The breadcrumb for a product page.", "name": "breadcrumb", "type": "STRING", "mode": "NULLABLE"}]}]}, {"description": "User information", "name": "user", "type": "RECORD", "mode": "NULLABLE", "fields": [{"description": "A hashed email.", "name": "em", "type": "STRING", "mode": "NULLABLE"}, {"description": "A hashed phone number.", "name": "ph", "type": "STRING", "mode": "NULLABLE"}, {"description": "A hashed username.", "name": "un", "type": "STRING", "mode": "NULLABLE"}, {"description": "A hashed user gender.", "name": "gd", "type": "STRING", "mode": "NULLABLE"}, {"description": "A hashed date of birth.", "name": "db", "type": "STRING", "mode": "NULLABLE"}, {"description": "A short-lived Facebook session ID.", "name": "fbp", "type": "STRING", "mode": "NULLABLE"}, {"description": "A short-lived Facebook click ID.", "name": "fbc", "type": "STRING", "mode": "NULLABLE"}, {"description": "A short-lived Google Analytics session ID.", "name": "ga", "type": "STRING", "mode": "NULLABLE"}, {"description": "A short-lived Google Analytics user ID.", "name": "gid", "type": "STRING", "mode": "NULLABLE"}, {"description": "Line user ID.", "name": "lmid", "type": "STRING", "mode": "NULLABLE"}]}, {"description": "Session information.", "name": "session", "type": "RECORD", "mode": "REQUIRED", "fields": [{"description": "A short-lived session ID.", "name": "id", "type": "STRING", "mode": "REQUIRED"}, {"description": "The session source or utm_source query param.", "name": "source", "type": "STRING", "mode": "NULLABLE"}, {"description": "The session medium or utm_medium query param.", "name": "medium", "type": "STRING", "mode": "NULLABLE"}, {"description": "The session campaign or utm_campaign query param.", "name": "campaign", "type": "STRING", "mode": "NULLABLE"}, {"description": "The session keyword or utm_term query param\n.", "name": "term", "type": "STRING", "mode": "NULLABLE"}, {"description": "The session content or utm_content query param.", "name": "content", "type": "STRING", "mode": "NULLABLE"}]}]