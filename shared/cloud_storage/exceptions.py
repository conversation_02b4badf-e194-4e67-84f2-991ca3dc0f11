from collections import defaultdict


class GoogleStorageIOError(Exception):
    """Cannot get bucket on google cloud storage."""


class LtaExceptions(Exception):
    def __init__(self) -> None:
        self.errors = defaultdict(list)

    def add_error(self, error: str, line_number: int) -> None:
        self.errors[error].append(f'{line_number}')

    def __str__(self) -> str:
        return 'CSV file error\n{}'.format(
            '\n'.join(f'{key}: {" ".join(val)} ' for key, val in self.errors.items())
        )
