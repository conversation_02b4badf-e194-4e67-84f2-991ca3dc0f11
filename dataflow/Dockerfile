FROM python:3.8-buster

ENV CLOUD_SDK_VERSION=322.0.0
ENV APACHE_BEAM_VERSION=2.37.0

# Google cloud SDK

WORKDIR /
ENV PATH /google-cloud-sdk/bin:$PATH
RUN apt-get -qqy update && apt-get install -qqy \
        curl \
    && curl -O https://dl.google.com/dl/cloudsdk/channels/rapid/downloads/google-cloud-sdk-${CLOUD_SDK_VERSION}-linux-x86_64.tar.gz && \
    tar xzf google-cloud-sdk-${CLOUD_SDK_VERSION}-linux-x86_64.tar.gz && \
    rm google-cloud-sdk-${CLOUD_SDK_VERSION}-linux-x86_64.tar.gz && \
    ln -s /lib /lib64

RUN gcloud config set core/disable_usage_reporting true && \
    gcloud config set component_manager/disable_update_check true && \
    gcloud config set metrics/environment github_docker_image
VOLUME ["/root/.config"]

# Dataflow SDK

RUN pip install apache-beam[gcp]==${APACHE_BEAM_VERSION}

WORKDIR /src