"""
成本最佳化的除錯查詢系統

基於 tracking_id 策略的快速除錯查詢，無需額外表格。
實施成本控制和查詢最佳化，大幅降低除錯成本。

版本: v2.1 (tracking_id 核心策略)
日期: 2025-08-18
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from google.cloud import bigquery

logger = logging.getLogger(__name__)


class CostOptimizedDebugQueries:
    """成本最佳化的除錯查詢範本"""
    
    def __init__(self, bq_client: bigquery.Client):
        """
        初始化除錯查詢系統
        
        Args:
            bq_client: BigQuery 客戶端
        """
        self.bq_client = bq_client
        self.daily_budget = 10.0  # $10 USD/day
        self.current_usage = 0.0
        self.query_history = []
        
        logger.info(f"CostOptimizedDebugQueries 初始化，日預算: ${self.daily_budget}")
    
    def find_event_by_tracking_id(self, tracking_id: str, project_id: str = "tagtoo-tracking") -> str:
        """
        通過 tracking_id 查找事件 (成本 < $0.50)
        
        Args:
            tracking_id: 追蹤 ID
            project_id: 專案 ID
            
        Returns:
            查詢 SQL
        """
        return f"""
        WITH integrated_event_info AS (
          SELECT 
            *,
            raw_json.tracking.message_id as source_message_id
          FROM `{project_id}.event_prod.integrated_event`
          WHERE raw_json.tracking.id = '{tracking_id}'
            AND DATE(event_time) >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)
          LIMIT 1
        )
        SELECT 
          ie.tracking_id,
          ie.event,
          ie.value,
          ie.currency,
          ie.event_time,
          ie.create_time,
          ie.raw_json.debug.session_hash,
          ie.raw_json.debug.ua_signature,
          ie.raw_json.debug.ref_domain,
          ie.raw_json.debug.geo_code,
          ie.source_message_id
        FROM integrated_event_info ie
        """
    
    def find_events_by_criteria(self, ec_id: int, event_date: str, 
                               project_id: str = "tagtoo-tracking", limit: int = 100) -> str:
        """
        按條件查找事件 (成本 < $2.00)
        
        Args:
            ec_id: 客戶 ID
            event_date: 事件日期 (YYYY-MM-DD)
            project_id: 專案 ID
            limit: 結果限制
            
        Returns:
            查詢 SQL
        """
        return f"""
        SELECT 
          raw_json.tracking.id as tracking_id,
          event,
          value,
          currency,
          event_time,
          raw_json.debug.session_hash,
          raw_json.debug.ua_signature,
          raw_json.debug.ref_domain,
          raw_json.tracking.message_id
        FROM `{project_id}.event_prod.integrated_event`
        WHERE ec_id = {ec_id}
          AND DATE(event_time) = '{event_date}'
          AND partner_source = 'legacy-tagtoo-event'
        ORDER BY event_time DESC
        LIMIT {limit}
        """
    
    def find_tagtoo_event_by_message_id(self, message_id: str, event_date: str,
                                       project_id: str = "tagtoo-tracking") -> str:
        """
        通過 message_id 查找原始 tagtoo_event (成本 < $1.00)
        
        Args:
            message_id: 訊息 ID
            event_date: 事件日期 (YYYY-MM-DD)
            project_id: 專案 ID
            
        Returns:
            查詢 SQL
        """
        return f"""
        SELECT 
          session_id,
          user_agent,
          referrer,
          ip_address,
          language,
          event,
          user,
          location,
          event_time,
          create_time
        FROM `{project_id}.event_prod.tagtoo_event`
        WHERE message_id = '{message_id}'
          AND DATE(event_time) = '{event_date}'
        LIMIT 1
        """
    
    def compare_events_consistency(self, tracking_id: str, 
                                  project_id: str = "tagtoo-tracking") -> str:
        """
        比較 tagtoo_event 和 integrated_event 的一致性 (成本 < $1.50)
        
        Args:
            tracking_id: 追蹤 ID
            project_id: 專案 ID
            
        Returns:
            查詢 SQL
        """
        return f"""
        WITH integrated_info AS (
          SELECT 
            *,
            raw_json.tracking.message_id as source_message_id
          FROM `{project_id}.event_prod.integrated_event`
          WHERE raw_json.tracking.id = '{tracking_id}'
            AND DATE(event_time) >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)
          LIMIT 1
        ),
        tagtoo_info AS (
          SELECT *
          FROM `{project_id}.event_prod.tagtoo_event`
          WHERE message_id = (SELECT source_message_id FROM integrated_info)
            AND DATE(event_time) >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY)
          LIMIT 1
        )
        SELECT 
          -- integrated_event 資料
          ie.tracking_id,
          ie.event as ie_event,
          ie.value as ie_value,
          ie.currency as ie_currency,
          ie.ec_id as ie_ec_id,
          ie.permanent as ie_permanent,
          
          -- tagtoo_event 資料
          te.event.name as te_event,
          te.event.value as te_value,
          te.event.currency as te_currency,
          te.ec_id as te_ec_id,
          te.permanent as te_permanent,
          
          -- 一致性檢查
          CASE 
            WHEN ie.event = te.event.name THEN 'MATCH' 
            ELSE 'MISMATCH' 
          END as event_consistency,
          CASE 
            WHEN ie.value = te.event.value THEN 'MATCH' 
            ELSE 'MISMATCH' 
          END as value_consistency,
          CASE 
            WHEN ie.ec_id = te.ec_id THEN 'MATCH' 
            ELSE 'MISMATCH' 
          END as ec_id_consistency
          
        FROM integrated_info ie
        LEFT JOIN tagtoo_info te ON TRUE
        """
    
    def get_daily_stats(self, event_date: str, project_id: str = "tagtoo-tracking") -> str:
        """
        獲取指定日期的統計資訊 (成本 < $3.00)
        
        Args:
            event_date: 事件日期 (YYYY-MM-DD)
            project_id: 專案 ID
            
        Returns:
            查詢 SQL
        """
        return f"""
        SELECT 
          COUNT(*) as total_events,
          COUNT(DISTINCT ec_id) as unique_customers,
          COUNT(DISTINCT event) as unique_event_types,
          COUNT(DISTINCT raw_json.tracking.id) as unique_tracking_ids,
          
          -- 事件類型分布
          COUNTIF(event = 'purchase') as purchase_events,
          COUNTIF(event = 'view_item') as view_item_events,
          COUNTIF(event = 'add_to_cart') as add_to_cart_events,
          
          -- 資料品質檢查
          COUNTIF(raw_json.tracking.id IS NULL) as missing_tracking_id,
          COUNTIF(raw_json.tracking.message_id IS NULL) as missing_message_id,
          
          -- 時間分布
          MIN(event_time) as earliest_event,
          MAX(event_time) as latest_event
          
        FROM `{project_id}.event_prod.integrated_event`
        WHERE DATE(event_time) = '{event_date}'
          AND partner_source = 'legacy-tagtoo-event'
        """
    
    def estimate_query_cost(self, query: str) -> float:
        """
        估算查詢成本
        
        Args:
            query: SQL 查詢
            
        Returns:
            預估成本 (USD)
        """
        try:
            job_config = bigquery.QueryJobConfig(dry_run=True)
            query_job = self.bq_client.query(query, job_config=job_config)
            bytes_processed = query_job.total_bytes_processed
            cost = (bytes_processed / (1024**4)) * 5.0  # $5 per TB
            return cost
        except Exception as e:
            logger.error(f"成本估算失敗: {e}")
            return 999.0  # 返回高成本以阻止執行
    
    def safe_execute_debug_query(self, query: str, description: str = "") -> Optional[List[Dict]]:
        """
        安全執行除錯查詢，控制成本
        
        Args:
            query: SQL 查詢
            description: 查詢描述
            
        Returns:
            查詢結果或 None
        """
        estimated_cost = self.estimate_query_cost(query)
        
        if self.current_usage + estimated_cost > self.daily_budget:
            logger.error(f"查詢成本 ${estimated_cost:.2f} 超過日預算限制 ${self.daily_budget}")
            raise Exception(f"查詢成本 ${estimated_cost:.2f} 超過日預算限制")
        
        try:
            logger.info(f"執行除錯查詢: {description}, 預估成本: ${estimated_cost:.2f}")
            
            query_job = self.bq_client.query(query)
            results = list(query_job.result())
            
            # 更新使用量
            self.current_usage += estimated_cost
            
            # 記錄查詢歷史
            self.query_history.append({
                'timestamp': datetime.utcnow(),
                'description': description,
                'cost': estimated_cost,
                'rows_returned': len(results)
            })
            
            logger.info(f"查詢完成，返回 {len(results)} 行結果")
            return results
            
        except Exception as e:
            logger.error(f"查詢執行失敗: {e}")
            raise
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """
        獲取使用統計
        
        Returns:
            使用統計資訊
        """
        return {
            'daily_budget': self.daily_budget,
            'current_usage': self.current_usage,
            'remaining_budget': self.daily_budget - self.current_usage,
            'usage_percentage': (self.current_usage / self.daily_budget) * 100,
            'queries_executed': len(self.query_history),
            'last_query_time': self.query_history[-1]['timestamp'] if self.query_history else None
        }
    
    def reset_daily_usage(self):
        """重置日使用量"""
        self.current_usage = 0.0
        self.query_history.clear()
        logger.info("日使用量已重置")
