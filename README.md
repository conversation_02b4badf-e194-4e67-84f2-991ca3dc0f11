Tagtoo Tracking
===============

Ingest, mapping and analyze large volumes of user event/entity data from Ecommerces in real time.

Development
---------------

Download [Docker Desktop](https://www.docker.com/products/docker-desktop) for Mac or Windows.
[Docker Compose](https://docs.docker.com/compose) will be automatically installed.
On Linux, make sure you have the latest version of [Compose](https://docs.docker.com/compose/install/).

Deployment
---------------

We use [Terraform](https://www.terraform.io/) for IaC and change automation. The config sources stores in [deploy](deploy) directory.

The SDK `gcloud`, `terraform` are installed in the console container, attach it with command `make manage`:
```
$ make manage

/src # gcloud -v
Google Cloud SDK 314.0.0
bq 2.0.62
core 2020.10.09
gsutil 4.53

/src # terraform -v
Terraform v0.13.4
+ provider registry.terraform.io/hashicorp/google v3.44.0
+ provider registry.terraform.io/hashicorp/google-beta v3.44.0
```

An GCS bucket is configured as terraform state remote backend, it should be protected carefully.