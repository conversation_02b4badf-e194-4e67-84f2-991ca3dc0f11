import argparse

BACKENDS = [
    'facebook_capi_batch',
    'facebook_capi_singleton',
    'lta',
    'lta_facebook_capi_batch',
]


def parse_arguments():
    parser = argparse.ArgumentParser()
    parser.add_argument('backend', choices=BACKENDS, help='Subscriber backend name.')
    parser.add_argument('--version', choices=['v1'], help='Subscriber version.')
    parser.add_argument(
        '--max-messages',
        type=int,
        default=300,
        help='Max message to subscribe at a time.',
    )
    args = parser.parse_args()
    return args


def main():
    args = parse_arguments()
    if args.backend == 'facebook_capi_batch':
        from s2s.facebook_capi import subscribers as fb_subscribers

        version_classes_map = {
            'v1': fb_subscribers.FacebookCAPISubscriberBatchV1,
        }
        subscriber = version_classes_map[args.version]()
        subscriber(max_messages=args.max_messages)
    elif args.backend == 'facebook_capi_singleton':
        from s2s.facebook_capi import subscribers as fb_subscribers

        version_classes_map = {
            'v1': fb_subscribers.FacebookCAPISubscriberSingletonV1,
        }
        subscriber = version_classes_map[args.version]()
        subscriber(max_messages=args.max_messages)
    elif args.backend == 'lta':
        from s2s.LTA import subscribers as LTA_subscribers

        version_classes_map = {
            'v1': LTA_subscribers.LTAToFacebookCapi,
        }
        subscriber = version_classes_map[args.version]()
        subscriber(max_messages=args.max_messages)
    elif args.backend == 'lta_facebook_capi_batch':
        from s2s.facebook_capi import subscribers as fb_subscribers

        version_classes_map = {
            'v1': fb_subscribers.LTAFacebookCAPIBatchSubscriber,
        }
        subscriber = version_classes_map[args.version]()
        subscriber(max_messages=args.max_messages)


if __name__ == '__main__':
    main()
