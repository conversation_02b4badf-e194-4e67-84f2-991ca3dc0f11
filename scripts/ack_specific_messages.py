#!/usr/bin/env python3
"""
確認（ack）Pub/Sub 訂閱中的特定消息，用於解決卡住的隊列。

這個腳本可以：
1. 拉取訂閱中的消息並顯示
2. 根據條件過濾消息（如缺少 user_agent 字段）
3. 確認（ack）符合條件的消息，讓隊列繼續處理其他消息

用法：
    # 顯示訂閱中的消息
    python ack_specific_messages.py --subscription tagtoo-event-to-bigquery-v1-prod --show-only
    
    # 確認缺少 user_agent 字段的消息
    python ack_specific_messages.py --subscription tagtoo-event-to-bigquery-v1-prod --filter-missing-field user_agent
    
    # 確認特定 IP 地址的消息
    python ack_specific_messages.py --subscription tagtoo-event-to-bigquery-v1-prod --filter-field ip_address --filter-value "***********"
    
    # 乾跑模式（不執行實際的確認操作）
    python ack_specific_messages.py --subscription tagtoo-event-to-bigquery-v1-prod --filter-missing-field user_agent --dry-run
"""

import argparse
import json
import datetime
import os
from google.cloud import pubsub_v1

def parse_args():
    parser = argparse.ArgumentParser(description='Ack specific messages in a Pub/Sub subscription')
    parser.add_argument('--subscription', required=True, help='Subscription ID')
    parser.add_argument('--project', default='tagtoo-tracking', help='GCP project ID')
    parser.add_argument('--max-messages', type=int, default=100, 
                       help='Maximum number of messages to pull')
    parser.add_argument('--show-only', action='store_true', 
                       help='Only show messages, do not ack')
    parser.add_argument('--filter-missing-field', 
                       help='Filter messages missing this field')
    parser.add_argument('--filter-field', 
                       help='Filter messages by this field')
    parser.add_argument('--filter-value', 
                       help='Filter messages where field equals this value')
    parser.add_argument('--backup-dir', default='pubsub_backups', 
                       help='Directory to save backup messages')
    parser.add_argument('--backup-file', default='pubsub_backup_{timestamp}.json', 
                       help='File to save backup messages (use {timestamp} for current time)')
    parser.add_argument('--dry-run', action='store_true', 
                       help='Dry run mode (do not perform ack)')
    return parser.parse_args()

def pull_messages(project, subscription, max_messages):
    """從訂閱中拉取消息"""
    subscriber = pubsub_v1.SubscriberClient()
    subscription_path = subscriber.subscription_path(project, subscription)
    
    try:
        response = subscriber.pull(
            request={"subscription": subscription_path, "max_messages": max_messages},
            timeout=90.0
        )
        
        return response.received_messages
    except Exception as e:
        print(f"拉取消息時出錯：{e}")
        return []

def backup_messages(messages, backup_dir, backup_file):
    """備份消息到文件"""
    # 替換時間戳
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_file = backup_file.replace('{timestamp}', timestamp)
    
    # 確保備份目錄存在
    os.makedirs(backup_dir, exist_ok=True)
    backup_path = os.path.join(backup_dir, backup_file)
    
    # 準備備份數據
    backup_data = []
    for received_message in messages:
        message = received_message.message
        try:
            data = message.data.decode('utf-8')
            # 嘗試解析 JSON
            try:
                data_json = json.loads(data)
                data = data_json  # 如果成功解析，使用解析後的 JSON
            except json.JSONDecodeError:
                pass  # 如果不是 JSON，保持原始字符串
        except UnicodeDecodeError:
            data = f"<binary data, length: {len(message.data)}>"
            
        attributes = dict(message.attributes)
        backup_data.append({
            'message_id': message.message_id,
            'publish_time': message.publish_time.isoformat(),
            'data': data,
            'attributes': attributes,
            'ack_id': received_message.ack_id
        })
    
    # 寫入文件
    with open(backup_path, 'w') as f:
        json.dump(backup_data, f, indent=2)
        
    print(f"已備份 {len(backup_data)} 條消息到文件 {backup_path}")
    return backup_path

def filter_messages(messages, filter_missing_field=None, filter_field=None, filter_value=None):
    """根據條件過濾消息"""
    filtered_messages = []
    
    for received_message in messages:
        message = received_message.message
        try:
            data = json.loads(message.data.decode('utf-8'))
        except (json.JSONDecodeError, UnicodeDecodeError):
            # 如果不是有效的 JSON，跳過
            continue
        
        # 過濾缺少字段的消息
        if filter_missing_field and filter_missing_field not in data:
            filtered_messages.append(received_message)
            continue
            
        # 過濾特定字段值的消息
        if filter_field and filter_value and filter_field in data:
            if str(data[filter_field]) == filter_value:
                filtered_messages.append(received_message)
                continue
    
    return filtered_messages

def ack_messages(project, subscription, messages, dry_run=False):
    """確認（ack）消息"""
    if dry_run:
        print(f"乾跑模式，將確認 {len(messages)} 條消息（但實際不執行）")
        return
    
    if not messages:
        print("沒有消息需要確認")
        return
    
    subscriber = pubsub_v1.SubscriberClient()
    subscription_path = subscriber.subscription_path(project, subscription)
    
    try:
        ack_ids = [msg.ack_id for msg in messages]
        subscriber.acknowledge(
            request={"subscription": subscription_path, "ack_ids": ack_ids}
        )
        print(f"已成功確認 {len(ack_ids)} 條消息")
    except Exception as e:
        print(f"確認消息時出錯：{e}")

def display_messages(messages):
    """顯示消息內容"""
    for i, received_message in enumerate(messages):
        message = received_message.message
        try:
            data = json.loads(message.data.decode('utf-8'))
            data_str = json.dumps(data, indent=2)
            if len(data_str) > 500:
                data_str = data_str[:500] + "... (truncated)"
        except (json.JSONDecodeError, UnicodeDecodeError):
            data_str = f"<無法解析的數據，長度: {len(message.data)}>"
            
        attributes = dict(message.attributes)
        
        print(f"\n消息 {i+1}:")
        print(f"  ID: {message.message_id}")
        print(f"  發布時間: {message.publish_time}")
        print(f"  屬性: {attributes}")
        print(f"  數據: {data_str}")
    
    print(f"\n共 {len(messages)} 條消息")

def main():
    args = parse_args()
    
    # 拉取消息
    messages = pull_messages(args.project, args.subscription, args.max_messages)
    if not messages:
        print("沒有找到消息")
        return
    
    print(f"已拉取 {len(messages)} 條消息")
    
    # 備份消息
    backup_path = backup_messages(messages, args.backup_dir, args.backup_file)
    
    # 過濾消息
    if args.filter_missing_field or (args.filter_field and args.filter_value):
        filtered_messages = filter_messages(
            messages, 
            args.filter_missing_field, 
            args.filter_field, 
            args.filter_value
        )
        print(f"過濾後剩餘 {len(filtered_messages)} 條消息")
    else:
        filtered_messages = messages
    
    # 顯示消息
    if args.show_only or args.dry_run:
        display_messages(filtered_messages)
    
    # 確認消息
    if not args.show_only:
        ack_messages(args.project, args.subscription, filtered_messages, args.dry_run)
        
    print(f"操作完成。備份文件：{backup_path}")

if __name__ == '__main__':
    main()
