import logging
from typing import List

from pydantic import BaseModel, parse_obj_as

from app.config import settings
from app.enums import ChannelType
from app.services.requests import RequestsAPIService

logger = logging.getLogger(__file__)


class ClientModel(BaseModel):
    client_id: str
    name: str
    channel_type: ChannelType
    is_internal: bool


class AccessTokenModel(BaseModel):
    ec_id: int
    token: str
    scopes: List[str]
    client: ClientModel


class EventConfigService(RequestsAPIService):

    api_host = settings.EVENT_CONFIG_API_SERVER_HOST

    default_headers = {'Authorization': f'Token {settings.EVENT_CONFIG_API_TOKEN}'}

    async def get_configs(self, token=None):
        url = f'{self.api_host}/api/event_api/access_tokens/'
        config_data = self._send_request(
            url=url,
            method='GET',
        )
        validated_config_data = [
            data.dict() for data in parse_obj_as(List[AccessTokenModel], config_data)
        ]
        return validated_config_data
