from django.db import models
from django.utils.translation import ugettext_lazy as _
from model_utils.models import TimeStampedModel


class FacebookCAPIPixel(TimeStampedModel):
    ec_id = models.PositiveIntegerField(db_index=True)
    pixel_name = models.CharField(max_length=120, verbose_name=_('Pixel Name'))
    pixel_id = models.CharField(max_length=20, verbose_name=_('Pixel ID'))
    pixel_token = models.CharField(max_length=255, verbose_name=_('Pixel Token'))
    is_active = models.BooleanField(default=True, verbose_name=_('Active'))

    def __str__(self):
        return self.pixel_name
