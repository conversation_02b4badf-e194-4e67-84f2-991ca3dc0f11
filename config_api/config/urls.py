from django.urls import path
from django.contrib import admin
from django.conf import settings
from django.conf.urls.static import static
from django.conf.urls import include
from django.http import HttpResponse

from rest_framework.permissions import AllowAny
from drf_yasg.views import get_schema_view
from drf_yasg import openapi

from config.api import api

schema_view = get_schema_view(
    openapi.Info(
        title="Event Config API",
        default_version='v1',
        description="Share configs among event microservices.",
    ),
    url=settings.DOMAIN,
    public=False,
    permission_classes=(AllowAny,),
)


urlpatterns = [
    path('admin/', admin.site.urls, name='admin'),
    path('grappelli/', include('grappelli.urls')),
    path(
        'api/swagger/', schema_view.with_ui('swagger', cache_timeout=0), name='swagger'
    ),
    path('api/', include(api.urls)),
]


if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
