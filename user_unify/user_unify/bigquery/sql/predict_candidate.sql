WITH flatten_by_email AS (
  SELECT
    group_id,
    group_time,
    email
  FROM
    `$groups_table`,
    `$groups_table`.emails AS email
),
flatten_by_phone AS (
  SELECT
    group_id,
    group_time,
    phone
  FROM
    `$groups_table`,
    `$groups_table`.phones AS phone
),
flatten_by_username AS (
  SELECT
    group_id,
    group_time,
    username
  FROM
    `$groups_table`,
    `$groups_table`.usernames AS username
),
-- flatten_by_fbp AS (
--   SELECT
--     group_id,
--     group_time,
--     fbp
--   FROM
--     `$groups_table`,
--     `$groups_table`.fbps AS fbp
-- ),
flatten_by_gid AS (
  SELECT
    group_id,
    group_time,
    gid
  FROM
    `$groups_table`,
    `$groups_table`.gids AS gid
),
flatten_by_permanent AS (
  SELECT
    group_id,
    group_time,
    permanent
  FROM
    `$groups_table`,
    `$groups_table`.permanents AS permanent
),
entity_data AS (
  SELECT
    *
  FROM
    `$get_entity_function`('$start_date', '$end_date')
),
-- t_fbp AS (
--   SELECT
--     permanent,
--     entity_time,
--     group_id,
--   FROM
--     entity_data
--     LEFT OUTER JOIN flatten_by_fbp ffbp ON ffbp.fbp IS NOT NULL
--     AND ffbp.fbp = entity_data.fbp
-- ),
t_gid AS (
  SELECT
    permanent,
    entity_time,
    group_id,
  FROM
    entity_data
    LEFT OUTER JOIN flatten_by_gid fgid ON fgid.gid IS NOT NULL
    AND fgid.gid = entity_data.gid
),
t_em AS (
  SELECT
    permanent,
    entity_time,
    group_id,
  FROM
    entity_data
    LEFT OUTER JOIN flatten_by_email fem ON fem.email IS NOT NULL
    AND fem.email = entity_data.em
),
t_ph AS (
  SELECT
    permanent,
    entity_time,
    group_id,
  FROM
    entity_data
    LEFT OUTER JOIN flatten_by_phone fph ON fph.phone IS NOT NULL
    AND fph.phone = entity_data.ph
),
t_un AS (
  SELECT
    permanent,
    entity_time,
    group_id,
  FROM
    entity_data
    LEFT OUTER JOIN flatten_by_username f_un ON f_un.username IS NOT NULL
    AND f_un.username = entity_data.un
),
t_permanent AS (
  SELECT
    entity_data.permanent,
    entity_time,
    group_id,
  FROM
    entity_data
    LEFT OUTER JOIN flatten_by_permanent f_pmt ON f_pmt.permanent IS NOT NULL
    AND f_pmt.permanent = entity_data.permanent
),
res AS (
  -- SELECT
  --   *
  -- FROM
  --   t_fbp
  -- UNION
  -- ALL
  SELECT
    *
  FROM
    t_gid
  UNION
  ALL
  SELECT
    *
  FROM
    t_ph
  UNION
  ALL
  SELECT
    *
  FROM
    t_em
  UNION
  ALL
  SELECT
    *
  FROM
    t_un
  UNION
  ALL
  SELECT
    *
  FROM
    t_permanent
)
SELECT
  permanent,
  ARRAY_AGG(DISTINCT group_id IGNORE NULLS) group_ids
FROM
  res
GROUP BY
  1
HAVING
  ARRAY_LENGTH(group_ids) > 1;