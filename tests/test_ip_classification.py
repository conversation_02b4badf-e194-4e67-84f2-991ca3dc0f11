#!/usr/bin/env python3
"""
測試 IP 地址分類功能。
測試 API 的 get_ip_from_request 函數和 subscriber 的 IP 處理邏輯。
"""

import unittest
from unittest.mock import MagicMock, patch
import ipaddress
import sys
import os

# 添加專案根目錄到 Python 路徑，以便導入模組
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 模擬 FastAPI 的 Request 類
class MockRequest:
    def __init__(self, headers=None, client_host=None):
        self.headers = headers or {}
        self.client = MagicMock()
        self.client.host = client_host or '127.0.0.1'

class TestIPClassification(unittest.TestCase):
    def test_api_get_ip_from_request(self):
        """測試 API 的 get_ip_from_request 函數"""
        # 導入 API 的 utils 模組
        try:
            from api.app.utils import get_ip_from_request
        except ImportError:
            # 如果無法導入，則跳過測試
            self.skipTest("無法導入 API 模組，跳過測試")
            return
        
        # 測試 IPv4 地址
        request = MockRequest(headers={'x-forwarded-for': '***********'})
        result = get_ip_from_request(request)
        self.assertEqual(result, {'ipv4': '***********', 'ipv6': None})
        
        # 測試 IPv6 地址
        request = MockRequest(headers={'x-forwarded-for': '2001:db8::1'})
        result = get_ip_from_request(request)
        self.assertEqual(result, {'ipv4': None, 'ipv6': '2001:db8::1'})
        
        # 測試無效 IP 地址
        request = MockRequest(headers={'x-forwarded-for': 'invalid-ip'})
        result = get_ip_from_request(request)
        self.assertEqual(result, {'ipv4': None, 'ipv6': None})
        
        # 測試多個 IP 地址（代理鏈）
        request = MockRequest(headers={'x-forwarded-for': '***********, ********'})
        result = get_ip_from_request(request)
        self.assertEqual(result, {'ipv4': '***********', 'ipv6': None})
        
        # 測試沒有 x-forwarded-for 頭
        request = MockRequest(client_host='***********')
        result = get_ip_from_request(request)
        self.assertEqual(result, {'ipv4': '***********', 'ipv6': None})

    def test_subscriber_ip_processing(self):
        """測試 subscriber 的 IP 處理邏輯"""
        # 導入 subscriber 模組
        try:
            from bq_writer.bq_writer.subscribers import TagtooEventSubscriberV1
        except ImportError:
            # 如果無法導入，則跳過測試
            self.skipTest("無法導入 subscriber 模組，跳過測試")
            return
        
        # 創建 subscriber 實例
        subscriber = TagtooEventSubscriberV1()
        
        # 模擬 callback_function 的參數
        message = MagicMock()
        
        # 模擬 parse 方法的返回值
        subscriber.parse = MagicMock(return_value={
            'data': {'ip_address': '***********'},
            'version': 'v1',
            'ec_id': 123
        })
        
        # 模擬 is_internal_ip 和 query_location 函數
        with patch('bq_writer.bq_writer.subscribers.is_internal_ip', return_value=False), \
             patch('bq_writer.bq_writer.subscribers.query_location') as mock_query_location:
            
            # 模擬 query_location 返回值
            mock_location = MagicMock()
            mock_location.country_code = 'TW'
            mock_location.region_name = 'Taipei'
            mock_location.city_name = 'Taipei'
            mock_location.latitude = 25.0
            mock_location.longitude = 121.0
            mock_location.zip_code = '100'
            mock_query_location.return_value = mock_location
            
            # 模擬 bigquery.Client
            subscriber.client = MagicMock()
            subscriber.client.insert_rows_json.return_value = []
            
            # 測試字串形式的 IPv4 地址
            subscriber.parse.return_value = {
                'data': {'ip_address': '***********'},
                'version': 'v1',
                'ec_id': 123
            }
            subscriber.callback_function(message)
            
            # 檢查 IP 地址是否被正確分類
            data = subscriber.parse.return_value['data']
            self.assertEqual(data['ip_address'], {'ipv4': '***********', 'ipv6': None})
            
            # 測試字串形式的 IPv6 地址
            subscriber.parse.return_value = {
                'data': {'ip_address': '2001:db8::1'},
                'version': 'v1',
                'ec_id': 123
            }
            subscriber.callback_function(message)
            
            # 檢查 IP 地址是否被正確分類
            data = subscriber.parse.return_value['data']
            self.assertEqual(data['ip_address'], {'ipv4': None, 'ipv6': '2001:db8::1'})
            
            # 測試字典形式的 IP 地址（API 已經分類）
            subscriber.parse.return_value = {
                'data': {'ip_address': {'ipv4': '***********', 'ipv6': None}},
                'version': 'v1',
                'ec_id': 123
            }
            subscriber.callback_function(message)
            
            # 檢查 IP 地址是否保持不變
            data = subscriber.parse.return_value['data']
            self.assertEqual(data['ip_address'], {'ipv4': '***********', 'ipv6': None})
            
            # 測試同時有 IPv4 和 IPv6 的情況（應該優先使用 IPv4）
            subscriber.parse.return_value = {
                'data': {'ip_address': {'ipv4': '***********', 'ipv6': '2001:db8::1'}},
                'version': 'v1',
                'ec_id': 123
            }
            subscriber.callback_function(message)
            
            # 檢查是否使用 IPv4 進行地理位置解析
            mock_query_location.assert_called_with('***********')

if __name__ == '__main__':
    unittest.main()
