import logging
from typing import Dict
from urllib.parse import urlencode

import requests

from .exceptions import RequestsServiceError

logger = logging.getLogger(__file__)


class RequestsAPIService(object):

    api_host = ''
    default_headers = {}
    _session = requests.Session()

    @property
    def session(self):
        if self._session is None:
            self._session = requests.Session()
        return self._session

    def _construct_headers(
        self,
        headers: Dict = None,
    ):
        headers = headers or {}
        _headers = self.default_headers
        _headers.update(headers)
        return _headers

    def _handle_response(self, url, response):
        if response.status_code >= 400 and response.status_code < 500:
            raise RequestsServiceError(
                url=url,
                status_code=response.status_code,
                detail=response.text,
            )
        elif response.status_code == 200:
            return response.json()

    def _send_request(
        self,
        url: str,
        method: str,
        headers: Dict = None,
        params: Dict = None,
        safe: str = '',
        data=None,
    ):
        if method == 'GET':
            if params:
                query_string = urlencode(params, safe=safe)
                url = f'{url}?{query_string}'
            response = self.session.get(
                url=url, headers=self._construct_headers(headers)
            )
        elif method == 'POST':
            response = self.session.post(
                url=url,
                json=data,
                headers=self._construct_headers(headers),
            )
        return self._handle_response(url, response)
