#!/usr/bin/env bash

set -o errexit
set -o pipefail

WORKERS=3
TIMEOUT=60
WORKER_CONNECTIONS=300

while getopts ":hw:t:c:" opt; do
  case "${opt}" in
    h) echo "-w: gevent worker number"
       echo "-t: timeout"
       exit
      ;;
    w) WORKERS=${OPTARG}
      ;;
    t) TIMEOUT=${OPTARG}
      ;;
    c) WORKER_CONNECTIONS=${OPTARG}
      ;;
    \? ) echo "Usage: cmd [-w] workers [-t] timeout [-c] worker_connections"
      exit
      ;;
  esac
done

python manage.py migrate --database=default

# python manage.py collectstatic --noinput --verbosity 0
gunicorn config.wsgi \
-k gevent \
-w ${WORKERS} \
-b 0.0.0.0:8000 \
-t ${TIMEOUT} \
--worker-connections=${WORKER_CONNECTIONS} \
--log-level=debug \
--chdir=/app
