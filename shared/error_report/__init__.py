import abc
import logging
import traceback

from google.api_core.exceptions import ResourceExhausted
from google.cloud.error_reporting import Client

logger = logging.getLogger(__file__)


class ErrorReportingService(metaclass=abc.ABCMeta):
    @property
    @abc.abstractmethod
    def project(self):
        pass

    @property
    @abc.abstractmethod
    def credentials(self):
        pass

    @property
    @abc.abstractmethod
    def service(self):
        pass

    def __init__(self, version):
        self.client = Client(
            project=self.project,
            credentials=self.credentials,
            service=self.service,
            version=version,
        )

    def report(self, msg: str) -> None:
        try:
            self.client.report(msg)
        except ResourceExhausted as err:
            logger.error(f'Error report ResourceExhausted error with {msg=}')
            logger.error(err)
        except Exception as err:
            logger.error(f'Error report {err.__class__.__name__} with {msg=}')
            self.report_exception()

    def report_exception(self, *args, **kwargs) -> None:
        prv_traceback = traceback.format_exc()
        try:
            self.client.report_exception(*args, **kwargs)
        except ResourceExhausted as err:
            logger.error(
                f'Error report ResourceExhausted error with traceback={prv_traceback}'
            )
            logger.error(err)
        except Exception:
            try:
                self.client.report_exception()
            except Exception as err:
                logging.error(
                    f'Error report {err.__class__.__name__} with traceback={traceback.format_exc()}'
                )


# Mock error report service
class MockErrorReportingService:
    def __init__(self, *args, **kwargs) -> None:
        pass

    def report(self, msg: str) -> None:
        print(msg)

    def report_exception(self) -> None:
        print(traceback.format_exc())
