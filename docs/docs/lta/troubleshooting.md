# LTA 數據處理故障排除指南

本文檔提供了 LTA 數據處理系統常見問題的故障排除步驟和最佳實踐。

## 常見問題

### 1. Job 執行時間異常長

**症狀**：
- 相似大小的數據文件處理時間差異大（如 59 秒 vs 401 秒）
- 某些 Job 執行時間明顯長於平均水平

**可能原因**：
- 節點資源競爭
- 節點負載不均衡
- Facebook API 響應慢
- 數據文件複雜度高

**排查步驟**：

1. **檢查節點資源使用率**：
   ```bash
   # 查看節點資源使用情況
   kubectl describe node <node-name>
   
   # 查看節點上運行的 Pod
   kubectl get pods -o wide | grep <node-name>
   ```

2. **檢查 Job 日誌**：
   ```bash
   # 查看 Job 日誌
   kubectl logs job/load-lta-data-<timestamp>
   
   # 查看處理時間
   kubectl logs job/load-lta-data-<timestamp> | grep "Successfully publish"
   ```

3. **檢查調度策略是否生效**：
   ```bash
   # 查看 Job 的詳細信息
   kubectl describe job load-lta-data-<timestamp>
   
   # 檢查 Pod 的調度信息
   kubectl describe pod <pod-name>
   ```

**解決方案**：
- 確保 Pod 反親和性和拓撲分佈約束正確配置
- 調整資源請求和限制
- 考慮增加節點數量或調整節點池配置

### 2. Job 創建失敗

**症狀**：
- `s2s-lta-subscriber` 無法創建 Job
- 日誌中出現權限錯誤

**可能原因**：
- RBAC 權限不足
- 資源配額限制
- 節點資源不足

**排查步驟**：

1. **檢查 RBAC 權限**：
   ```bash
   # 查看 ServiceAccount 權限
   kubectl describe role event-s2s
   kubectl describe rolebinding event-s2s
   ```

2. **檢查資源配額**：
   ```bash
   # 查看命名空間資源配額
   kubectl describe resourcequota
   
   # 查看命名空間限制範圍
   kubectl describe limitrange
   ```

3. **檢查節點資源**：
   ```bash
   # 查看節點資源使用情況
   kubectl top nodes
   ```

**解決方案**：
- 確保 ServiceAccount 有創建 Job 的權限
- 調整資源配額或請求更少的資源
- 增加節點數量或調整節點池配置

### 3. 數據處理失敗

**症狀**：
- Job 成功創建但處理失敗
- 日誌中出現錯誤消息

**可能原因**：
- AVRO 文件格式錯誤
- GCS 訪問權限問題
- Facebook API 錯誤
- 內存不足

**排查步驟**：

1. **檢查 AVRO 文件**：
   ```bash
   # 使用 gsutil 下載文件
   gsutil cp gs://tagtoo-ml-workflow/topic10/Audience_labels/YYYYMMDD/XXXXXX.avro .
   
   # 使用 avro-tools 檢查文件
   java -jar avro-tools-1.10.2.jar tojson XXXXXX.avro
   ```

2. **檢查 GCS 訪問權限**：
   ```bash
   # 檢查 ServiceAccount 權限
   gcloud iam service-accounts get-iam-policy <service-account>
   ```

3. **檢查 Facebook API 錯誤**：
   ```bash
   # 查看詳細日誌
   kubectl logs job/load-lta-data-<timestamp> | grep "error"
   ```

**解決方案**：
- 修復 AVRO 文件格式
- 確保 ServiceAccount 有適當的 GCS 訪問權限
- 檢查 Facebook API 憑證和參數
- 增加內存請求和限制

### 4. Pub/Sub 消息處理問題

**症狀**：
- 消息積壓在 Pub/Sub 訂閱中
- `s2s-lta-subscriber` 無法處理消息

**可能原因**：
- 訂閱配置錯誤
- 處理超時
- 權限問題
- 代碼錯誤

**排查步驟**：

1. **檢查訂閱狀態**：
   ```bash
   # 查看訂閱詳情
   gcloud pubsub subscriptions describe lta-v1-prod
   
   # 查看未確認消息數量
   gcloud pubsub subscriptions describe lta-v1-prod --format="value(unackedMessages)"
   ```

2. **檢查 `s2s-lta-subscriber` 日誌**：
   ```bash
   # 查看訂閱者日誌
   kubectl logs deployment/s2s-lta-subscriber -c lta-facebook-capi
   ```

3. **檢查權限**：
   ```bash
   # 檢查 ServiceAccount 權限
   gcloud projects get-iam-policy tagtoo-tracking
   ```

**解決方案**：
- 調整訂閱配置（如確認截止時間）
- 確保 ServiceAccount 有適當的 Pub/Sub 權限
- 修復代碼錯誤
- 考慮使用死信主題處理無法處理的消息

## 性能優化

### 監控關鍵指標

為了及時發現和解決問題，應該監控以下關鍵指標：

1. **Job 執行時間**：
   ```bash
   # 查看最近 Job 的執行時間
   kubectl get jobs -l job-type=load-lta-data --sort-by=.metadata.creationTimestamp
   ```

2. **節點資源使用率**：
   ```bash
   # 查看節點 CPU 和內存使用率
   kubectl top nodes
   ```

3. **Pod 資源使用率**：
   ```bash
   # 查看 Pod CPU 和內存使用率
   kubectl top pods -l job-type=load-lta-data
   ```

4. **Pub/Sub 消息積壓**：
   ```bash
   # 查看未確認消息數量
   gcloud pubsub subscriptions describe lta-v1-prod --format="value(unackedMessages)"
   ```

### 性能調優建議

1. **資源配置**：
   - 根據實際負載調整 CPU 和內存請求
   - 確保 CPU 限制足夠處理峰值負載

2. **批處理策略**：
   - 優化批次大小，避免過大或過小
   - 考慮實施動態批處理

3. **調度策略**：
   - 使用 Pod 反親和性和拓撲分佈約束確保負載均衡
   - 設置適當的優先級確保關鍵任務優先執行

4. **節點池配置**：
   - 使用適當的機器類型（如 e2-standard-2）
   - 配置自動擴展以應對負載變化

## 常用命令參考

### Kubernetes 命令

```bash
# 查看所有 Job
kubectl get jobs

# 查看特定 Job 詳情
kubectl describe job load-lta-data-<timestamp>

# 查看 Job 日誌
kubectl logs job/load-lta-data-<timestamp>

# 查看節點資源使用情況
kubectl top nodes

# 查看 Pod 資源使用情況
kubectl top pods
```

### Pub/Sub 命令

```bash
# 查看主題列表
gcloud pubsub topics list

# 查看訂閱列表
gcloud pubsub subscriptions list

# 查看訂閱詳情
gcloud pubsub subscriptions describe lta-v1-prod

# 清除積壓消息
gcloud pubsub subscriptions seek lta-v1-prod --time=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
```

### GCS 命令

```bash
# 列出 AVRO 文件
gsutil ls gs://tagtoo-ml-workflow/topic10/Audience_labels/YYYYMMDD/

# 查看文件大小
gsutil du -h gs://tagtoo-ml-workflow/topic10/Audience_labels/YYYYMMDD/XXXXXX.avro

# 下載文件
gsutil cp gs://tagtoo-ml-workflow/topic10/Audience_labels/YYYYMMDD/XXXXXX.avro .
```

## 參考資料

- [Kubernetes 故障排除](https://kubernetes.io/docs/tasks/debug/)
- [Pub/Sub 故障排除](https://cloud.google.com/pubsub/docs/troubleshooting)
- [GCS 故障排除](https://cloud.google.com/storage/docs/troubleshooting)
