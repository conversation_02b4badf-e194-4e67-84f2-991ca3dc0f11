from fastapi import status

import orj<PERSON>
import pytest
from pytest_cases import parametrize_with_cases

from app.config import settings

from .cases.case_test_api import (
    EventAPIPartialValidCases,
    EventAPIResponseNGCases,
    EventAPIResponseOKCases,
)


class TestIndex(object):
    def test_index_ok(
        self,
        api_client: pytest.fixture,
    ):
        response = api_client.get('/')
        assert response.status_code == status.HTTP_200_OK


class TestEventAPI(object):
    @parametrize_with_cases(
        'data, expected',
        cases=EventAPIResponseOKCases,
    )
    def test_event_api_ok(
        self,
        api_client: pytest.fixture,
        mocker: pytest.fixture,
        error_reporting_service_mock: pytest.fixture,
        pubsub_subscriber: pytest.fixture,
        data,
        expected,
    ):
        response = api_client.post(
            '/event/v1',
            json=data,
        )
        assert response.status_code == status.HTTP_200_OK

        response = pubsub_subscriber.pull_message('tagtoo-event')
        message = response.received_messages[0].message
        received_data = orjson.loads(message.data)
        assert received_data == expected

    @parametrize_with_cases(
        'data, expected_status_code',
        cases=EventAPIResponseNGCases,
    )
    def test_event_api_ng(
        self,
        api_client: pytest.fixture,
        error_reporting_service_mock: pytest.fixture,
        data,
        expected_status_code,
    ):
        response = api_client.post(
            '/event/v1',
            json=data,
        )
        assert response.status_code == expected_status_code
        error_reporting_service_mock.assert_called()

    @parametrize_with_cases(
        'data, expected',
        cases=EventAPIPartialValidCases,
    )
    def test_event_api_partial_valid(
        self,
        api_client: pytest.fixture,
        error_reporting_service_mock: pytest.fixture,
        pubsub_subscriber: pytest.fixture,
        data,
        expected,
    ):
        response = api_client.post(
            '/event/v1',
            json=data,
        )
        test_topics = ['facebook-capi', 'tagtoo-event']

        assert response.status_code == status.HTTP_200_OK
        error_reporting_service_mock.assert_called()
        for topic in test_topics:
            response = pubsub_subscriber.pull_message(topic)
            try:
                message = response.received_messages[0].message
                received_data = orjson.loads(message.data)
            except IndexError:
                received_data = None
            assert received_data == expected[topic], f'Test failed on topic: {topic}'


class TestPermanent(object):
    def test_get_permanent_ok(
        self,
        api_client,
    ):
        response = api_client.get(
            '/permanent?fp=abcdef0123456789abcdef0123456789',
            headers={settings.API_TOKEN_HEADER: 'test_permanent'},
        )
        assert response.status_code == status.HTTP_200_OK

    def test_get_permanent_ng_no_query_string(self, api_client):
        response = api_client.get(
            '/permanent', headers={settings.API_TOKEN_HEADER: 'test_permanent'}
        )
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_get_permanent_authenticate_ng_no_token(
        self,
        api_client,
    ):
        response = api_client.get('/permanent?fp=abcdef0123456789abcdef0123456789')
        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_get_permanent_authenticate_ng_invalid_token(
        self,
        api_client,
    ):
        response = api_client.get(
            '/permanent?fp=abcdef0123456789abcdef0123456789',
            headers={settings.API_TOKEN_HEADER: 'invalid_token'},
        )
        assert response.status_code == status.HTTP_403_FORBIDDEN

    @pytest.mark.parametrize(
        'fp, expected',
        [
            # Correct Input
            ('abcdef0123456789abcdef0123456789', status.HTTP_200_OK),
            # Length is not 32
            ('abcdef', status.HTTP_422_UNPROCESSABLE_ENTITY),
            # Contain non-hex charactors
            ('zzzzzz0123456789abcdef0123456789', status.HTTP_422_UNPROCESSABLE_ENTITY),
        ],
    )
    def test_get_permanent_authenticate_ng_invalid_fp(
        self, api_client: pytest.fixture, fp, expected
    ):
        response = api_client.get(
            f'/permanent?fp={fp}',  # Length is not 32
            headers={settings.API_TOKEN_HEADER: 'test_permanent'},
        )
        assert response.status_code == expected
