"""
最佳化的 integrated_event 轉換器

實施追蹤友善的 raw_json 策略，支援快速除錯和成本控制。
基於 legacy-event-sync 分析，確保完全一致的轉換邏輯。

版本: v2.1 (tracking_id 核心策略)
日期: 2025-08-18
"""

import hashlib
import time
import re
from datetime import datetime
from typing import Dict, Any, Optional
from urllib.parse import urlparse
import logging

logger = logging.getLogger(__name__)


class OptimizedIntegratedEventTransformer:
    """最佳化的 integrated_event 轉換器"""

    PARTNER_SOURCE = "legacy-tagtoo-event"  # ✅ 確認的正確值
    EXCLUDED_EVENT_TYPES = ["focus"]        # ✅ 事件過濾配置

    def __init__(self):
        """初始化轉換器"""
        self.schema_version = "v1.1"
        logger.info(f"初始化 OptimizedIntegratedEventTransformer, schema: {self.schema_version}")

    def transform(self, tagtoo_data: dict, message_id: str) -> dict:
        """
        轉換 tagtoo_event 格式到 integrated_event 格式

        Args:
            tagtoo_data: 來自 tagtoo_event 的原始資料
            message_id: Pub/Sub 訊息 ID

        Returns:
            integrated_event 格式的資料
        """
        try:
            event_details = tagtoo_data.get("event", {})
            user_details = tagtoo_data.get("user", {})
            location_details = tagtoo_data.get("location", {})

            # 生成追蹤 ID
            tracking_id = self._generate_tracking_id(tagtoo_data, message_id)

            integrated_data = {
                "permanent": self._safe_str_convert(tagtoo_data.get("permanent")),
                "ec_id": self._safe_int_convert(tagtoo_data.get("ec_id")),
                "partner_source": self.PARTNER_SOURCE,
                "event_time": tagtoo_data.get("event_time"),
                "create_time": datetime.utcnow().isoformat(),
                "link": self._safe_str_convert(tagtoo_data.get("link")),
                "event": event_details.get("name"),
                "value": event_details.get("value"),
                "currency": event_details.get("currency"),
                "order_id": event_details.get("custom_data", {}).get("order_id"),
                "items": event_details.get("items", []),
                "user": {
                    "em": user_details.get("em"),
                    "ph": user_details.get("ph"),
                } if user_details else None,
                "partner_id": None,
                "page": None,
                "location": location_details if location_details else None,
                "raw_json": self._build_trackable_raw_json(tagtoo_data, message_id, tracking_id),
            }

            logger.debug(f"成功轉換事件，tracking_id: {tracking_id}")
            return integrated_data

        except Exception as e:
            logger.error(f"轉換失敗: {e}, tagtoo_data: {tagtoo_data}")
            raise

    def should_write_to_integrated(self, event_name: str) -> bool:
        """
        判斷事件是否應該寫入 integrated_event

        Args:
            event_name: 事件名稱

        Returns:
            是否應該寫入
        """
        should_write = event_name not in self.EXCLUDED_EVENT_TYPES
        if not should_write:
            logger.debug(f"跳過事件 {event_name}，在排除清單中")
        return should_write

    def _generate_tracking_id(self, tagtoo_data: dict, message_id: str) -> str:
        """
        生成唯一且可查詢的追蹤 ID

        格式: {ec_id}_{permanent_hash}_{message_hash}_{timestamp}
        長度控制在 50 字元以內

        Args:
            tagtoo_data: 原始資料
            message_id: 訊息 ID

        Returns:
            唯一追蹤 ID
        """
        try:
            components = [
                str(tagtoo_data.get("ec_id", 0)),
                self._hash_string(tagtoo_data.get("permanent", ""), 8),
                self._hash_string(message_id, 8),
                str(int(time.time() * 1000))[-6:]  # 時間戳後 6 位
            ]
            tracking_id = "_".join(components)

            # 確保長度不超過 50 字元
            if len(tracking_id) > 50:
                tracking_id = tracking_id[:50]

            return tracking_id

        except Exception as e:
            logger.error(f"生成 tracking_id 失敗: {e}")
            # 降級策略：使用時間戳
            return f"fallback_{int(time.time() * 1000)}"

    def _build_trackable_raw_json(self, tagtoo_data: dict, message_id: str, tracking_id: str) -> dict:
        """
        建構可追蹤的最小化 raw_json

        目標大小: ~200B (vs 原方案 ~500B)
        包含追蹤資訊和最小化除錯資訊

        Args:
            tagtoo_data: 原始資料
            message_id: 訊息 ID
            tracking_id: 追蹤 ID

        Returns:
            最佳化的 raw_json
        """
        return {
            "tracking": {
                "id": tracking_id,
                "message_id": message_id,
                "source_ts": tagtoo_data.get("event_time"),
                "process_ts": datetime.utcnow().isoformat(),
                "version": self.schema_version
            },
            "debug": {
                # 極度最小化的除錯資訊
                "session_hash": self._hash_session(tagtoo_data.get("session_id")),
                "ua_signature": self._extract_ua_signature(tagtoo_data.get("user_agent")),
                "ref_domain": self._extract_domain(tagtoo_data.get("referrer")),
                "geo_code": tagtoo_data.get("location", {}).get("country_code")
            }
        }

    def _hash_session(self, session_id: str) -> Optional[str]:
        """
        會話 ID 雜湊化，節省空間

        Args:
            session_id: 原始會話 ID

        Returns:
            8位雜湊值或 None
        """
        if not session_id:
            return None
        return self._hash_string(session_id, 8)

    def _extract_ua_signature(self, user_agent: str) -> Optional[str]:
        """
        提取 User Agent 特徵簽名，而非完整字串

        Args:
            user_agent: 原始 User Agent

        Returns:
            簡化的特徵簽名，如 "Chrome_Windows"
        """
        if not user_agent:
            return None

        try:
            # 提取瀏覽器資訊
            browser_match = re.search(r'(Chrome|Firefox|Safari|Edge)/[\d.]+', user_agent)
            browser = browser_match.group(1) if browser_match else 'Unk'

            # 提取作業系統資訊
            os_match = re.search(r'(Windows|Mac|Linux|Android|iOS)', user_agent)
            os = os_match.group(1) if os_match else 'Unk'

            return f"{browser}_{os}"

        except Exception as e:
            logger.warning(f"解析 User Agent 失敗: {e}")
            return "Parse_Error"

    def _extract_domain(self, url: str) -> Optional[str]:
        """
        提取網域名稱，限制長度

        Args:
            url: 原始 URL

        Returns:
            網域名稱，最長 20 字元
        """
        if not url:
            return None

        try:
            domain = urlparse(url).netloc
            return domain[:20] if domain else None
        except Exception as e:
            logger.warning(f"解析網域失敗: {e}")
            return None

    def _hash_string(self, text: str, length: int = 8) -> str:
        """
        字串雜湊化

        Args:
            text: 原始字串
            length: 雜湊長度

        Returns:
            指定長度的雜湊值
        """
        if not text:
            return "0" * length

        return hashlib.md5(text.encode()).hexdigest()[:length]

    def _safe_str_convert(self, value: Any) -> Optional[str]:
        """安全的字串轉換，處理各種類型"""
        if value is None:
            return None
        return str(value)

    def _safe_int_convert(self, value: Any) -> Optional[int]:
        """安全的整數轉換"""
        if value is None:
            return None
        try:
            if isinstance(value, str):
                value = value.strip()
                if not value:
                    return None
            return int(value)
        except (ValueError, TypeError):
            logger.warning(f"無法轉換為整數: {value}, 類型: {type(value)}")
            return None

    def get_tracking_id_from_data(self, integrated_data: dict) -> Optional[str]:
        """
        從 integrated_event 資料中提取 tracking_id

        Args:
            integrated_data: integrated_event 資料

        Returns:
            tracking_id 或 None
        """
        try:
            return integrated_data.get("raw_json", {}).get("tracking", {}).get("id")
        except Exception:
            return None
