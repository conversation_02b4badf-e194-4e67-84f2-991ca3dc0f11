version: '3.7'

services:
  s2s:
    &s2s
    container_name: s2s
    image: ad_track_s2s
    build:
      context: .
      dockerfile: s2s/Dockerfile
    entrypoint: ./scripts/entrypoint.sh
    volumes:
      - ./s2s:/app
      - ./gcloud/sa/event-s2s-vm.json:/credentials/event-s2s-vm.json
    environment:
      - GOOGLE_APPLICATION_CREDENTIALS=/credentials/event-s2s-vm.json
      - PUBSUB_EMULATOR_HOST=s2s-pubsub:8681
    restart: always
    env_file: ./s2s/.env
    command:
      - tail
      - -F
      - /dev/null
    networks:
      - s2s
    depends_on:
      - s2s_broker
      - s2s_db
      - s2s_pubsub

  capi_subscriber_batch:
    <<: *s2s
    container_name: capi-sub-batch
    command:
      [
        "python",
        "subscribe.py",
        "facebook_capi_batch",
        "--version=v1",
        "--max-messages=100"
      ]
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "ps -eocmd | grep '^python subscribe.py' && exit 0 || exit 1"
        ]
      interval: 5s
      start_period: 5s

  capi_subscriber_singleton:
    <<: *s2s
    container_name: capi-sub-singleton
    command:
      [
        "python",
        "subscribe.py",
        "facebook_capi_singleton",
        "--version=v1",
        "--max-messages=100"
      ]
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "ps -eocmd | grep '^python subscribe.py' && exit 0 || exit 1"
        ]
      interval: 5s
      start_period: 5s

  lta_subscriber:
    <<: *s2s
    container_name: lta-sub
    command:
      [
        "python",
        "subscribe.py",
        "lta",
        "--version=v1",
        "--max-messages=100"
      ]
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "ps -eocmd | grep '^python subscribe.py' && exit 0 || exit 1"
        ]
      interval: 5s
      start_period: 5s

  lta_facebook_capi_batch_subcriber:
    <<: *s2s
    container_name: lta-capi-batch-sub
    command:
      [
        "python",
        "subscribe.py",
        "lta_facebook_capi_batch",
        "--version=v1",
        "--max-messages=100"
      ]
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "ps -eocmd | grep '^python subscribe.py' && exit 0 || exit 1"
        ]
      interval: 5s
      start_period: 5s


  capi_failed_batch_handler:
    <<: *s2s
    container_name: capi-failed-batch-handler
    command: [ "python", "worker.py", "failed_batch_handler" ]
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "ps -eocmd | grep '^python worker.py' && exit 0 || exit 1"
        ]
      interval: 5s
      start_period: 5s

  s2s_db:
    container_name: s2s-db
    image: redis:6-alpine
    networks:
      - s2s

  s2s_broker:
    container_name: s2s-broker
    image: rabbitmq:3.8-alpine
    networks:
      - s2s

  s2s_pubsub:
    container_name: s2s-pubsub
    image: messagebird/gcloud-pubsub-emulator
    environment:
      - PUBSUB_PROJECT1=tagtoo-tracking,capi-compressed:batch,capi-decompressed:singleton,lta:lta,lta-fb:lta-fb
    networks:
      - s2s

networks:
  s2s:
    driver: bridge
    external: true
    name: config
