import uuid

from fastapi.testclient import TestClient

import or<PERSON><PERSON>
import pytest
import redis
from faker import Faker
from google.cloud import pubsub

from app.config import settings
from app.enums import ChannelType, EventAPIScope
from app.main import app

from .utils import Subscriber

faker = Faker()


@pytest.fixture(scope='session')
def api_client():
    with TestClient(app) as client:
        yield client


@pytest.fixture(scope='session')
def redis_db():
    return redis.Redis(host=f'{settings.REDIS_HOST}', port=f'{settings.REDIS_PORT}')


@pytest.fixture(scope='function', autouse=True)
def error_reporting_service_mock(
    mocker: pytest.fixture,
):
    from app.services.error_reporting import ErrorReportingClient

    return mocker.patch.object(
        ErrorReportingClient,
        'report',
        autospec=True,
    )


@pytest.fixture(scope='function', autouse=True)
def pubsub_subscriber():
    topic_names = (
        'facebook-capi',
        'tagtoo-event',
    )

    with pubsub.SubscriberClient() as client:
        subscriber = Subscriber(client)
        for topic_name in topic_names:
            subscription_name = f'{topic_name}-{uuid.uuid4().hex}'
            subscriber.add_subscription(topic_name, subscription_name)
        yield subscriber
        for topic_name in topic_names:
            subscriber.delete_subscription(topic_name)


@pytest.fixture(scope='session', autouse=True)
def event_api_tokens(
    redis_db: pytest.fixture,
):
    from app.utils import construct_key

    configs = {
        'frontend_scp_all': {
            'ec_id': 100,
            'token': 'test_fe-all',
            'client': {
                'name': 'muffet',
                'channel_type': ChannelType.FRONTEND,
                'is_internal': True,
            },
            'scopes': [EventAPIScope.FACEBOOK_CAPI, EventAPIScope.TAGTOO_EVENT],
        },
        'frontend_scp_fbcapi': {
            'ec_id': 100,
            'token': 'test_fe-fbcapi',
            'client': {
                'name': 'muffet',
                'channel_type': ChannelType.FRONTEND,
                'is_internal': True,
            },
            'scopes': [EventAPIScope.FACEBOOK_CAPI],
        },
        'frontend_scp_tagtoo': {
            'ec_id': 100,
            'token': 'test_fe-tagtoo',
            'client': {
                'name': 'muffet',
                'channel_type': ChannelType.FRONTEND,
                'is_internal': True,
            },
            'scopes': [EventAPIScope.TAGTOO_EVENT],
        },
        'backend_scp_all': {
            'ec_id': 153,
            'token': 'test_be-all',
            'client': {
                'name': 's2s',
                'channel_type': ChannelType.BACKEND,
                'is_internal': False,
            },
            'scopes': [EventAPIScope.FACEBOOK_CAPI, EventAPIScope.TAGTOO_EVENT],
        },
        'backend_scp_fbcapi': {
            'ec_id': 153,
            'token': 'test_be-fbcapi',
            'client': {
                'name': 's2s',
                'channel_type': ChannelType.BACKEND,
                'is_internal': False,
            },
            'scopes': [EventAPIScope.FACEBOOK_CAPI],
        },
        'backend_scp_tagtoo': {
            'ec_id': 153,
            'token': 'test_be-tagtoo',
            'client': {
                'name': 's2s',
                'channel_type': ChannelType.BACKEND,
                'is_internal': False,
            },
            'scopes': [EventAPIScope.TAGTOO_EVENT],
        },
        'permanent': {
            'ec_id': 255,
            'token': 'test_permanent',
            'client': {
                'name': 'muffet',
                'channel_type': ChannelType.FRONTEND,
                'is_internal': True,
            },
            'scopes': [EventAPIScope.PERMANENT],
        },
    }
    for key, config in configs.items():
        config['client']['client_id'] = uuid.uuid4().hex
        config['scopes'] = [s for s in config['scopes']]
        redis_db.set(construct_key(config['token']), orjson.dumps(config))
    yield configs
    for key, config in configs.items():
        redis_db.delete(construct_key(config['token']))
