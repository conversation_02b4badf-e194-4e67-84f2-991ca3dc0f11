import argparse

BACKENDS = ['tagtoo_event']


def parse_arguments():
    parser = argparse.ArgumentParser()
    parser.add_argument('backend', choices=BACKENDS, help='Subscriber backend name.')
    parser.add_argument('--version', choices=['v1'], help='Subscriber version.')
    parser.add_argument(
        '--max-messages',
        type=int,
        default=None,
        help='Max message to subscribe at a time.',
    )
    parser.add_argument('--timeout', type=int, default=None, help='Subscriber timeout.')
    args = parser.parse_args()
    return args


def main():
    args = parse_arguments()
    from bq_writer import subscribers

    if args.backend == 'tagtoo_event':
        version_classes_map = {
            'v1': subscribers.TagtooEventSubscriberV1,
        }
        subscriber = version_classes_map[args.version]()
        subscriber(max_messages=args.max_messages, timeout=args.timeout)


if __name__ == '__main__':
    main()
