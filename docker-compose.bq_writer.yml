version: '3.7'

services:
  bq_writer: &bq_writer
    container_name: bq-writer
    image: ad_track_bq_writer
    build:
      context: .
      dockerfile: bq_writer/Dockerfile
    entrypoint: ./scripts/entrypoint.sh
    volumes:
      - ./bq_writer:/app
      - ./gcloud/sa/event-bq-writer-vm.json:/credentials/event-bq-writer-vm.json
    environment:
      - GOOGLE_APPLICATION_CREDENTIALS=/credentials/event-bq-writer-vm.json
      - PUBSUB_EMULATOR_HOST=bq-writer-pubsub:8681
      - BIGQUERY_DATASET_ID=${BIGQUERY_DATASET_ID}
      - BIGQUERY_TAGTOO_EVENT_TABLE_ID=${BIGQUERY_TAGTOO_EVENT_TABLE_ID}
    restart: always
    env_file: ./bq_writer/.env
    command:
      - tail
      - -F
      - /dev/null
    networks:
      - bq_writer
    depends_on:
      - ip2location
      - bq_writer_pubsub

  tagtoo_event_subscriber:
    <<: *bq_writer
    container_name: tagtoo-event-subscriber
    command: ["python", "subscribe.py", "tagtoo_event", "--version=v1", "--max-messages=100"]
    healthcheck:
      test: ["CMD-SHELL", "ps -eocmd | grep '^python subscribe.py' && exit 0 || exit 1"]
      interval: 5s
      start_period: 5s

  ip2location:
    container_name: ip2location
    image: ad_track_ip2location
    build:
      context: ./ip2location
      args:
        - API_TOKEN=fVbjKMZgUzOiJ8Gyz3y1N6R33EB6IDQjEfgtJMjehbezHicZPJrOwnttEUM4dLds
        - PRODUCT_CODE=DB9LITE
    networks:
      - bq_writer

  bq_writer_pubsub:
    container_name: bq-writer-pubsub
    image: messagebird/gcloud-pubsub-emulator
    environment:
      - PUBSUB_PROJECT1=tagtoo-tracking,tagtoo-event:tagtoo-event-to-bigquery
    networks:
      - bq_writer

networks:
  bq_writer:
    driver: bridge
