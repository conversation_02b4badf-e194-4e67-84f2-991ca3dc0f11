# BigQuery 雙寫入架構成本監控策略

## 🎯 **目標**

建立持續、準確的成本監控機制，確保雙寫入架構的成本影響在可控範圍內，並提供實際的成本效益驗證。

## 📊 **成本監控框架**

### **1. 實時成本追蹤**

#### **BigQuery 成本監控**
```sql
-- 日度 BigQuery 成本查詢
SELECT
  DATE(creation_time) as date,
  project_id,
  SUM(total_bytes_processed) / POW(10, 12) as tb_processed,
  SUM(total_bytes_processed) / POW(10, 12) * 5.0 as estimated_cost_usd
FROM `region-us`.INFORMATION_SCHEMA.JOBS_BY_PROJECT
WHERE creation_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 DAY)
  AND job_type = 'QUERY'
  AND state = 'DONE'
GROUP BY date, project_id
ORDER BY date DESC;
```

#### **GKE 資源成本監控**
```bash
# 使用 gcloud 監控 GKE 成本
gcloud monitoring time-series list \
  --filter='metric.type="kubernetes.io/container/cpu/core_usage_time"' \
  --interval.start-time="2025-08-01T00:00:00Z" \
  --interval.end-time="2025-08-22T23:59:59Z" \
  --aggregation.alignment-period="86400s" \
  --aggregation.per-series-aligner="ALIGN_RATE" \
  --aggregation.cross-series-reducer="REDUCE_SUM"
```

### **2. 成本警報機制**

#### **Cloud Monitoring 警報配置**
```yaml
# BigQuery 成本警報
alertPolicy:
  displayName: "BigQuery Daily Cost Alert"
  conditions:
    - displayName: "Daily BigQuery cost exceeds $60"
      conditionThreshold:
        filter: 'resource.type="bigquery_project"'
        comparison: COMPARISON_GREATER_THAN
        thresholdValue: 60.0
        duration: "300s"
  notificationChannels:
    - "projects/tagtoo-tracking/notificationChannels/email-alerts"

# GKE 資源使用警報  
alertPolicy:
  displayName: "GKE Resource Usage Alert"
  conditions:
    - displayName: "GKE daily cost exceeds $700"
      conditionThreshold:
        filter: 'resource.type="gke_cluster"'
        comparison: COMPARISON_GREATER_THAN
        thresholdValue: 700.0
        duration: "300s"
```

### **3. 成本歸因分析**

#### **雙寫入功能成本分離**
```python
def calculate_dual_write_cost_attribution():
    """計算雙寫入功能的具體成本歸因"""
    
    # BigQuery 成本歸因
    bq_costs = {
        'baseline_storage': 36.88,
        'integrated_event_storage': 0.38,
        'baseline_queries': get_baseline_query_cost(),
        'debug_queries': get_debug_query_cost(),
        'dual_write_operations': get_write_operation_cost()
    }
    
    # GKE 成本歸因
    gke_costs = {
        'baseline_compute': 460.51,
        'dual_write_cpu_overhead': calculate_cpu_overhead(),
        'dual_write_memory_overhead': calculate_memory_overhead(),
        'background_queue_processing': calculate_queue_processing_cost()
    }
    
    return {
        'bigquery_attribution': bq_costs,
        'gke_attribution': gke_costs,
        'total_dual_write_cost': sum(bq_costs.values()) + sum(gke_costs.values()) - 497.39
    }
```

## 🔍 **除錯查詢成本驗證**

### **實際效益測量方法**

#### **1. A/B 測試方法**
```python
def debug_query_cost_ab_test():
    """A/B 測試除錯查詢成本節省"""
    
    # 測試場景
    test_scenarios = [
        {
            'name': 'session_tracking_7days',
            'traditional_query': get_traditional_session_query(),
            'optimized_query': get_optimized_session_query(),
            'expected_data_range': '7 days'
        },
        {
            'name': 'error_analysis_1day', 
            'traditional_query': get_traditional_error_query(),
            'optimized_query': get_optimized_error_query(),
            'expected_data_range': '1 day'
        }
    ]
    
    results = []
    for scenario in test_scenarios:
        traditional_cost = estimate_query_cost(scenario['traditional_query'])
        optimized_cost = estimate_query_cost(scenario['optimized_query'])
        
        results.append({
            'scenario': scenario['name'],
            'traditional_cost': traditional_cost,
            'optimized_cost': optimized_cost,
            'savings_pct': (traditional_cost - optimized_cost) / traditional_cost * 100,
            'absolute_savings': traditional_cost - optimized_cost
        })
    
    return results
```

#### **2. 生產環境驗證指標**
```yaml
# 除錯查詢成本驗證指標
debug_query_metrics:
  baseline_period: "30 days before dual-write"
  comparison_period: "30 days after dual-write"
  
  metrics:
    - name: "average_debug_query_cost"
      target_reduction: "40-60%"
      measurement_method: "BigQuery job cost analysis"
    
    - name: "debug_query_frequency"
      baseline_tracking: "manual log analysis"
      new_tracking: "automated tracking_id queries"
    
    - name: "debug_resolution_time"
      target_improvement: "50% faster"
      measurement_method: "incident response time tracking"
```

## 📈 **成本最佳化建議**

### **1. 短期最佳化 (1-3 個月)**

#### **資源使用最佳化**
- **GKE 自動調整**：基於實際使用情況調整資源請求
- **佇列大小調整**：根據實際負載調整 `INTEGRATED_QUEUE_MAXSIZE`
- **採樣率最佳化**：根據除錯需求調整採樣率

#### **BigQuery 查詢最佳化**
- **分區策略**：確保 integrated_event 表格有效分區
- **叢集索引**：基於 tracking_id 建立叢集索引
- **查詢模式分析**：識別最常用的除錯查詢模式

### **2. 中期最佳化 (3-6 個月)**

#### **架構演進**
- **統一事件架構**：逐步遷移到統一的事件處理架構
- **成本效益分析**：基於實際使用數據重新評估架構
- **自動化成本控制**：實施自動化的成本控制機制

#### **效能調整**
- **背景處理最佳化**：改善背景佇列處理效率
- **記憶體使用最佳化**：減少記憶體佔用
- **網路最佳化**：減少不必要的網路傳輸

### **3. 長期策略 (6-12 個月)**

#### **成本模型演進**
- **動態定價適應**：適應 GCP 定價變化
- **業務增長規劃**：考慮業務增長對成本的影響
- **技術債務管理**：識別和解決技術債務

## 🚨 **成本控制機制**

### **自動化成本控制**

#### **1. 成本閾值控制**
```python
def automated_cost_control():
    """自動化成本控制機制"""
    
    daily_cost_limit = 60.0  # $60/day
    monthly_cost_limit = 1800.0  # $1800/month
    
    current_daily_cost = get_current_daily_cost()
    current_monthly_cost = get_current_monthly_cost()
    
    if current_daily_cost > daily_cost_limit:
        # 觸發警報
        send_cost_alert("Daily cost limit exceeded")
        
        # 自動調整採樣率
        if current_daily_cost > daily_cost_limit * 1.2:
            reduce_sampling_rate(0.5)  # 減少 50% 採樣率
    
    if current_monthly_cost > monthly_cost_limit:
        # 觸發緊急停用
        disable_dual_write_feature()
        send_emergency_alert("Monthly cost limit exceeded - dual write disabled")
```

#### **2. 智慧資源調整**
```python
def intelligent_resource_adjustment():
    """基於使用模式的智慧資源調整"""
    
    # 分析過去 7 天的使用模式
    usage_pattern = analyze_usage_pattern(days=7)
    
    # 預測未來 24 小時的資源需求
    predicted_load = predict_load(usage_pattern)
    
    # 調整資源配置
    if predicted_load < 0.7:  # 低負載
        scale_down_resources(0.8)  # 減少 20% 資源
    elif predicted_load > 1.3:  # 高負載
        scale_up_resources(1.2)   # 增加 20% 資源
```

## 📊 **成本報告和儀表板**

### **週度成本報告**
```markdown
# 週度成本報告範本

## 成本概覽
- 本週總成本: $XXX
- 與基準比較: +X.X%
- 雙寫入功能歸因成本: $XXX

## 關鍵指標
- BigQuery 查詢成本: $XXX
- GKE 運算成本: $XXX
- 除錯查詢節省: $XXX (-X.X%)

## 異常分析
- 成本異常事件: X 次
- 主要原因: XXX
- 採取的行動: XXX

## 下週預測
- 預期成本: $XXX
- 風險因素: XXX
- 建議行動: XXX
```

### **即時成本儀表板**
- **當日成本追蹤**：實時顯示當日累計成本
- **月度成本趨勢**：顯示月度成本趨勢和預測
- **功能歸因分析**：顯示雙寫入功能的具體成本貢獻
- **警報狀態**：顯示當前的成本警報狀態

## 🎯 **成功指標**

### **成本控制指標**
- ✅ **月度成本變異 < 10%**：實際成本與預期成本差異小於 10%
- ✅ **成本警報響應時間 < 5 分鐘**：成本異常警報響應時間
- ✅ **自動化成本控制覆蓋率 > 90%**：自動化處理的成本控制事件比例

### **成本效益指標**
- 🎯 **除錯查詢成本節省 > 40%**：實際測量的除錯查詢成本節省
- 🎯 **除錯效率提升 > 50%**：問題排查時間減少
- 🎯 **系統可觀測性改善**：監控指標覆蓋率提升

---

## 📝 **結論**

通過建立完整的成本監控策略，我們能夠：

1. **確保成本可控**：實時監控和自動化控制機制
2. **驗證實際效益**：基於生產環境的實際測量
3. **持續最佳化**：基於數據驅動的成本最佳化
4. **風險管控**：完整的成本風險管控機制

這個策略確保雙寫入架構的成本影響始終在可控範圍內，並提供實際的成本效益驗證。

*最後更新：2025-08-22*
*版本：v1.0*
