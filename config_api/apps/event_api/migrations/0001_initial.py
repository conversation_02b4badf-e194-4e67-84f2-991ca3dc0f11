# Generated by Django 3.1.7 on 2021-03-04 07:03

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import model_utils.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name='Client',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'created',
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name='created',
                    ),
                ),
                (
                    'modified',
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name='modified',
                    ),
                ),
                (
                    'client_id',
                    models.CharField(
                        max_length=22, unique=True, verbose_name='Client ID'
                    ),
                ),
                ('name', models.CharField(max_length=120, verbose_name='Name')),
                (
                    'channel_type',
                    models.PositiveSmallIntegerField(
                        choices=[(0, 'Front Channel'), (1, 'Back Channel')],
                        verbose_name='Channel Type',
                    ),
                ),
                (
                    'is_internal',
                    models.BooleanField(default=False, verbose_name='Internal'),
                ),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Scope',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'created',
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name='created',
                    ),
                ),
                (
                    'modified',
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name='modified',
                    ),
                ),
                ('name', models.CharField(max_length=120, verbose_name='Name')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='AccessToken',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'created',
                    model_utils.fields.AutoCreatedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name='created',
                    ),
                ),
                (
                    'modified',
                    model_utils.fields.AutoLastModifiedField(
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name='modified',
                    ),
                ),
                ('ec_id', models.PositiveIntegerField(db_index=True)),
                (
                    'token',
                    models.CharField(max_length=60, unique=True, verbose_name='Token'),
                ),
                (
                    'revoked',
                    models.DateTimeField(blank=True, null=True, verbose_name='Revoked'),
                ),
                (
                    'client',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='access_tokens',
                        to='event_api_app.client',
                        verbose_name='Client',
                    ),
                ),
                (
                    'scopes',
                    models.ManyToManyField(
                        related_name='access_tokens',
                        to='event_api_app.Scope',
                        verbose_name='Scope',
                    ),
                ),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
