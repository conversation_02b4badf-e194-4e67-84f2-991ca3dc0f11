services:
  ip2location_fix:
    container_name: ip2location-fix
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - .:/app
      - ${GOOGLE_APPLICATION_CREDENTIALS}:/credentials/service-account.json
    environment:
      - GOOGLE_APPLICATION_CREDENTIALS=/credentials/service-account.json
      - BIGQUERY_DATASET_ID=event_prod
      - BIGQUERY_TABLE_ID=tagtoo_event
      - MYSQL_HOST=ip2location
      - MYSQL_PORT=3306
      - MYSQL_USER=root
      - MYSQL_PASSWORD=
      - MYSQL_DATABASE=ip2location_database
    command: ["python", "/app/fix_location.py"]
    networks:
      - ip2location_network
    depends_on:
      ip2location:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "mariadb", "-h", "localhost", "-u", "root", "-e", "SELECT 1"]
      interval: 5s
      timeout: 5s
      retries: 20

  ip2location_test:
    container_name: ip2location-test
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - .:/app
      - ${GOOGLE_APPLICATION_CREDENTIALS}:/credentials/service-account.json
    environment:
      - GOOGLE_APPLICATION_CREDENTIALS=/credentials/service-account.json
      - MYSQL_HOST=ip2location
      - MYSQL_PORT=3306
      - MYSQL_USER=root
      - MYSQL_PASSWORD=
      - MYSQL_DATABASE=ip2location_database
    command: ["sh", "-c", "sleep 30 && python /app/test_connection.py"]
    networks:
      - ip2location_network
    depends_on:
      ip2location:
        condition: service_healthy

  ip2location:
    container_name: ip2location
    image: asia.gcr.io/tagtoo-tracking/ip2location-mysql:2024-08-08
    platform: linux/amd64
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ALLOW_EMPTY_PASSWORD=yes  # 允許無密碼連接
      - MYSQL_ROOT_HOST=%  # 允許從任何主機連接 root 用戶
    volumes:
      - ./mysql-conf:/etc/mysql/conf.d
      - ip2location_data:/var/lib/mysql
    healthcheck:
      test: ["CMD", "mariadb", "-h", "localhost", "-u", "root", "-e", "SELECT 1"]
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 30s
    networks:
      - ip2location_network

networks:
  ip2location_network:
    driver: bridge
    name: ip2location_network
    external: true

volumes:
  ip2location_data:
    name: ip2location_data