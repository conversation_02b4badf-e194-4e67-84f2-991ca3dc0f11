#!/bin/bash
# emergency_rollback.sh - BigQuery 雙寫入架構緊急回滾腳本
# 版本: v1.0
# 最後更新: 2025-09-03

set -e

# 配置變數
LEVEL=${1:-1}
NAMESPACE="event-prod"
DEPLOYMENT="bq-writer-tagtoo-event-subscriber"
PROJECT_ID="tagtoo-tracking"
DATASET_ID="event_prod"
TABLE_ID="integrated_event"

# 顏色定義
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# 檢查必要工具
check_prerequisites() {
    log_info "檢查必要工具..."
    
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl 未安裝或不在 PATH 中"
        exit 1
    fi
    
    if ! command -v bq &> /dev/null; then
        log_error "bq CLI 未安裝或不在 PATH 中"
        exit 1
    fi
    
    # 檢查 kubectl 連線
    if ! kubectl cluster-info &> /dev/null; then
        log_error "無法連接到 Kubernetes 叢集"
        exit 1
    fi
    
    log_success "必要工具檢查通過"
}

# Level 1: 立即停用功能
level1_rollback() {
    log_warn "🔴 開始執行 Level 1: 立即停用功能"
    
    # 方法 A: 直接設定環境變數
    log_info "設定環境變數停用雙寫入功能..."
    kubectl set env deployment/$DEPLOYMENT \
        INTEGRATED_WRITE_ENABLED=false \
        -n $NAMESPACE
    
    # 等待部署更新
    log_info "等待部署更新..."
    kubectl rollout status deployment/$DEPLOYMENT -n $NAMESPACE --timeout=300s
    
    # 驗證設定
    log_info "驗證環境變數設定..."
    CURRENT_VALUE=$(kubectl get deployment $DEPLOYMENT -n $NAMESPACE \
        -o jsonpath='{.spec.template.spec.containers[0].env[?(@.name=="INTEGRATED_WRITE_ENABLED")].value}')
    
    if [ "$CURRENT_VALUE" = "false" ]; then
        log_success "✅ 雙寫入功能已成功停用"
    else
        log_error "❌ 環境變數設定失敗，當前值: $CURRENT_VALUE"
        exit 1
    fi
}

# Level 2: 配置回滾
level2_rollback() {
    log_warn "🟡 開始執行 Level 2: 配置回滾"
    
    # 先執行 Level 1
    level1_rollback
    
    # 檢查是否有 deploy 容器
    if ! docker ps | grep -q "deploy"; then
        log_warn "deploy 容器未運行，嘗試啟動..."
        if command -v make &> /dev/null; then
            make deploy-up
        else
            log_error "無法啟動 deploy 容器，請手動執行 make deploy-up"
            exit 1
        fi
    fi
    
    # 建立回滾配置
    log_info "建立 Terraform 回滾配置..."
    docker exec deploy sh -c "cd /src && cat > prod.tfvars.rollback << 'EOF'
bq_writer = {
  env = {
    test_mode                      = \"False\"
    integrated_write_enabled       = \"false\"
    integrated_write_sample_rate   = \"0.0\"
    integrated_queue_maxsize       = \"1000\"
    max_immediate_retries          = \"3\"
    max_delayed_retries            = \"5\"
    bq_debug_daily_budget          = \"5.0\"
  }
  ip2location = {
    image_tag = \"2025-04-14\"
    requests = {
      cpu               = \"100m\"
      memory            = \"400Mi\"
      ephemeral-storage = \"3Gi\"
    }
  }
}
EOF"
    
    # 應用 Terraform 回滾配置
    log_info "應用 Terraform 回滾配置..."
    docker exec deploy sh -c "cd /src && terraform plan -var-file=prod.tfvars.rollback"
    
    read -p "確認要應用 Terraform 回滾配置嗎？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker exec deploy sh -c "cd /src && terraform apply -var-file=prod.tfvars.rollback -auto-approve"
        log_success "✅ Terraform 配置回滾完成"
    else
        log_warn "⚠️ 跳過 Terraform 配置回滾"
    fi
}

# Level 3: 完整回滾
level3_rollback() {
    log_warn "🟠 開始執行 Level 3: 完整回滾"
    
    # 先執行 Level 2
    level2_rollback
    
    # 檢查部署歷史
    log_info "檢查部署歷史..."
    kubectl rollout history deployment/$DEPLOYMENT -n $NAMESPACE
    
    # 回滾到前一個版本
    log_info "回滾 Kubernetes 部署到前一個版本..."
    kubectl rollout undo deployment/$DEPLOYMENT -n $NAMESPACE
    
    # 等待回滾完成
    log_info "等待回滾完成..."
    kubectl rollout status deployment/$DEPLOYMENT -n $NAMESPACE --timeout=600s
    
    # BigQuery 表格處理
    log_info "處理 BigQuery integrated_event 表格..."
    echo "選擇 BigQuery 表格處理方式："
    echo "1) 保留表格（推薦）"
    echo "2) 建立備份後刪除"
    echo "3) 直接刪除"
    read -p "請選擇 (1-3): " -n 1 -r
    echo
    
    case $REPLY in
        1)
            log_info "保留 integrated_event 表格供後續分析"
            ;;
        2)
            BACKUP_TABLE="integrated_event_backup_$(date +%Y%m%d_%H%M%S)"
            log_info "建立備份表格: $BACKUP_TABLE"
            bq cp $PROJECT_ID:$DATASET_ID.$TABLE_ID $PROJECT_ID:event_backup.$BACKUP_TABLE
            log_info "刪除原始表格..."
            bq rm -f $PROJECT_ID:$DATASET_ID.$TABLE_ID
            log_success "✅ 表格已備份並刪除"
            ;;
        3)
            log_warn "直接刪除 integrated_event 表格..."
            bq rm -f $PROJECT_ID:$DATASET_ID.$TABLE_ID
            log_success "✅ 表格已刪除"
            ;;
        *)
            log_info "無效選擇，保留表格"
            ;;
    esac
    
    log_success "✅ Level 3 完整回滾完成"
}

# 驗證回滾結果
verify_rollback() {
    log_info "🔍 驗證回滾結果..."
    
    # 檢查 Pod 狀態
    log_info "檢查 Pod 狀態..."
    kubectl get pods -n $NAMESPACE -l app=$DEPLOYMENT
    
    # 檢查環境變數
    log_info "檢查環境變數..."
    CURRENT_VALUE=$(kubectl get deployment $DEPLOYMENT -n $NAMESPACE \
        -o jsonpath='{.spec.template.spec.containers[0].env[?(@.name=="INTEGRATED_WRITE_ENABLED")].value}')
    echo "INTEGRATED_WRITE_ENABLED: $CURRENT_VALUE"
    
    # 檢查最近日誌
    log_info "檢查最近日誌..."
    kubectl logs deployment/$DEPLOYMENT -n $NAMESPACE --tail=20 | grep -E "(雙寫入器|INTEGRATED_WRITE|ERROR)" || true
    
    # 檢查資源使用
    log_info "檢查資源使用..."
    kubectl top pods -n $NAMESPACE -l app=$DEPLOYMENT || log_warn "無法獲取資源使用情況"
    
    log_success "✅ 回滾驗證完成"
}

# 主函數
main() {
    echo "🚨 BigQuery 雙寫入架構緊急回滾腳本"
    echo "版本: v1.0"
    echo "時間: $(date)"
    echo "回滾等級: Level $LEVEL"
    echo

    # 檢查參數
    if [[ ! "$LEVEL" =~ ^[1-3]$ ]]; then
        log_error "無效的回滾等級: $LEVEL"
        echo "使用方法: $0 [1|2|3]"
        echo "  1: 立即停用功能 (< 5 分鐘)"
        echo "  2: 配置回滾 (< 30 分鐘)"
        echo "  3: 完整回滾 (< 60 分鐘)"
        exit 1
    fi

    # 確認執行
    read -p "確認要執行 Level $LEVEL 回滾嗎？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "回滾操作已取消"
        exit 0
    fi

    # 檢查必要工具
    check_prerequisites

    # 記錄開始時間
    START_TIME=$(date +%s)

    # 執行對應等級的回滾
    case $LEVEL in
        1)
            level1_rollback
            ;;
        2)
            level2_rollback
            ;;
        3)
            level3_rollback
            ;;
    esac

    # 驗證回滾結果
    verify_rollback

    # 計算執行時間
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))

    log_success "🎉 Level $LEVEL 回滾完成！"
    log_info "執行時間: ${DURATION} 秒"
    log_info "請執行 verify_rollback.sh 進行詳細驗證"
}

# 執行主函數
main "$@"
