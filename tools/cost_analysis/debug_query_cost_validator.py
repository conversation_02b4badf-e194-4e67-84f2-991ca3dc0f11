#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug Query Cost Validator

Validate the cost savings of optimized debug queries using CostOptimizedDebugQueries
"""

import json
import subprocess
import sys
from datetime import datetime, timedelta
from typing import Dict, Any, List
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DebugQueryCostValidator:
    """除錯查詢成本驗證器"""

    def __init__(self, project_id: str = "tagtoo-tracking"):
        self.project_id = project_id
        self.query_tb_cost = 5.0  # $5.00 per TB processed

    def estimate_query_cost(self, query: str) -> Dict[str, Any]:
        """估算查詢成本"""
        try:
            cmd = [
                'bq', 'query', '--dry_run', '--use_legacy_sql=false',
                '--format=json', query
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            query_info = json.loads(result.stdout)

            bytes_processed = int(query_info.get('totalBytesProcessed', 0))
            tb_processed = bytes_processed / (1024**4)  # Convert to TB
            cost = tb_processed * self.query_tb_cost

            return {
                'bytes_processed': bytes_processed,
                'gb_processed': bytes_processed / (1024**3),
                'tb_processed': tb_processed,
                'cost_usd': cost,
                'query': query[:100] + "..." if len(query) > 100 else query
            }
        except subprocess.CalledProcessError as e:
            logger.error(f"查詢成本估算失敗: {e}")
            return {
                'bytes_processed': 0,
                'gb_processed': 0,
                'tb_processed': 0,
                'cost_usd': 0,
                'error': str(e)
            }

    def get_traditional_debug_queries(self) -> List[str]:
        """傳統除錯查詢（使用完整 tagtoo_event 表格）"""
        return [
            # 查詢 1: 特定 session 的所有事件
            """
            SELECT
                timestamp,
                event_type,
                page_url,
                user_agent,
                ip,
                session_hash,
                ec_id,
                raw_json
            FROM `tagtoo-tracking.event_prod.tagtoo_event`
            WHERE session_hash LIKE 'session_%'
            AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
            ORDER BY timestamp DESC
            LIMIT 1000
            """,

            # 查詢 2: 特定 IP 的事件分析
            """
            SELECT
                ip,
                COUNT(*) as event_count,
                COUNT(DISTINCT session_hash) as unique_sessions,
                ARRAY_AGG(DISTINCT event_type LIMIT 10) as event_types,
                MIN(timestamp) as first_event,
                MAX(timestamp) as last_event
            FROM `tagtoo-tracking.event_prod.tagtoo_event`
            WHERE ip LIKE '%.%.%.%'
            AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 DAY)
            GROUP BY ip
            LIMIT 100
            """,

            # 查詢 3: 特定 User Agent 的事件追蹤
            """
            SELECT
                user_agent,
                page_url,
                event_type,
                timestamp,
                session_hash
            FROM `tagtoo-tracking.event_prod.tagtoo_event`
            WHERE user_agent LIKE '%Chrome%'
            AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 DAY)
            ORDER BY timestamp DESC
            LIMIT 500
            """,

            # 查詢 4: 錯誤事件分析
            """
            SELECT
                event_type,
                page_url,
                timestamp,
                session_hash
            FROM `tagtoo-tracking.event_prod.tagtoo_event`
            WHERE event_type = 'error'
            AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 DAY)
            ORDER BY timestamp DESC
            LIMIT 200
            """,

            # 查詢 5: 特定頁面的事件統計
            """
            SELECT
                page_url,
                event_type,
                COUNT(*) as event_count,
                COUNT(DISTINCT session_hash) as unique_sessions,
                COUNT(DISTINCT ip) as unique_ips
            FROM `tagtoo-tracking.event_prod.tagtoo_event`
            WHERE page_url IS NOT NULL
            AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 DAY)
            GROUP BY page_url, event_type
            ORDER BY event_count DESC
            LIMIT 50
            """
        ]

    def get_optimized_debug_queries(self) -> List[str]:
        """最佳化除錯查詢（使用 integrated_event 表格）"""
        return [
            # 查詢 1: 特定 session 的所有事件（最佳化版）
            """
            SELECT
                timestamp,
                event_type,
                page_url,
                tracking_id,
                raw_json
            FROM `tagtoo-tracking.event_prod.integrated_event`
            WHERE JSON_EXTRACT_SCALAR(raw_json, '$.debug.session_hash') LIKE 'session_%'
            AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 DAY)
            ORDER BY timestamp DESC
            LIMIT 1000
            """,

            # 查詢 2: 特定 IP 的事件分析（最佳化版）
            """
            SELECT
                JSON_EXTRACT_SCALAR(raw_json, '$.debug.ip') as ip,
                COUNT(*) as event_count,
                COUNT(DISTINCT JSON_EXTRACT_SCALAR(raw_json, '$.debug.session_hash')) as unique_sessions,
                ARRAY_AGG(DISTINCT event_type) as event_types,
                MIN(timestamp) as first_event,
                MAX(timestamp) as last_event
            FROM `tagtoo-tracking.event_prod.integrated_event`
            WHERE JSON_EXTRACT_SCALAR(raw_json, '$.debug.ip') = '*************'
            AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 DAY)
            GROUP BY JSON_EXTRACT_SCALAR(raw_json, '$.debug.ip')
            """,

            # 查詢 3: 特定 User Agent 的事件追蹤（最佳化版）
            """
            SELECT
                JSON_EXTRACT_SCALAR(raw_json, '$.debug.ua_signature') as ua_signature,
                page_url,
                event_type,
                timestamp,
                tracking_id,
                raw_json
            FROM `tagtoo-tracking.event_prod.integrated_event`
            WHERE JSON_EXTRACT_SCALAR(raw_json, '$.debug.ua_signature') LIKE '%Chrome/91.0%'
            AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 3 DAY)
            ORDER BY timestamp DESC
            LIMIT 1000
            """,

            # 查詢 4: 錯誤事件分析（最佳化版）
            """
            SELECT
                event_type,
                page_url,
                JSON_EXTRACT_SCALAR(raw_json, '$.debug.ua_signature') as ua_signature,
                JSON_EXTRACT_SCALAR(raw_json, '$.debug.ip') as ip,
                timestamp,
                tracking_id,
                raw_json
            FROM `tagtoo-tracking.event_prod.integrated_event`
            WHERE JSON_EXTRACT_SCALAR(raw_json, '$.error') IS NOT NULL
            AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 DAY)
            ORDER BY timestamp DESC
            """,

            # 查詢 5: 特定頁面的事件統計（最佳化版）
            """
            SELECT
                page_url,
                event_type,
                COUNT(*) as event_count,
                COUNT(DISTINCT JSON_EXTRACT_SCALAR(raw_json, '$.debug.session_hash')) as unique_sessions,
                COUNT(DISTINCT JSON_EXTRACT_SCALAR(raw_json, '$.debug.ip')) as unique_ips,
                AVG(CAST(JSON_EXTRACT_SCALAR(raw_json, '$.load_time') AS FLOAT64)) as avg_load_time
            FROM `tagtoo-tracking.event_prod.integrated_event`
            WHERE page_url LIKE '%product%'
            AND timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 DAY)
            GROUP BY page_url, event_type
            ORDER BY event_count DESC
            """
        ]

    def validate_cost_savings(self) -> Dict[str, Any]:
        """驗證成本節省效果"""
        logger.info("開始驗證除錯查詢成本節省...")

        traditional_queries = self.get_traditional_debug_queries()
        optimized_queries = self.get_optimized_debug_queries()

        traditional_costs = []
        optimized_costs = []

        # 分析傳統查詢成本
        logger.info("分析傳統除錯查詢成本...")
        for i, query in enumerate(traditional_queries, 1):
            logger.info(f"分析傳統查詢 {i}/5...")
            cost_info = self.estimate_query_cost(query)
            cost_info['query_type'] = f'traditional_query_{i}'
            traditional_costs.append(cost_info)

        # 分析最佳化查詢成本
        logger.info("分析最佳化除錯查詢成本...")
        for i, query in enumerate(optimized_queries, 1):
            logger.info(f"分析最佳化查詢 {i}/5...")
            cost_info = self.estimate_query_cost(query)
            cost_info['query_type'] = f'optimized_query_{i}'
            optimized_costs.append(cost_info)

        # 計算總成本和節省
        total_traditional_cost = sum(c['cost_usd'] for c in traditional_costs)
        total_optimized_cost = sum(c['cost_usd'] for c in optimized_costs)
        cost_savings = total_traditional_cost - total_optimized_cost
        savings_percentage = (cost_savings / total_traditional_cost) * 100 if total_traditional_cost > 0 else 0

        return {
            'traditional_queries': traditional_costs,
            'optimized_queries': optimized_costs,
            'summary': {
                'total_traditional_cost': total_traditional_cost,
                'total_optimized_cost': total_optimized_cost,
                'cost_savings': cost_savings,
                'savings_percentage': savings_percentage,
                'target_savings': 60.0,  # 目標節省 60%
                'target_achieved': savings_percentage >= 60.0
            }
        }

    def generate_validation_report(self) -> str:
        """生成驗證報告"""
        validation_result = self.validate_cost_savings()

        if not validation_result:
            return "❌ 無法生成除錯查詢成本驗證報告"

        summary = validation_result['summary']
        traditional = validation_result['traditional_queries']
        optimized = validation_result['optimized_queries']

        report = f"""
# 除錯查詢成本節省驗證報告

## 📊 成本節省摘要

- **傳統查詢總成本**: ${summary['total_traditional_cost']:.4f}
- **最佳化查詢總成本**: ${summary['total_optimized_cost']:.4f}
- **成本節省**: ${summary['cost_savings']:.4f} (-{summary['savings_percentage']:.1f}%)
- **目標節省**: {summary['target_savings']:.0f}%
- **目標達成**: {'✅ 是' if summary['target_achieved'] else '❌ 否'}

## 📈 詳細查詢成本比較

| 查詢類型 | 傳統查詢成本 | 最佳化查詢成本 | 節省金額 | 節省比例 |
|---------|-------------|---------------|---------|---------|"""

        for i in range(len(traditional)):
            trad_cost = traditional[i]['cost_usd']
            opt_cost = optimized[i]['cost_usd']
            savings = trad_cost - opt_cost
            savings_pct = (savings / trad_cost) * 100 if trad_cost > 0 else 0

            report += f"""
| 查詢 {i+1} | ${trad_cost:.4f} | ${opt_cost:.4f} | ${savings:.4f} | {savings_pct:.1f}% |"""

        report += f"""

## 🔍 查詢處理資料量比較

### 傳統查詢
"""

        for i, query in enumerate(traditional, 1):
            report += f"""
**查詢 {i}**: {query['gb_processed']:.2f} GB 處理量 (${query['cost_usd']:.4f})"""

        report += """

### 最佳化查詢
"""

        for i, query in enumerate(optimized, 1):
            report += f"""
**查詢 {i}**: {query['gb_processed']:.2f} GB 處理量 (${query['cost_usd']:.4f})"""

        report += f"""

## 💡 成本節省分析

### 關鍵成功因素
1. **資料表大小差異**: integrated_event 表格大小遠小於 tagtoo_event
2. **欄位最佳化**: raw_json 僅包含除錯必要資訊
3. **查詢最佳化**: 針對除錯場景最佳化的查詢結構

### 實際效益
- **平均查詢成本**: 從 ${summary['total_traditional_cost']/5:.4f} 降至 ${summary['total_optimized_cost']/5:.4f}
- **成本節省率**: {summary['savings_percentage']:.1f}%
- **年度節省預估**: ${summary['cost_savings'] * 365:.2f} (假設每日一次除錯查詢)

---
*報告生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
*基於實際 BigQuery 查詢成本估算*
        """

        return report.strip()


def main():
    """主函數"""
    validator = DebugQueryCostValidator()

    print("🔍 開始除錯查詢成本節省驗證...")
    print("=" * 60)

    report = validator.generate_validation_report()
    print(report)

    # 儲存報告
    with open('debug_query_cost_validation_report.md', 'w', encoding='utf-8') as f:
        f.write(report)

    print("\n" + "=" * 60)
    print("✅ 除錯查詢成本驗證完成！報告已儲存至 debug_query_cost_validation_report.md")


if __name__ == "__main__":
    main()
