# In order to use the shared local packages, please build from the root context

# Stage1
FROM --platform=linux/amd64 python:3.8 as build-deps

COPY shared /shared

RUN python /shared/setup_pubsub.py bdist_wheel && \
    python /shared/setup_error_report.py bdist_wheel

# Stage2
FROM --platform=linux/amd64 python:3.8

LABEL author="<PERSON> (<EMAIL>)"

# python envs
ENV PYTHONFAULTHANDLER=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONHASHSEED=random \
    PIP_NO_CACHE_DIR=off \
    PIP_DISABLE_PIP_VERSION_CHECK=on \
    PIP_DEFAULT_TIMEOUT=100

USER root

RUN apt-get update -y && apt-get install -y libmariadb-dev

COPY --from=build-deps /dist /dist

WORKDIR /app

COPY bq_writer/requirements.txt .

RUN pip install --upgrade pip && \
    pip install /dist/* && \
    pip install -r requirements.txt

COPY bq_writer/. .

# 直接在容器中創建 entrypoint.sh
RUN echo '#!/bin/sh\n\
\n\
set -e\n\
\n\
cmd="$@"\n\
\n\
ip2location_db_ready() {\n\
python << END\n\
import sys\n\
from bq_writer.ip2location import ip2location_connection\n\
try:\n\
  with ip2location_connection() as conn:\n\
    cursor = conn.cursor()\n\
    cursor.execute("SELECT 1;")\n\
    if cursor.fetchone():\n\
      sys.exit(0)\n\
except Exception as exc:\n\
  sys.exit(-1)\n\
END\n\
}\n\
\n\
until ip2location_db_ready; do\n\
  >&2 echo "IP2Location database is unavailable - sleeping"\n\
  sleep 10\n\
done\n\
\n\
>&2 echo "IP2Location is up - continuing..."\n\
\n\
exec $cmd' > /app/scripts/entrypoint.sh && \
    chmod +x /app/scripts/entrypoint.sh
