<script src="https://unpkg.com/mermaid@8.13.3/dist/mermaid.min.js"></script>

## Permanent has multiple emails

**Input (in-order)**

```json
[
  {
    "permanent": "p1",
    "email": "e1"
  },
  {
    "permanent": "p1",
    "email": "e2"
  }
]
```

**Graph Overlays**

```mermaid
flowchart LR
    subgraph Ground Truth
    p1(("&ensp;p1&ensp;"))
    end
```

```mermaid
flowchart LR
    p1 -.-> p1
    subgraph Group
    p1(("&ensp;p1&ensp;"))
    end
```

**Entity**

|rowkey|meta.timestamp|meta.tx1|meta.tx2|meta.tx3|meta.tx4|
|---|---|---|---|---|---|
|p1#e1####|1636516671000000000|Y|Y|Y|Y|
|p1#e2####|1636516672000000000|Y|Y|Y|Y|

**Permanent**

|rowkey|meta.timestamp|meta.group|emails.e1|emails.e2|
|---|---|---|---|---|
|p1|1636516671000000000|p1|||

**Email**

|rowkey|permanents.p1|
|---|---|
|e1||

|rowkey|permanents.p1|
|---|---|
|e2||

## Simple 3-node graph

**Input (in-order)**

```json
[
  {
    "permanent": "p1",
    "email": "e1"
  },
  {
    "permanent": "p2",
    "mobile": "m1"
  },
  {
    "permanent": "p3",
    "email": "e1",
    "mobile": "m1"
  }
]
```

**Graph Overlays**

```mermaid
flowchart LR
    p1 o-- email --o p3
    p2 o-- mobile --o p3
    subgraph Ground Truth
    p1(("&ensp;p1&ensp;"))
    p2(("&ensp;p2&ensp;"))
    p3(("&ensp;p3&ensp;"))
    end
```

```mermaid
flowchart LR
    p1 -.-> p1
    p2 -.-> p1
    p3 -.-> p1
    subgraph Group
    p1(("&ensp;p1&ensp;"))
    p2(("&ensp;p2&ensp;"))
    p3(("&ensp;p3&ensp;"))
    end
```

**Entity**

|rowkey|meta.timestamp|meta.tx1|meta.tx2|meta.tx3|meta.tx4|
|---|---|---|---|---|---|
|p1#e1####|1636516671000000000|Y|Y|Y|Y|
|p2##m1###|1636516672000000000|Y|Y|Y|Y|
|p3#e1#m1###|1636516673000000000|Y|Y|Y|Y|

**Permanent**

|rowkey|meta.timestamp|meta.group|emails.e1|permanents.p3|
|---|---|---|---|---|
|p1|1636516671000000000|p1|||

|rowkey|meta.timestamp|meta.group|mobiles.m1|permanents.p3|
|---|---|---|---|---|
|p2|1636516672000000000|p1|||

|rowkey|meta.timestamp|meta.group|emails.e1|mobiles.m1|permanents.p1|permanents.p2|
|---|---|---|---|---|---|---|
|p3|1636516673000000000|p1|||||

**Email**

|rowkey|permanents.p1|permanents.p3|
|---|---|---|
|e1||

**Mobile**

|rowkey|permanents.p2|permanents.p3|
|---|---|---|
|m1||

## Merge two sub-graphs

An insert event might eventually merge multiple sub-graphs together, in this example, illustrating a node `p6` insertion
into a graph where two sub-graphs `p1-p2` and `p3-p5` exists.

```mermaid
flowchart LR
    p1 o--o p2
    p3 o--o p4
    p3 o--o p5
    p6(("&ensp;p6&ensp;")) o--o p1
    p6 o--o p5
    subgraph "Graph (root=p1)"
    p1(("&ensp;p1&ensp;"))
    p2(("&ensp;p2&ensp;"))
    end
    subgraph "Graph (root=p3)"
    p3(("&ensp;p3&ensp;"))
    p4(("&ensp;p4&ensp;"))
    p5(("&ensp;p5&ensp;"))
    end
```

Assume the insert time of each permanent are:
```
# p1 is the oldest root
p1 < p2 < p3 < p4 < p5 < p6
```

After traversal, all nodes will be merged together by pointing `meta.group` to node `p1`.

```mermaid
flowchart LR
    p1 o--o p2
    p3 o--o p4
    p3 o--o p5
    p6 o--o p1
    p6 o--o p5
    subgraph "Merged Graph (root=p1)"
    p1(("&ensp;p1&ensp;"))
    p2(("&ensp;p2&ensp;"))
    p3(("&ensp;p3&ensp;"))
    p4(("&ensp;p4&ensp;"))
    p5(("&ensp;p5&ensp;"))
    p6(("&ensp;p6&ensp;"))
    end
```

```mermaid
flowchart LR
    p2 -.-> p1
    p3 -.-> p1
    p4 -.-> p1
    p5 -.-> p1
    p6 -.-> p1
    subgraph "Group Overlay"
    p1(("&ensp;p1&ensp;"))
    p2(("&ensp;p2&ensp;"))
    p3(("&ensp;p3&ensp;"))
    p4(("&ensp;p4&ensp;"))
    p5(("&ensp;p5&ensp;"))
    p6(("&ensp;p6&ensp;"))
    end
```