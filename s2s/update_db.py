import argparse


DATABASES = {1: "Facebook CAPI Pixels"}


def parse_arguments():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        'db', type=int, choices=DATABASES.keys(), help='Redis database.'
    )
    args = parser.parse_args()
    return args


def main():
    args = parse_arguments()
    if args.db == 1:
        from s2s.facebook_capi.utils import update_ec_pixels

        update_ec_pixels()


if __name__ == '__main__':
    main()
