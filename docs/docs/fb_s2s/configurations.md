Use **environment variables** to config the application.

| Name                                          | Description                                                                          |
| --------------------------------------------- | ------------------------------------------------------------------------------------ |
| MODE                                          | Control the app behavior in test/dev/prod environment.                               |
| GCP_PROJECT_ID                                | The Project ID of GCP.                                                               |
| FACEBOOK_CAPI_TOPIC_CAPI_COMPRESSED           | Pub/Sub Topic for batch channel.                                                     |
| FACEBOOK_CAPI_TOPIC_FAILED_BATCH_DECOMPRESSED | Pub/Sub Topic for singleton channel.                                                 |
| LTA_FACEBOOK_TOPIC                            | Pub/Sub Topic for LTA S2S channel.                                                   |
| FACEBOOK_CAPI_SUBSCRIPTION_BATCH_V1           | Pub/Sub Subscription for batch channel                                               |
| FACEBOOK_CAPI_SUBSCRIPTION_SINGLETON_V1       | Pub/Sub Subscription for singleton channel.                                          |
| LTA_FACEBOOK_SUBSCRIPTION_V1                  | Pub/Sub Subscription for LTA S2S channel.                                            |
| RABBITMQ_HOST                                 | RabbitMQ host URI.                                                                   |
| REDIS_HOST                                    | Redis host URI.                                                                      |
| S2S_PIXELS_CONFIG_API                         | Target URI to get [CAPI Pixels](/fb_s2s/database#capi-pixels) from Event Config API. |
| CONFIG_API_AUTH_TOKEN                         | Authentication token for accessing Event Config API.                                 |
| CONFIG_FACEBOOK_CAPI_TTL                      | The time-to-live for each config data stores in Redis.                               |
| FACEBOOK_API_VERSION                          | The version of Facebook Conversion API, e.g. `v12.0`.                                |