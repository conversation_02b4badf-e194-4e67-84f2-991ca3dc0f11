from fastapi import status

import pytest


class EventAPIResponseOKCases(object):
    def case_frontend_token_simple(
        self,
        event_api_tokens: pytest.fixture,
        mocker: pytest.fixture,
    ):
        mocker.patch('app.routers.generic.get_system_time', return_value=1234567890)
        return (
            {
                'token': event_api_tokens['frontend_scp_all']['token'],
                'tagtoo': {
                    'permanent': 'abcdefg',
                    'language': 'zh-TW',
                    'link': 'https://www.tagtoo.com/blog',
                    'referrer': 'https://www.tagtoo.com',
                    'event': {
                        'name': 'add_to_cart',
                        'value': 300,
                        'currency': 'TWD',
                        'items': [],
                    },
                    'session': {
                        'id': 'ID1234567',
                    },
                },
            },
            {
                'permanent': 'abcdefg',
                'event_time': 1234567890,
                'language': 'zh-TW',
                'link': 'https://www.tagtoo.com/blog',
                'referrer': 'https://www.tagtoo.com',
                'event': {
                    'name': 'add_to_cart',
                    'value': 300.0,
                    'currency': 'TWD',
                    'items': [],
                    'custom_data': {},
                },
                'user': {},
                'user_agent': 'testclient',
                'ip_address': 'testclient',
                'session': {
                    'id': 'ID1234567',
                    'campaign': None,
                    'content': None,
                    'medium': None,
                    'source': None,
                    'term': None,
                },
            },
        )

    def case_backend_token_simple_without_event_time(
        self,
        event_api_tokens: pytest.fixture,
        mocker: pytest.fixture,
    ):
        mocker.patch('app.routers.generic.get_system_time', return_value=1234567890)
        return (
            {
                'token': event_api_tokens['backend_scp_tagtoo']['token'],
                'tagtoo': {
                    'permanent': 'abcdefg',
                    'language': 'zh-TW',
                    'link': 'https://www.tagtoo.com/blog',
                    'referrer': 'https://www.tagtoo.com',
                    'user_agent': 'mytestclient',
                    'ip_address': 'mytestip',
                    'event': {
                        'name': 'add_to_cart',
                        'value': 300,
                        'currency': 'TWD',
                        'items': [],
                    },
                    'session': {
                        'id': 'ID1234567',
                    },
                },
            },
            {
                'permanent': 'abcdefg',
                'event_time': 1234567890,
                'language': 'zh-TW',
                'link': 'https://www.tagtoo.com/blog',
                'referrer': 'https://www.tagtoo.com',
                'event': {
                    'name': 'add_to_cart',
                    'value': 300.0,
                    'currency': 'TWD',
                    'items': [],
                    'custom_data': {},
                },
                'user': {},
                'user_agent': 'mytestclient',
                'ip_address': 'mytestip',
                'session': {
                    'id': 'ID1234567',
                    'campaign': None,
                    'content': None,
                    'medium': None,
                    'source': None,
                    'term': None,
                },
            },
        )

    def case_backend_token_simple_with_event_time(
        self,
        event_api_tokens: pytest.fixture,
        mocker: pytest.fixture,
    ):
        mocker.patch('app.routers.generic.get_system_time', return_value=1234567890)
        return (
            {
                'token': event_api_tokens['backend_scp_tagtoo']['token'],
                'event_time': 1098765432,
                'tagtoo': {
                    'permanent': 'abcdefg',
                    'language': 'zh-TW',
                    'link': 'https://www.tagtoo.com/blog',
                    'referrer': 'https://www.tagtoo.com',
                    'user_agent': 'mytestclient',
                    'ip_address': 'mytestip',
                    'event': {
                        'name': 'add_to_cart',
                        'value': 300,
                        'currency': 'TWD',
                        'items': [],
                    },
                    'session': {
                        'id': 'ID1234567',
                    },
                },
            },
            {
                'permanent': 'abcdefg',
                'event_time': 1098765432,
                'language': 'zh-TW',
                'link': 'https://www.tagtoo.com/blog',
                'referrer': 'https://www.tagtoo.com',
                'event': {
                    'name': 'add_to_cart',
                    'value': 300.0,
                    'currency': 'TWD',
                    'items': [],
                    'custom_data': {},
                },
                'user': {},
                'user_agent': 'mytestclient',
                'ip_address': 'mytestip',
                'session': {
                    'id': 'ID1234567',
                    'campaign': None,
                    'content': None,
                    'medium': None,
                    'source': None,
                    'term': None,
                },
            },
        )


class EventAPIResponseNGCases(object):
    def case_frontend_client_validation_ng(
        self,
        event_api_tokens: pytest.fixture,
    ):
        return (
            {
                'token': event_api_tokens['frontend_scp_all']['token'],
                'tagtoo': {
                    'language': 'invalid-language-code',
                    'link': 'https://www.tagtoo.com/blog',
                    'referrer': 'https://www.tagtoo.com',
                    'user_agent': 'mytestclient',
                    'ip_address': 'mytestip',
                    'event': {
                        'name': 'add_to_cart',
                        'value': 300,
                        'currency': 'TWD',
                        'items': [],
                    },
                    'session': {
                        'id': 'ID1234567',
                    },
                },
            },
            status.HTTP_200_OK,
        )

    def case_backend_client_validation_ng(
        self,
        event_api_tokens: pytest.fixture,
    ):
        return (
            {
                'token': event_api_tokens['backend_scp_tagtoo']['token'],
                'tagtoo': {
                    'language': 'zh-TW',
                    'link': 'invalid-url-string',
                    'referrer': 'https://www.tagtoo.com',
                    'user_agent': 'myuseragent',
                    'ip_address': 'mytestip',
                    'event': {
                        'name': 'add_to_cart',
                        'value': 300,
                        'currency': 'TWD',
                        'items': [],
                    },
                    'session': {
                        'id': 'ID1234567',
                    },
                },
            },
            status.HTTP_422_UNPROCESSABLE_ENTITY,
        )

    def case_backend_client_post_validation_ng(
        self,
        event_api_tokens: pytest.fixture,
    ):
        return (
            {
                'token': event_api_tokens['backend_scp_tagtoo']['token'],
                'tagtoo': {
                    'language': 'zh-TW',
                    'link': 'https://www.tagtoo.com/blog',
                    'referrer': 'https://www.tagtoo.com',
                    # No 'user_agent' key
                    'ip_address': 'mytestip',
                    'event': {
                        'name': 'add_to_cart',
                        'value': 300,
                        'currency': 'TWD',
                        'items': [],
                    },
                    'session': {
                        'id': 'ID1234567',
                    },
                },
            },
            status.HTTP_422_UNPROCESSABLE_ENTITY,
        )

    def case_invalid_event_config_token(
        self,
        event_api_tokens: pytest.fixture,
    ):
        return (
            {
                'token': 'invalid-token',
                'tagtoo': {
                    'language': 'zh-TW',
                    'link': 'https://www.tagtoo.com/blog',
                    'referrer': 'https://www.tagtoo.com',
                    'user_agent': 'myuseragent',
                    'ip_address': 'mytestip',
                    'event': {
                        'name': 'add_to_cart',
                        'value': 300,
                        'currency': 'TWD',
                        'items': [],
                    },
                    'session': {
                        'id': 'ID1234567',
                    },
                },
            },
            status.HTTP_403_FORBIDDEN,
        )

    def case_frontend_contain_data_without_authorized_scope_01(
        self,
        event_api_tokens: pytest.fixture,
    ):
        '''
        The token did not own authorization on 'Tagtoo' while the request data
        contain 'tagtoo' data key.
        '''
        return (
            {
                'token': event_api_tokens['frontend_scp_fbcapi']['token'],
                'facebook': {
                    'user_data': {
                        'external_id': ['7fd40e78cf1df1e2e5b90f83b892db86'],
                        'client_user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 12_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.1 Mobile/15E148 Safari/604.1',
                        'fbp': 'fb.0.1616950300357.247776436',
                    },
                    'action_source': 'website',
                    'event_id': 'a2953037-d65f-4197-af22-422fbbc8a055',
                    'event_name': 'PageView',
                    'event_source_url': 'https://www.obdesign.com.tw/inpage.aspx?no=7004',
                    'data_processing_options_state': 3,
                },
                'tagtoo': {
                    'language': 'zh-TW',
                    'link': 'https://www.tagtoo.com/blog',
                    'referrer': 'https://www.tagtoo.com',
                    'user_agent': 'myuseragent',
                    'ip_address': 'mytestip',
                    'event': {
                        'name': 'add_to_cart',
                        'value': 300,
                        'currency': 'TWD',
                        'items': [],
                    },
                    'session': {
                        'id': 'ID1234567',
                    },
                },
            },
            status.HTTP_403_FORBIDDEN,
        )

    def case_frontend_contain_data_without_authorized_scope_02(
        self,
        event_api_tokens: pytest.fixture,
    ):
        '''
        The token did not own authorization on 'Facebook CAPI' while the request data
        contain 'facebook_capi' data key.
        '''
        return (
            {
                'token': event_api_tokens['frontend_scp_tagtoo']['token'],
                'facebook': {
                    'user_data': {
                        'external_id': ['7fd40e78cf1df1e2e5b90f83b892db86'],
                        'client_user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 12_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.1 Mobile/15E148 Safari/604.1',
                        'fbp': 'fb.0.1616950300357.247776436',
                    },
                    'action_source': 'website',
                    'event_id': 'a2953037-d65f-4197-af22-422fbbc8a055',
                    'event_name': 'PageView',
                    'event_source_url': 'https://www.obdesign.com.tw/inpage.aspx?no=7004',
                    'data_processing_options_state': 3,
                },
                'tagtoo': {
                    'language': 'zh-TW',
                    'link': 'https://www.tagtoo.com/blog',
                    'referrer': 'https://www.tagtoo.com',
                    'user_agent': 'myuseragent',
                    'ip_address': 'mytestip',
                    'event': {
                        'name': 'add_to_cart',
                        'value': 300,
                        'currency': 'TWD',
                        'items': [],
                    },
                    'session': {
                        'id': 'ID1234567',
                    },
                },
            },
            status.HTTP_403_FORBIDDEN,
        )

    def case_backend_contain_data_without_authorized_scope_01(
        self,
        event_api_tokens: pytest.fixture,
    ):
        '''
        The token did not own authorization on 'Tagtoo' while the request data
        contain 'tagtoo' data key.
        '''
        return (
            {
                'token': event_api_tokens['backend_scp_fbcapi']['token'],
                'facebook': {
                    'user_data': {
                        'external_id': ['7fd40e78cf1df1e2e5b90f83b892db86'],
                        'client_user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 12_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.1 Mobile/15E148 Safari/604.1',
                        'fbp': 'fb.0.1616950300357.247776436',
                    },
                    'action_source': 'website',
                    'event_id': 'a2953037-d65f-4197-af22-422fbbc8a055',
                    'event_name': 'PageView',
                    'event_source_url': 'https://www.obdesign.com.tw/inpage.aspx?no=7004',
                    'data_processing_options_state': 3,
                },
                'tagtoo': {
                    'language': 'zh-TW',
                    'link': 'https://www.tagtoo.com/blog',
                    'referrer': 'https://www.tagtoo.com',
                    'user_agent': 'myuseragent',
                    'ip_address': 'mytestip',
                    'event': {
                        'name': 'add_to_cart',
                        'value': 300,
                        'currency': 'TWD',
                        'items': [],
                    },
                    'session': {
                        'id': 'ID1234567',
                    },
                },
            },
            status.HTTP_403_FORBIDDEN,
        )

    def case_backend_contain_data_without_authorized_scope_02(
        self,
        event_api_tokens: pytest.fixture,
    ):
        '''
        The token did not own authorization on 'Facebook CAPI' while the request data
        contain 'facebook_capi' data key.
        '''
        return (
            {
                'token': event_api_tokens['backend_scp_tagtoo']['token'],
                'facebook': {
                    'user_data': {
                        'external_id': ['7fd40e78cf1df1e2e5b90f83b892db86'],
                        'client_user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 12_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.1 Mobile/15E148 Safari/604.1',
                        'fbp': 'fb.0.1616950300357.247776436',
                    },
                    'action_source': 'website',
                    'event_id': 'a2953037-d65f-4197-af22-422fbbc8a055',
                    'event_name': 'PageView',
                    'event_source_url': 'https://www.obdesign.com.tw/inpage.aspx?no=7004',
                    'data_processing_options_state': 3,
                },
                'tagtoo': {
                    'language': 'zh-TW',
                    'link': 'https://www.tagtoo.com/blog',
                    'referrer': 'https://www.tagtoo.com',
                    'user_agent': 'myuseragent',
                    'ip_address': 'mytestip',
                    'event': {
                        'name': 'add_to_cart',
                        'value': 300,
                        'currency': 'TWD',
                        'items': [],
                    },
                    'session': {
                        'id': 'ID1234567',
                    },
                },
            },
            status.HTTP_403_FORBIDDEN,
        )


class EventAPIPartialValidCases(object):
    '''
    Test the cases that data validation is failed at certain parts of the data.
    '''

    def case_top_level_invalid(
        self,
        event_api_tokens: pytest.fixture,
    ):
        '''
        Top-level field `event_time` is not in valid format.
        Expect:
        - A validation error report should be published
        - No event data will be published
        '''
        return (
            # Input data
            {
                'event_time': 'bacdefb',
                'token': event_api_tokens['frontend_scp_all']['token'],
                'facebook': {
                    'user_data': {
                        'external_id': ['7fd40e78cf1df1e2e5b90f83b892db86'],
                        'client_user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 12_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.1 Mobile/15E148 Safari/604.1',
                        'fbp': 'fb.0.1616950300357.247776436',
                    },
                    'action_source': 'website',
                    'event_id': 'a2953037-d65f-4197-af22-422fbbc8a055',
                    'event_name': 'PageView',
                    'event_source_url': 'https://www.obdesign.com.tw/inpage.aspx?no=7004',
                    'data_processing_options_state': 3,
                },
                'tagtoo': {
                    'permanent': None,
                    'language': 'zh-TW',
                    'link': 'https://www.tagtoo.com/blog',
                    'referrer': 'https://www.tagtoo.com',
                    'user_agent': 'myuseragent',
                    'ip_address': 'mytestip',
                    'event': {
                        'name': 'add_to_cart',
                        'value': 300,
                        'currency': 'TWD',
                        'items': [],
                    },
                    'session': {
                        'id': 'ID1234567',
                    },
                },
            },
            # Expected
            {
                'facebook-capi': None,
                'tagtoo-event': None,
            },
        )

    def case_a_part_of_scope_data_invalid_01(
        self,
        event_api_tokens: pytest.fixture,
    ):
        '''
        FB-CAPI field `data_processing_options_state` is not in valid format.
        Expect:
        - A validation error report should be published
        - Tagtoo event data should should be published
        '''
        return (
            # Input data
            {
                'event_time': 123456,
                'token': event_api_tokens['frontend_scp_all']['token'],
                'facebook': {
                    'user_data': {
                        'external_id': ['7fd40e78cf1df1e2e5b90f83b892db86'],
                        'client_user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 12_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.1 Mobile/15E148 Safari/604.1',
                        'fbp': 'fb.0.1616950300357.247776436',
                    },
                    'action_source': 'website',
                    'event_id': 'a2953037-d65f-4197-af22-422fbbc8a055',
                    'event_name': 'PageView',
                    'event_source_url': 'https://www.obdesign.com.tw/inpage.aspx?no=7004',
                    'data_processing_options_state': 'aaaa',  # Invalid data
                },
                'tagtoo': {
                    'permanent': 'abcdef',
                    'language': 'zh-TW',
                    'link': 'https://www.tagtoo.com/blog',
                    'referrer': 'https://www.tagtoo.com',
                    # 'user_agent': 'myuseragent',
                    # 'ip_address': 'mytestip',
                    'event': {
                        'name': 'add_to_cart',
                        'value': 300,
                        'currency': 'TWD',
                        'items': [],
                    },
                    'session': {
                        'id': 'ID1234567',
                    },
                },
            },
            # Expected
            {
                'facebook-capi': None,
                'tagtoo-event': {
                    'permanent': 'abcdef',
                    'language': 'zh-TW',
                    'link': 'https://www.tagtoo.com/blog',
                    'referrer': 'https://www.tagtoo.com',
                    'user_agent': 'testclient',
                    'ip_address': 'testclient',
                    'event_time': 123456,
                    'event': {
                        'name': 'add_to_cart',
                        'value': 300.0,
                        'currency': 'TWD',
                        'items': [],
                        'custom_data': {},
                    },
                    'session': {
                        'id': 'ID1234567',
                        'campaign': None,
                        'content': None,
                        'medium': None,
                        'source': None,
                        'term': None,
                    },
                    'user': {},
                },
            },
        )

    def case_a_part_of_scope_data_invalid_02(
        self, event_api_tokens: pytest.fixture, mocker: pytest.fixture
    ):
        '''
        TAGTOO field `items[0].value` is not in valid format.
        Expect:
        - A validation error report should be published
        - Facebook-CAPI event data should should be published
        '''
        mocker.patch('app.routers.generic.get_system_time', return_value=1234567890)

        return (
            # Input data
            {
                'event_time': None,
                'token': event_api_tokens['frontend_scp_all']['token'],
                'facebook': {
                    'user_data': {
                        'external_id': ['7fd40e78cf1df1e2e5b90f83b892db86'],
                        'client_user_agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 12_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.1 Mobile/15E148 Safari/604.1',
                        'fbp': 'fb.0.1616950300357.247776436',
                    },
                    'action_source': 'website',
                    'event_id': 'a2953037-d65f-4197-af22-422fbbc8a055',
                    'event_name': 'PageView',
                    'event_source_url': 'https://www.obdesign.com.tw/inpage.aspx?no=7004',
                    'data_processing_options_state': 3,
                },
                'tagtoo': {
                    'language': 'zh-TW',
                    'link': 'https://www.tagtoo.com/blog',
                    'referrer': 'https://www.tagtoo.com',
                    # 'user_agent': 'myuseragent',
                    # 'ip_address': 'mytestip',
                    'event': {
                        'name': 'add_to_cart',
                        'value': 'invalid_value',  # Invalid field
                        'currency': 'TWD',
                        'items': [],
                    },
                    'session': {
                        'id': 'ID1234567',
                    },
                },
            },
            # Expected
            {
                'facebook-capi': {
                    'event_time': 1234567890,
                    'user_data': {
                        'external_id': ['7fd40e78cf1df1e2e5b90f83b892db86'],
                        'country': None,
                        'client_ip_address': 'testclient',
                        'client_user_agent': 'testclient',
                        'fbp': 'fb.0.1616950300357.247776436',
                        'fbc': None,
                        'fb_login_id': None,
                        'ct': None,
                        'db': None,
                        'em': None,
                        'fn': None,
                        'ge': None,
                        'lead_id': None,
                        'ln': None,
                        'ph': None,
                        'st': None,
                        'subscription_id': None,
                        'zp': None,
                    },
                    'action_source': 'website',
                    'event_id': 'a2953037-d65f-4197-af22-422fbbc8a055',
                    'event_name': 'PageView',
                    'event_source_url': 'https://www.obdesign.com.tw/inpage.aspx?no=7004',
                    'custom_data': None,
                    'data_processing_options': None,
                    'data_processing_options_country': None,
                    'data_processing_options_state': 3,
                },
                'tagtoo-event': None,
            },
        )
